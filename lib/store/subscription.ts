import { SubscriptionData } from '@/lib/constants/subscription';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface SubscriptionState {
  // Subscription data
  subscriptionData: Partial<SubscriptionData>;
  subscriberId: string | null;

  // Checkout state
  isCheckingOut: boolean;
  paymentError: string | null;
  paymentSuccess: boolean;
  paymentData: any | null;

  // Actions
  updateSubscriptionData: (updates: Partial<SubscriptionData>) => void;
  setSubscriberId: (id: string) => void;
  setCheckingOut: (isCheckingOut: boolean) => void;
  setPaymentError: (error: string | null) => void;
  setPaymentSuccess: (success: boolean, data?: any) => void;
  resetSubscription: () => void;

  // Validation
  canProceedToCheckout: () => boolean;
}

const initialSubscriptionData: Partial<SubscriptionData> = {
  deliveryType: 'pickup', // Default to pickup (only option)
  pickupLocation: 'elite_bodies', // Default to first location
  shareSize: 'standard', // Default to the single share option
};

export const useSubscriptionStore = create<SubscriptionState>()(
  persist(
    (set, get) => ({
      // Initial state
      subscriptionData: initialSubscriptionData,
      subscriberId: null,
      isCheckingOut: false,
      paymentError: null,
      paymentSuccess: false,
      paymentData: null,

      // Actions
      updateSubscriptionData: (updates) =>
        set((state) => ({
          subscriptionData: { ...state.subscriptionData, ...updates },
        })),

      setSubscriberId: (id) =>
        set(() => ({
          subscriberId: id,
        })),

      setCheckingOut: (isCheckingOut) =>
        set(() => ({
          isCheckingOut,
        })),

      setPaymentError: (error) =>
        set(() => ({
          paymentError: error,
          paymentSuccess: false,
        })),

      setPaymentSuccess: (success, data = null) =>
        set(() => ({
          paymentSuccess: success,
          paymentData: data,
          paymentError: null,
        })),

      resetSubscription: () =>
        set(() => ({
          subscriptionData: initialSubscriptionData,
          subscriberId: null,
          isCheckingOut: false,
          paymentError: null,
          paymentSuccess: false,
          paymentData: null,
        })),

      // Validation
      canProceedToCheckout: () => {
        const { subscriptionData, subscriberId } = get();
        return !!(
          (subscriptionData.shareSize || subscriptionData.boxSize) &&
          subscriptionData.frequency &&
          subscriptionData.paymentPlan &&
          subscriptionData.deliveryType &&
          subscriptionData.pickupLocation &&
          subscriberId // Must have subscriber ID to proceed to checkout
        );
      },
    }),
    {
      name: 'subscription-store',
      // Only persist the subscription data, not the checkout state
      partialize: (state) => ({
        subscriptionData: state.subscriptionData,
        subscriberId: state.subscriberId,
      }),
    }
  )
);
