-- Enable uuid-ossp extension for UUID generation
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Subscribers table: Stores customer information, linked to auth.users
CREATE TABLE subscribers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID UNIQUE REFERENCES auth.users(id) ON DELETE CASCADE, -- Link to Supabase Auth
    name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    phone TEXT,
    address TEXT NOT NULL,
    special_instructions TEXT,
    allergies TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Enable RLS for subscribers
ALTER TABLE subscribers ENABLE ROW LEVEL SECURITY;
CREATE POLICY subscribers_access ON subscribers
    FOR ALL
    USING (auth.uid() = user_id); -- Restrict to authenticated user

-- Subscriptions table: Stores subscription details
CREATE TABLE subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    subscriber_id UUID REFERENCES subscribers(id) ON DELETE CASCADE,
    box_size TEXT NOT NULL CHECK (box_size IN ('Small', 'Medium', 'Large')),
    frequency TEXT NOT NULL CHECK (frequency IN ('Weekly', 'Biweekly')),
    delivery_type TEXT NOT NULL CHECK (delivery_type IN ('Delivery', 'Pickup')),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Enable RLS for subscriptions
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;
CREATE POLICY subscriptions_access ON subscriptions
    FOR ALL
    USING (EXISTS (
        SELECT 1 FROM subscribers
        WHERE subscribers.id = subscriptions.subscriber_id
        AND subscribers.user_id = auth.uid()
    )); -- Restrict to authenticated user's subscriptions

-- Payments table: Tracks payment status
CREATE TABLE payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    subscription_id UUID REFERENCES subscriptions(id) ON DELETE CASCADE,
    payment_provider TEXT NOT NULL, -- 'Stripe' or 'PayPal'
    payment_id TEXT NOT NULL, -- External ID from payment provider
    amount DECIMAL(10, 2) NOT NULL,
    status TEXT NOT NULL, -- e.g., 'succeeded', 'pending', 'failed'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Enable RLS for payments
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
CREATE POLICY payments_access ON payments
    FOR ALL
    USING (EXISTS (
        SELECT 1 FROM subscriptions
        JOIN subscribers ON subscribers.id = subscriptions.subscriber_id
        WHERE subscriptions.id = payments.subscription_id
        AND subscribers.user_id = auth.uid()
    )); -- Restrict to authenticated user's payments

-- Box Contents table: Stores weekly box contents
CREATE TABLE box_contents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    week_start_date DATE NOT NULL,
    contents TEXT NOT NULL, -- Description of box contents (e.g., JSON or plain text)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Enable RLS for box_contents
ALTER TABLE box_contents ENABLE ROW LEVEL SECURITY;
CREATE POLICY box_contents_access ON box_contents
    FOR SELECT
    USING (true); -- Public read access for weekly contents

-- Pause Requests table: Tracks pause/skip requests
CREATE TABLE pause_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    subscription_id UUID REFERENCES subscriptions(id) ON DELETE CASCADE,
    pause_start_date DATE NOT NULL,
    pause_end_date DATE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Enable RLS for pause_requests
ALTER TABLE pause_requests ENABLE ROW LEVEL SECURITY;
CREATE POLICY pause_requests_access ON pause_requests
    FOR ALL
    USING (EXISTS (
        SELECT 1 FROM subscriptions
        JOIN subscribers ON subscribers.id = subscriptions.subscriber_id
        WHERE subscriptions.id = pause_requests.subscription_id
        AND subscribers.user_id = auth.uid()
    )); -- Restrict to authenticated user's pause requests

-- Contact form submissions table: Stores contact form data
CREATE TABLE contact_submissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    email TEXT NOT NULL,
    message TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Enable RLS for contact_submissions (admin access only)
ALTER TABLE contact_submissions ENABLE ROW LEVEL SECURITY;
CREATE POLICY contact_submissions_admin_access ON contact_submissions
    FOR ALL
    USING (false); -- No public access, admin only through service role

-- Newsletter subscriptions table: Stores newsletter email addresses
CREATE TABLE newsletter_subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email TEXT UNIQUE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Enable RLS for newsletter_subscriptions (admin access only)
ALTER TABLE newsletter_subscriptions ENABLE ROW LEVEL SECURITY;
CREATE POLICY newsletter_subscriptions_admin_access ON newsletter_subscriptions
    FOR ALL
    USING (false); -- No public access, admin only through service role

-- Produce items table: Stores available produce items
CREATE TABLE produce_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT UNIQUE NOT NULL,
    category TEXT NOT NULL CHECK (category IN ('vegetables', 'fruits', 'herbs', 'greens')),
    is_common BOOLEAN DEFAULT FALSE, -- Common items that appear frequently
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Enable RLS for produce_items (public read access)
ALTER TABLE produce_items ENABLE ROW LEVEL SECURITY;
CREATE POLICY produce_items_read_access ON produce_items
    FOR SELECT
    USING (true); -- Public read access

-- User produce preferences table: Stores "never send" items for each user
CREATE TABLE user_produce_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    produce_item_id UUID REFERENCES produce_items(id) ON DELETE CASCADE,
    preference_type TEXT NOT NULL CHECK (preference_type IN ('never_send', 'preferred')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, produce_item_id, preference_type)
);

-- Enable RLS for user_produce_preferences
ALTER TABLE user_produce_preferences ENABLE ROW LEVEL SECURITY;
CREATE POLICY user_produce_preferences_access ON user_produce_preferences
    FOR ALL
    USING (auth.uid() = user_id); -- Restrict to authenticated user

-- Create indexes for better performance
CREATE INDEX idx_subscribers_user_id ON subscribers(user_id);
CREATE INDEX idx_subscribers_email ON subscribers(email);
CREATE INDEX idx_subscriptions_subscriber_id ON subscriptions(subscriber_id);
CREATE INDEX idx_subscriptions_is_active ON subscriptions(is_active);
CREATE INDEX idx_payments_subscription_id ON payments(subscription_id);
CREATE INDEX idx_payments_status ON payments(status);
CREATE INDEX idx_box_contents_week_start_date ON box_contents(week_start_date);
CREATE INDEX idx_pause_requests_subscription_id ON pause_requests(subscription_id);
CREATE INDEX idx_contact_submissions_created_at ON contact_submissions(created_at);
CREATE INDEX idx_newsletter_subscriptions_email ON newsletter_subscriptions(email);
CREATE INDEX idx_newsletter_subscriptions_is_active ON newsletter_subscriptions(is_active);
