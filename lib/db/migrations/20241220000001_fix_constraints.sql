-- Fix database constraints to match application constants

-- Drop existing check constraints
ALTER TABLE subscriptions DROP CONSTRAINT IF EXISTS subscriptions_box_size_check;
ALTER TABLE subscriptions DROP CONSTRAINT IF EXISTS subscriptions_frequency_check;
ALTER TABLE subscriptions DROP CONSTRAINT IF EXISTS subscriptions_delivery_type_check;

-- Add new check constraints that match application constants
ALTER TABLE subscriptions 
ADD CONSTRAINT subscriptions_box_size_check 
CHECK (box_size IN ('small', 'medium', 'large'));

ALTER TABLE subscriptions 
ADD CONSTRAINT subscriptions_frequency_check 
CHECK (frequency IN ('weekly', 'biweekly', 'monthly'));

ALTER TABLE subscriptions 
ADD CONSTRAINT subscriptions_delivery_type_check 
CHECK (delivery_type IN ('pickup'));

-- Update existing data to match new constraints (if any exists)
UPDATE subscriptions SET box_size = LOWER(box_size) WHERE box_size IN ('Small', 'Medium', 'Large');
UPDATE subscriptions SET frequency = LOWER(frequency) WHERE frequency IN ('Weekly', 'Biweekly', 'Monthly');
UPDATE subscriptions SET delivery_type = LOWER(delivery_type) WHERE delivery_type IN ('Delivery', 'Pickup');
