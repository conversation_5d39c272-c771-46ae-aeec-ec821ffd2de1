-- Add missing columns to subscriptions table for enhanced subscription model
-- This migration adds all the columns needed for the subscription management features

-- Add payment_plan column
ALTER TABLE subscriptions
ADD COLUMN IF NOT EXISTS payment_plan TEXT;

-- Add discount_percentage column
ALTER TABLE subscriptions
ADD COLUMN IF NOT EXISTS discount_percentage DECIMAL(5,2) DEFAULT 0;

-- Add deliveries_remaining column
ALTER TABLE subscriptions
ADD COLUMN IF NOT EXISTS deliveries_remaining INTEGER;

-- Add auto_renew column
ALTER TABLE subscriptions
ADD COLUMN IF NOT EXISTS auto_renew BOOLEAN DEFAULT true;

-- Add next_delivery_date column
ALTER TABLE subscriptions
ADD COLUMN IF NOT EXISTS next_delivery_date DATE;

-- Add status column
ALTER TABLE subscriptions
ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'active';

-- Add base_price_cents column
ALTER TABLE subscriptions
ADD COLUMN IF NOT EXISTS base_price_cents INTEGER;

-- Add check constraints for new columns (drop first if exists)
ALTER TABLE subscriptions
DROP CONSTRAINT IF EXISTS subscriptions_payment_plan_check;

ALTER TABLE subscriptions
ADD CONSTRAINT subscriptions_payment_plan_check
CHECK (payment_plan IN ('4_deliveries', '12_deliveries', '24_deliveries'));

ALTER TABLE subscriptions
DROP CONSTRAINT IF EXISTS subscriptions_status_check;

ALTER TABLE subscriptions
ADD CONSTRAINT subscriptions_status_check
CHECK (status IN ('active', 'paused', 'cancelled', 'completed'));

-- Update existing subscriptions with default values
UPDATE subscriptions
SET
  payment_plan = '4_deliveries',
  discount_percentage = 0,
  deliveries_remaining = 4,
  auto_renew = true,
  status = 'active',
  base_price_cents = 2500, -- Default $25.00 for small box
  next_delivery_date = CURRENT_DATE + INTERVAL '7 days'
WHERE payment_plan IS NULL;

-- Create indexes for better performance on new columns
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_subscriptions_next_delivery_date ON subscriptions(next_delivery_date);
CREATE INDEX IF NOT EXISTS idx_subscriptions_auto_renew ON subscriptions(auto_renew);
CREATE INDEX IF NOT EXISTS idx_subscriptions_payment_plan ON subscriptions(payment_plan);
