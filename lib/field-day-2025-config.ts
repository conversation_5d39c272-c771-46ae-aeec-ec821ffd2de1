import { z } from 'zod';

// Field Day 2025 Product Configuration
export interface FieldDay2025Product {
  id: string;
  name: string;
  description: string;
  specifications: string[];
  unitPriceCents: number;
  unitPrice: number;
  images: ProductImage[];
  maxQuantity: number;
  minQuantity: number;
  isAvailable: boolean;
}

export interface ProductImage {
  url: string;
  alt: string;
  width: number;
  height: number;
  isPrimary: boolean;
}

export interface PickupLocation {
  id: string;
  name: string;
  address: string;
  // hours: string;
  contactInfo?: string;
}

// Main product configuration
export const FIELD_DAY_2025_PRODUCT: FieldDay2025Product = {
  id: 'field-day-2025-standard-box',
  name: 'Field Day 2025 - Standard Box',
  description:
    'Premium seasonal produce box specially curated for Field Day 2025. Perfect for families and groups looking to enjoy fresh, locally-sourced vegetables and fruits.',
  specifications: [
    'seasonal items',
    'Locally sourced from partner farms',
    'Organic and sustainably grown',
    'Recipe suggestions included',
    'Feeds 3-4 people',
    'Pickup only - no delivery available',
  ],
  // unitPriceCents: 3599, // $35.99
  unitPriceCents: 2499, // $35.99
  unitPrice: 24.99,
  // unitPrice: 35.99,
  images: [
    // {
    //   url: '/img/diverse-local-vendors-preparing-healthy-market-counter.jpg',
    //   alt: 'Field Day 2025 Medium Box - Fresh seasonal produce from local vendors',
    //   width: 800,
    //   height: 600,
    //   isPrimary: true,
    // },
    {
      url: '/img/african-man-harvesting-vegetables.jpg',
      alt: 'Fresh vegetables being harvested from local partner farms',
      width: 800,
      height: 600,
      isPrimary: false,
    },
    {
      url: '/img/womanfarmer.jpg',
      alt: 'Happy customer with Field Day 2025 produce box order',
      width: 800,
      height: 600,
      isPrimary: false,
    },
  ],
  maxQuantity: 10,
  minQuantity: 1,
  isAvailable: true,
};

// Tax configuration
export const TAX_RATE = 0.0; // No tax for now, can be updated

// Pickup locations
export const PICKUP_LOCATIONS: PickupLocation[] = [
  {
    id: 'north_four_corners_local_park',
    name: 'North Four Corners Local Park',
    address:
      'Parking lot, 315 University Blvd W, Silver Spring, MD 20901, United States',
    // hours: 'Monday-Friday: 6AM-8PM, Saturday: 8AM-6PM, Sunday: Closed',
    contactInfo: 'Phone: (*************',
  },
];

// Order status enums
export const ORDER_STATUSES = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  READY: 'ready',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
} as const;

export const PAYMENT_STATUSES = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  SUCCEEDED: 'succeeded',
  FAILED: 'failed',
  CANCELED: 'canceled',
  REQUIRES_ACTION: 'requires_action',
} as const;

export const PICKUP_STATUSES = {
  NOT_READY: 'not_ready',
  READY: 'ready',
  PICKED_UP: 'picked_up',
} as const;

export type OrderStatus = (typeof ORDER_STATUSES)[keyof typeof ORDER_STATUSES];
export type PaymentStatus =
  (typeof PAYMENT_STATUSES)[keyof typeof PAYMENT_STATUSES];
export type PickupStatus =
  (typeof PICKUP_STATUSES)[keyof typeof PICKUP_STATUSES];

// Validation schemas
export const fieldDay2025OrderSchema = z.object({
  quantity: z
    .number()
    .min(
      FIELD_DAY_2025_PRODUCT.minQuantity,
      `Minimum quantity is ${FIELD_DAY_2025_PRODUCT.minQuantity}`
    )
    .max(
      FIELD_DAY_2025_PRODUCT.maxQuantity,
      `Maximum quantity is ${FIELD_DAY_2025_PRODUCT.maxQuantity}`
    ),
  firstName: z
    .string()
    .min(1, 'First name is required')
    .max(50, 'First name must be less than 50 characters')
    .regex(
      /^[a-zA-Z\s'-]+$/,
      'First name can only contain letters, spaces, hyphens, and apostrophes'
    ),
  lastName: z
    .string()
    .min(1, 'Last name is required')
    .max(50, 'Last name must be less than 50 characters')
    .regex(
      /^[a-zA-Z\s'-]+$/,
      'Last name can only contain letters, spaces, hyphens, and apostrophes'
    ),
  email: z
    .string()
    .email('Please enter a valid email address')
    .max(100, 'Email must be less than 100 characters'),
  phoneNumber: z
    .string()
    .min(10, 'Phone number must be at least 10 digits')
    .max(20, 'Phone number must be less than 20 characters')
    .regex(/^[\d\s\-\(\)\+\.]+$/, 'Please enter a valid phone number'),
});

export type FieldDay2025OrderData = z.infer<typeof fieldDay2025OrderSchema>;

// Admin update schemas
export const adminOrderUpdateSchema = z.object({
  orderStatus: z
    .enum([
      ORDER_STATUSES.PENDING,
      ORDER_STATUSES.CONFIRMED,
      ORDER_STATUSES.READY,
      ORDER_STATUSES.COMPLETED,
      ORDER_STATUSES.CANCELLED,
    ])
    .optional(),
  pickupStatus: z
    .enum([
      PICKUP_STATUSES.NOT_READY,
      PICKUP_STATUSES.READY,
      PICKUP_STATUSES.PICKED_UP,
    ])
    .optional(),
  paymentStatus: z
    .enum([
      PAYMENT_STATUSES.PENDING,
      PAYMENT_STATUSES.PROCESSING,
      PAYMENT_STATUSES.SUCCEEDED,
      PAYMENT_STATUSES.FAILED,
      PAYMENT_STATUSES.CANCELED,
      PAYMENT_STATUSES.REQUIRES_ACTION,
    ])
    .optional(),
  paymentMethod: z.string().optional(),
  paymentTimestamp: z.string().optional(),
  paymentIntentId: z.string().optional(),
  adminNotes: z
    .string()
    .max(1000, 'Admin notes must be less than 1000 characters')
    .optional(),
});

export type AdminOrderUpdateData = z.infer<typeof adminOrderUpdateSchema>;

// Helper functions
export function calculateOrderTotal(quantity: number): {
  subtotalCents: number;
  taxAmountCents: number;
  totalAmountCents: number;
  subtotal: number;
  taxAmount: number;
  total: number;
} {
  const subtotalCents = FIELD_DAY_2025_PRODUCT.unitPriceCents * quantity;
  const taxAmountCents = Math.round(subtotalCents * TAX_RATE);
  const totalAmountCents = subtotalCents + taxAmountCents;

  return {
    subtotalCents,
    taxAmountCents,
    totalAmountCents,
    subtotal: subtotalCents / 100,
    taxAmount: taxAmountCents / 100,
    total: totalAmountCents / 100,
  };
}

export function formatCurrency(cents: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(cents / 100);
}

export function formatOrderNumber(orderNumber: string): string {
  return orderNumber.toUpperCase();
}

export function getOrderStatusDisplay(status: OrderStatus): {
  label: string;
  color: 'default' | 'secondary' | 'destructive' | 'outline';
} {
  switch (status) {
    case ORDER_STATUSES.PENDING:
      return { label: 'Pending', color: 'outline' };
    case ORDER_STATUSES.CONFIRMED:
      return { label: 'Confirmed', color: 'secondary' };
    case ORDER_STATUSES.READY:
      return { label: 'Ready for Pickup', color: 'default' };
    case ORDER_STATUSES.COMPLETED:
      return { label: 'Completed', color: 'default' };
    case ORDER_STATUSES.CANCELLED:
      return { label: 'Cancelled', color: 'destructive' };
    default:
      return { label: 'Unknown', color: 'outline' };
  }
}

export function getPaymentStatusDisplay(status: PaymentStatus): {
  label: string;
  color: 'default' | 'secondary' | 'destructive' | 'outline';
} {
  switch (status) {
    case PAYMENT_STATUSES.PENDING:
      return { label: 'Pending', color: 'outline' };
    case PAYMENT_STATUSES.PROCESSING:
      return { label: 'Processing', color: 'secondary' };
    case PAYMENT_STATUSES.SUCCEEDED:
      return { label: 'Paid', color: 'default' };
    case PAYMENT_STATUSES.FAILED:
      return { label: 'Failed', color: 'destructive' };
    case PAYMENT_STATUSES.CANCELED:
      return { label: 'Cancelled', color: 'destructive' };
    case PAYMENT_STATUSES.REQUIRES_ACTION:
      return { label: 'Action Required', color: 'outline' };
    default:
      return { label: 'Unknown', color: 'outline' };
  }
}

export function getPickupStatusDisplay(status: PickupStatus): {
  label: string;
  color: 'default' | 'secondary' | 'destructive' | 'outline';
} {
  switch (status) {
    case PICKUP_STATUSES.NOT_READY:
      return { label: 'Not Ready', color: 'outline' };
    case PICKUP_STATUSES.READY:
      return { label: 'Ready for Pickup', color: 'default' };
    case PICKUP_STATUSES.PICKED_UP:
      return { label: 'Picked Up', color: 'secondary' };
    default:
      return { label: 'Unknown', color: 'outline' };
  }
}

// Event configuration
export const FIELD_DAY_2025_EVENT = {
  name: 'Field Day 2025',
  date: '2025-06-15', // Update with actual event date
  description:
    'Join us for an amazing day of community, fresh food, and fun activities!',
  website: 'https://fieldday2025.com', // Update with actual website
  socialMedia: {
    facebook: 'https://facebook.com/fieldday2025',
    instagram: 'https://instagram.com/fieldday2025',
    twitter: 'https://twitter.com/fieldday2025',
  },
} as const;
