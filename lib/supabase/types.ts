export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  public: {
    Tables: {
      box_contents: {
        Row: {
          contents: string;
          created_at: string | null;
          id: string;
          week_start_date: string;
        };
        Insert: {
          contents: string;
          created_at?: string | null;
          id?: string;
          week_start_date: string;
        };
        Update: {
          contents?: string;
          created_at?: string | null;
          id?: string;
          week_start_date?: string;
        };
        Relationships: [];
      };
      contact_submissions: {
        Row: {
          created_at: string | null;
          email: string;
          id: string;
          message: string;
          name: string;
        };
        Insert: {
          created_at?: string | null;
          email: string;
          id?: string;
          message: string;
          name: string;
        };
        Update: {
          created_at?: string | null;
          email?: string;
          id?: string;
          message?: string;
          name?: string;
        };
        Relationships: [];
      };
      deliveries: {
        Row: {
          box_contents: string | null;
          created_at: string | null;
          delivery_date: string;
          id: string;
          pickup_location: string;
          special_instructions: string | null;
          status: string;
          subscription_id: string | null;
          updated_at: string | null;
        };
        Insert: {
          box_contents?: string | null;
          created_at?: string | null;
          delivery_date: string;
          id?: string;
          pickup_location: string;
          special_instructions?: string | null;
          status: string;
          subscription_id?: string | null;
          updated_at?: string | null;
        };
        Update: {
          box_contents?: string | null;
          created_at?: string | null;
          delivery_date?: string;
          id?: string;
          pickup_location?: string;
          special_instructions?: string | null;
          status?: string;
          subscription_id?: string | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'deliveries_subscription_id_fkey';
            columns: ['subscription_id'];
            isOneToOne: false;
            referencedRelation: 'subscriptions';
            referencedColumns: ['id'];
          },
        ];
      };
      field_day_2025_orders: {
        Row: {
          admin_notes: string | null;
          created_at: string | null;
          email: string;
          first_name: string;
          id: string;
          last_name: string;
          order_number: string;
          order_status: string;
          payment_intent_id: string | null;
          payment_method: string | null;
          payment_status: string;
          payment_timestamp: string | null;
          phone_number: string;
          pickup_status: string;
          product_name: string;
          quantity: number;
          tax_amount_cents: number | null;
          total_amount_cents: number;
          unit_price_cents: number;
          updated_at: string | null;
        };
        Insert: {
          admin_notes?: string | null;
          created_at?: string | null;
          email: string;
          first_name: string;
          id?: string;
          last_name: string;
          order_number: string;
          order_status?: string;
          payment_intent_id?: string | null;
          payment_method?: string | null;
          payment_status?: string;
          payment_timestamp?: string | null;
          phone_number: string;
          pickup_status?: string;
          product_name?: string;
          quantity: number;
          tax_amount_cents?: number | null;
          total_amount_cents: number;
          unit_price_cents: number;
          updated_at?: string | null;
        };
        Update: {
          admin_notes?: string | null;
          created_at?: string | null;
          email?: string;
          first_name?: string;
          id?: string;
          last_name?: string;
          order_number?: string;
          order_status?: string;
          payment_intent_id?: string | null;
          payment_method?: string | null;
          payment_status?: string;
          payment_timestamp?: string | null;
          phone_number?: string;
          pickup_status?: string;
          product_name?: string;
          quantity?: number;
          tax_amount_cents?: number | null;
          total_amount_cents?: number;
          unit_price_cents?: number;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      newsletter_subscriptions: {
        Row: {
          created_at: string | null;
          email: string;
          id: string;
          is_active: boolean | null;
        };
        Insert: {
          created_at?: string | null;
          email: string;
          id?: string;
          is_active?: boolean | null;
        };
        Update: {
          created_at?: string | null;
          email?: string;
          id?: string;
          is_active?: boolean | null;
        };
        Relationships: [];
      };
      pause_requests: {
        Row: {
          created_at: string | null;
          id: string;
          pause_end_date: string;
          pause_start_date: string;
          subscription_id: string | null;
        };
        Insert: {
          created_at?: string | null;
          id?: string;
          pause_end_date: string;
          pause_start_date: string;
          subscription_id?: string | null;
        };
        Update: {
          created_at?: string | null;
          id?: string;
          pause_end_date?: string;
          pause_start_date?: string;
          subscription_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'pause_requests_subscription_id_fkey';
            columns: ['subscription_id'];
            isOneToOne: false;
            referencedRelation: 'subscriptions';
            referencedColumns: ['id'];
          },
        ];
      };
      payments: {
        Row: {
          amount: number;
          created_at: string | null;
          id: string;
          payment_id: string;
          payment_provider: string;
          status: string;
          subscription_id: string | null;
        };
        Insert: {
          amount: number;
          created_at?: string | null;
          id?: string;
          payment_id: string;
          payment_provider: string;
          status: string;
          subscription_id?: string | null;
        };
        Update: {
          amount?: number;
          created_at?: string | null;
          id?: string;
          payment_id?: string;
          payment_provider?: string;
          status?: string;
          subscription_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'payments_subscription_id_fkey';
            columns: ['subscription_id'];
            isOneToOne: false;
            referencedRelation: 'subscriptions';
            referencedColumns: ['id'];
          },
        ];
      };
      produce_items: {
        Row: {
          category: string;
          created_at: string | null;
          id: string;
          is_common: boolean | null;
          name: string;
        };
        Insert: {
          category: string;
          created_at?: string | null;
          id?: string;
          is_common?: boolean | null;
          name: string;
        };
        Update: {
          category?: string;
          created_at?: string | null;
          id?: string;
          is_common?: boolean | null;
          name?: string;
        };
        Relationships: [];
      };
      subscribers: {
        Row: {
          address: string;
          allergies: string | null;
          created_at: string | null;
          email: string;
          id: string;
          name: string;
          phone: string | null;
          role: Database['public']['Enums']['user_role'] | null;
          special_instructions: string | null;
          user_id: string | null;
        };
        Insert: {
          address: string;
          allergies?: string | null;
          created_at?: string | null;
          email: string;
          id?: string;
          name: string;
          phone?: string | null;
          role?: Database['public']['Enums']['user_role'] | null;
          special_instructions?: string | null;
          user_id?: string | null;
        };
        Update: {
          address?: string;
          allergies?: string | null;
          created_at?: string | null;
          email?: string;
          id?: string;
          name?: string;
          phone?: string | null;
          role?: Database['public']['Enums']['user_role'] | null;
          special_instructions?: string | null;
          user_id?: string | null;
        };
        Relationships: [];
      };
      subscriptions: {
        Row: {
          auto_renew: boolean | null;
          base_price_cents: number | null;
          box_size: string;
          created_at: string | null;
          deliveries_remaining: number | null;
          delivery_type: string;
          discount_percentage: number | null;
          frequency: string;
          id: string;
          is_active: boolean | null;
          next_delivery_date: string | null;
          payment_plan: string | null;
          pickup_location: string;
          status: string | null;
          subscriber_id: string | null;
          updated_at: string | null;
        };
        Insert: {
          auto_renew?: boolean | null;
          base_price_cents?: number | null;
          box_size: string;
          created_at?: string | null;
          deliveries_remaining?: number | null;
          delivery_type: string;
          discount_percentage?: number | null;
          frequency: string;
          id?: string;
          is_active?: boolean | null;
          next_delivery_date?: string | null;
          payment_plan?: string | null;
          pickup_location: string;
          status?: string | null;
          subscriber_id?: string | null;
          updated_at?: string | null;
        };
        Update: {
          auto_renew?: boolean | null;
          base_price_cents?: number | null;
          box_size?: string;
          created_at?: string | null;
          deliveries_remaining?: number | null;
          delivery_type?: string;
          discount_percentage?: number | null;
          frequency?: string;
          id?: string;
          is_active?: boolean | null;
          next_delivery_date?: string | null;
          payment_plan?: string | null;
          pickup_location?: string;
          status?: string | null;
          subscriber_id?: string | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'subscriptions_subscriber_id_fkey';
            columns: ['subscriber_id'];
            isOneToOne: false;
            referencedRelation: 'subscribers';
            referencedColumns: ['id'];
          },
        ];
      };
      user_produce_preferences: {
        Row: {
          created_at: string | null;
          id: string;
          preference_type: string;
          produce_item_id: string | null;
          user_id: string | null;
        };
        Insert: {
          created_at?: string | null;
          id?: string;
          preference_type: string;
          produce_item_id?: string | null;
          user_id?: string | null;
        };
        Update: {
          created_at?: string | null;
          id?: string;
          preference_type?: string;
          produce_item_id?: string | null;
          user_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'user_produce_preferences_produce_item_id_fkey';
            columns: ['produce_item_id'];
            isOneToOne: false;
            referencedRelation: 'produce_items';
            referencedColumns: ['id'];
          },
        ];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      generate_field_day_order_number: {
        Args: Record<PropertyKey, never>;
        Returns: string;
      };
    };
    Enums: {
      user_role: 'user' | 'admin';
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DefaultSchema = Database[Extract<keyof Database, 'public'>];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema['Tables'] & DefaultSchema['Views'])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
        Database[DefaultSchemaTableNameOrOptions['schema']]['Views'])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
      Database[DefaultSchemaTableNameOrOptions['schema']]['Views'])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema['Tables'] &
        DefaultSchema['Views'])
    ? (DefaultSchema['Tables'] &
        DefaultSchema['Views'])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R;
      }
      ? R
      : never
    : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I;
      }
      ? I
      : never
    : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U;
      }
      ? U
      : never
    : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema['Enums']
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions['schema']]['Enums']
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions['schema']]['Enums'][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema['Enums']
    ? DefaultSchema['Enums'][DefaultSchemaEnumNameOrOptions]
    : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema['CompositeTypes']
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes']
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes'][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema['CompositeTypes']
    ? DefaultSchema['CompositeTypes'][PublicCompositeTypeNameOrOptions]
    : never;

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {
      user_role: ['user', 'admin'],
    },
  },
} as const;
