import { Tables } from '@/lib/supabase/types';

// Database table types
export type Subscriber = Tables<'subscribers'>;
export type Subscription = Tables<'subscriptions'>;
export type Payment = Tables<'payments'>;
export type BoxContent = Tables<'box_contents'>;
export type PauseRequest = Tables<'pause_requests'>;
export type ContactSubmission = Tables<'contact_submissions'>;
export type NewsletterSubscription = Tables<'newsletter_subscriptions'>;
export type ProduceItem = Tables<'produce_items'>;
export type UserProducePreference = Tables<'user_produce_preferences'>;
export type Delivery = Tables<'deliveries'>;

// Extended types with relationships
export type SubscriberWithSubscriptions = Subscriber & {
  subscriptions: Subscription[];
  subscription_count?: number;
};

export type SubscriptionWithSubscriber = Subscription & {
  subscribers: Subscriber;
};

export type PaymentWithSubscription = Payment & {
  subscriptions: Subscription & {
    subscribers: Subscriber;
  };
};

export type DeliveryWithSubscription = Delivery & {
  subscriptions: Subscription & {
    subscribers: Subscriber;
  };
};

// Admin dashboard statistics
export interface AdminStats {
  totalSubscribers: number;
  activeSubscriptions: number;
  pendingContactForms: number;
  totalRevenue: number;
  monthlyRevenue: number;
  newSubscribersThisMonth: number;
}

// Table filter types
export interface TableFilters {
  search?: string;
  status?: string;
  dateRange?: {
    from: Date;
    to: Date;
  };
  [key: string]: any;
}

// Common response type for admin operations
export interface AdminResponse<T> {
  data: T | null;
  error: string | null;
  count?: number;
}

// Pagination parameters
export interface PaginationParams {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Admin action types
export type AdminAction =
  | 'view'
  | 'create'
  | 'update'
  | 'delete'
  | 'export'
  | 'bulk_update'
  | 'bulk_delete';

// Audit log entry
export interface AuditLogEntry {
  id: string;
  admin_id: string;
  action: AdminAction;
  resource_type: string;
  resource_id: string;
  changes?: Record<string, any>;
  created_at: string;
}

// Form validation schemas will be defined separately using Zod
