import { SubscriptionData } from '@/lib/constants/subscription';
import { Tables, TablesInsert } from '@/lib/supabase/types';

// Database types
export type Payment = Tables<'payments'>;
export type PaymentInsert = TablesInsert<'payments'>;

// Payment status types
export type PaymentStatus =
  | 'pending'
  | 'processing'
  | 'succeeded'
  | 'failed'
  | 'canceled'
  | 'requires_action'
  | 'requires_payment_method';

// Payment provider types
export type PaymentProvider = 'stripe' | 'paypal';

// Payment intent creation data
export interface CreatePaymentIntentData {
  subscriptionData: SubscriptionData;
  subscriberId: string;
  amount: number; // in cents
  currency: string;
  metadata?: Record<string, string>;
}

// Payment intent response
export interface PaymentIntentResponse {
  clientSecret: string;
  paymentIntentId: string;
  amount: number;
  currency: string;
}

// Payment confirmation data
export interface PaymentConfirmationData {
  paymentIntentId: string;
  subscriptionData: SubscriptionData;
  subscriberId: string;
}

// Subscription creation with payment data
export interface SubscriptionWithPaymentData {
  subscriptionData: SubscriptionData;
  paymentIntentId: string;
  subscriberId: string;
}

// Payment webhook event data
export interface PaymentWebhookData {
  type: string;
  paymentIntentId: string;
  status: PaymentStatus;
  amount: number;
  currency: string;
  metadata?: Record<string, string>;
}

// Payment error types
export interface PaymentError {
  code: string;
  message: string;
  type:
    | 'card_error'
    | 'validation_error'
    | 'api_error'
    | 'authentication_error'
    | 'rate_limit_error';
  param?: string;
}

// Payment method types
export interface PaymentMethodData {
  id: string;
  type: 'card';
  card?: {
    brand: string;
    last4: string;
    exp_month: number;
    exp_year: number;
  };
}

// Subscription payment summary
export interface SubscriptionPaymentSummary {
  subscriptionId: string;
  totalAmount: number;
  currency: string;
  paymentStatus: PaymentStatus;
  paymentMethod?: PaymentMethodData;
  createdAt: string;
  nextPaymentDate?: string;
}
