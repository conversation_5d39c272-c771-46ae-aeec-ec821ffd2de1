import { Tables, TablesInsert, TablesUpdate } from '@/lib/supabase/types';

// Database types
export type Delivery = Tables<'deliveries'>;
export type DeliveryInsert = TablesInsert<'deliveries'>;
export type DeliveryUpdate = TablesUpdate<'deliveries'>;

// Delivery status types
export type DeliveryStatus = 'scheduled' | 'delivered' | 'cancelled';

// Delivery status constants
export const DELIVERY_STATUS = {
  scheduled: {
    id: 'scheduled' as const,
    name: 'Scheduled',
    description: 'Delivery is scheduled for pickup',
    color: '#f59e0b', // amber
  },
  delivered: {
    id: 'delivered' as const,
    name: 'Delivered',
    description: 'Delivery has been completed',
    color: '#22c55e', // green
  },
  cancelled: {
    id: 'cancelled' as const,
    name: 'Cancelled',
    description: 'Delivery has been cancelled',
    color: '#ef4444', // red
  },
} as const;

// Delivery creation data
export interface CreateDeliveryData {
  subscriptionId: string;
  deliveryDate: string; // ISO date string
  pickupLocation: string;
  boxContents?: string;
  specialInstructions?: string;
}

// Delivery update data
export interface UpdateDeliveryData {
  status?: DeliveryStatus;
  deliveryDate?: string;
  pickupLocation?: string;
  boxContents?: string;
  specialInstructions?: string;
}

// Delivery with subscription details
export interface DeliveryWithDetails extends Delivery {
  subscription?: {
    id: string;
    box_size: string;
    frequency: string;
    subscriber?: {
      id: string;
      name: string;
      email: string;
      phone?: string;
    };
  };
}

// Delivery metrics for admin dashboard
export interface DeliveryMetrics {
  week: string;
  delivered: number;
  scheduled: number;
}

// Delivery filters for admin tables
export interface DeliveryFilters {
  status?: DeliveryStatus;
  dateRange?: {
    from: Date;
    to: Date;
  };
  pickupLocation?: string;
  search?: string; // Search by subscriber name or email
}

// Delivery summary for reports
export interface DeliverySummary {
  totalDeliveries: number;
  deliveredCount: number;
  scheduledCount: number;
  cancelledCount: number;
  deliveryRate: number; // percentage of delivered vs total
}
