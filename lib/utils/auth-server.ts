import { createClient } from '@/lib/supabase/server';
import type { AuthUser, AuthResponse } from './auth';

// Server-side auth functions
export const authServer = {
  // Get current user on server
  async getCurrentUser(): Promise<AuthResponse<AuthUser>> {
    try {
      const supabase = await createClient();
      const {
        data: { user },
        error,
      } = await supabase.auth.getUser();

      if (error) {
        return { data: null, error: { message: error.message } };
      }

      if (!user) {
        return { data: null, error: { message: 'No user found' } };
      }

      // Get user role from subscribers table
      const { data: subscriber } = await supabase
        .from('subscribers')
        .select('role, name')
        .eq('user_id', user.id)
        .single();

      return {
        data: {
          id: user.id,
          email: user.email!,
          name: subscriber?.name || user.user_metadata?.name,
          role: subscriber?.role || 'user',
        },
        error: null,
      };
    } catch (error) {
      console.error('Server get current user error:', error);
      return {
        data: null,
        error: { message: 'An unexpected error occurred while getting user' },
      };
    }
  },

  // Check if user is admin
  async isAdmin(): Promise<boolean> {
    const { data: user } = await this.getCurrentUser();
    return user?.role === 'admin';
  },

  // Check if user is authenticated
  async isAuthenticated(): Promise<boolean> {
    const { data: user } = await this.getCurrentUser();
    return user !== null;
  },

  // Get user session
  async getSession() {
    try {
      const supabase = await createClient();
      const {
        data: { session },
        error,
      } = await supabase.auth.getSession();

      if (error) {
        return { session: null, error: { message: error.message } };
      }

      return { session, error: null };
    } catch (error) {
      console.error('Get session error:', error);
      return {
        session: null,
        error: {
          message: 'An unexpected error occurred while getting session',
        },
      };
    }
  },
};
