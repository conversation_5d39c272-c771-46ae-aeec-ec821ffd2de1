import { type FrequencyId } from '@/lib/constants/subscription';
import { formatDate, formatPrice } from '@/lib/utils/format';

/**
 * Email utility functions for formatting and data preparation
 */

export interface SubscriptionEmailData {
  boxSize: string;
  frequency: string;
  paymentPlan: string;
  pickupLocation: string;
  nextDeliveryDate: Date;
  totalPrice: number;
  deliveriesRemaining?: number;
  specialInstructions?: string;
}

export interface SubscriberData {
  id: string;
  email: string;
  first_name: string;
  last_name?: string;
  phone?: string;
  address?: string;
}

export interface ContactFormData {
  id: string;
  name: string;
  email: string;
  message: string;
  created_at: string;
}

/**
 * Format subscription data for email templates
 */
export function formatSubscriptionForEmail(
  subscriptionData: SubscriptionEmailData,
  subscriberData: SubscriberData
) {
  return {
    subscriber: {
      name: subscriberData.first_name,
      fullName:
        `${subscriberData.first_name} ${subscriberData.last_name || ''}`.trim(),
      email: subscriberData.email,
    },
    subscription: {
      boxSize: subscriptionData.boxSize,
      frequency: subscriptionData.frequency,
      paymentPlan: subscriptionData.paymentPlan,
      pickupLocation: subscriptionData.pickupLocation,
      nextDeliveryDate: formatDate(subscriptionData.nextDeliveryDate),
      totalPrice: formatPrice(subscriptionData.totalPrice),
      deliveriesRemaining: subscriptionData.deliveriesRemaining,
      specialInstructions: subscriptionData.specialInstructions,
    },
  };
}

/**
 * Format contact form data for admin notification
 */
export function formatContactFormForEmail(contactData: ContactFormData) {
  return {
    name: contactData.name,
    email: contactData.email,
    message: contactData.message,
    submittedAt: formatDate(new Date(contactData.created_at)),
    messagePreview:
      contactData.message.length > 100
        ? `${contactData.message.substring(0, 100)}...`
        : contactData.message,
  };
}

/**
 * Get next delivery date based on frequency
 */
export function getNextDeliveryDate(frequency: FrequencyId): Date {
  const now = new Date();
  const nextDelivery = new Date(now);

  switch (frequency) {
    case 'weekly':
      nextDelivery.setDate(now.getDate() + 7);
      break;
    case 'biweekly':
      nextDelivery.setDate(now.getDate() + 14);
      break;
    default:
      nextDelivery.setDate(now.getDate() + 7);
  }

  return nextDelivery;
}

/**
 * Get renewal reminder date (7 days before next delivery)
 */
export function getRenewalReminderDate(nextDeliveryDate: Date): Date {
  const reminderDate = new Date(nextDeliveryDate);
  reminderDate.setDate(nextDeliveryDate.getDate() - 7);
  return reminderDate;
}

/**
 * Get delivery reminder date (3 days before delivery)
 */
export function getDeliveryReminderDate(deliveryDate: Date): Date {
  const reminderDate = new Date(deliveryDate);
  reminderDate.setDate(deliveryDate.getDate() - 3);
  return reminderDate;
}

/**
 * Format frequency for display
 */
export function formatFrequencyDisplay(frequency: string): string {
  switch (frequency.toLowerCase()) {
    case 'weekly':
      return 'Weekly';
    case 'biweekly':
      return 'Every 2 weeks';
    case 'monthly':
      return 'Monthly';
    default:
      return frequency;
  }
}

/**
 * Format box size for display
 */
export function formatBoxSizeDisplay(boxSize: string): string {
  switch (boxSize.toLowerCase()) {
    case 'small':
      return 'Small Box (2-3 people)';
    case 'medium':
      return 'Medium Box (4-5 people)';
    case 'large':
      return 'Large Box (6+ people)';
    default:
      return boxSize;
  }
}

/**
 * Format payment plan for display
 */
export function formatPaymentPlanDisplay(paymentPlan: string): string {
  switch (paymentPlan.toLowerCase()) {
    case 'weekly':
      return 'Pay Weekly';
    case 'monthly':
      return 'Pay Monthly';
    case 'seasonal':
      return 'Pay for Full Season';
    default:
      return paymentPlan;
  }
}

/**
 * Generate email subject lines
 */
export const EMAIL_SUBJECTS = {
  welcome: 'Welcome to AsedaFoods CSA! Your Account is Ready',
  subscriptionConfirmation: 'Your AsedaFoods CSA Subscription is Confirmed!',
  paymentConfirmation: 'Payment Received - Thank You!',
  subscriptionCancellation: 'Your Subscription Has Been Cancelled',
  subscriptionPause: 'Your Subscription Has Been Paused',
  deliveryReminder: 'Your Fresh Box is Ready for Pickup Soon!',
  deliveryProcessed: 'Your Fresh Box Has Been Processed',
  renewalReminder: 'Time to Renew Your AsedaFoods Subscription',
  adminNewSubscription: 'New CSA Subscription Received',
  adminContactForm: 'New Contact Form Submission',
  adminNewsletterSignup: 'New Newsletter Subscription',
} as const;

/**
 * Default email addresses
 */
export const EMAIL_ADDRESSES = {
  noreply: 'AsedaFoods <<EMAIL>>',
  support: 'AsedaFoods Support <<EMAIL>>',
  admin: process.env.ADMIN_EMAIL || '<EMAIL>',
} as const;
