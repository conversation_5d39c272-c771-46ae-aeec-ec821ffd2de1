import { createClient } from '@/lib/supabase/client';
import { toast } from 'sonner';

// Create a single Supabase client instance for reuse
const supabaseClient = createClient();

export type AuthUser = {
  id: string;
  email: string;
  name?: string;
  role?: 'user' | 'admin';
};

export type SignUpData = {
  email: string;
  password: string;
  name: string;
  address?: string;
  phone?: string;
  role?: 'user' | 'admin';
};

export type SignInData = {
  email: string;
  password: string;
};

export type AuthError = {
  message: string;
  code?: string;
};

export type AuthResponse<T = unknown> = {
  data: T | null;
  error: AuthError | null;
};

// Client-side auth functions
export const authClient = {
  // Sign up new user with integrated toast notifications
  async signUp(userData: SignUpData): Promise<AuthResponse<AuthUser>> {
    const toastPromise = toast.promise(
      new Promise<AuthResponse<AuthUser>>(async (resolve, reject) => {
        try {
          const { data, error } = await supabaseClient.auth.signUp({
            email: userData.email,
            password: userData.password,
            options: {
              emailRedirectTo: `${window.location.origin}/auth/callback`,
              data: {
                name: userData.name,
                address: userData.address || 'Address to be provided',
                phone: userData.phone || '',
                role: userData.role || 'user',
              },
            },
          });

          if (error) {
            reject(new Error(error.message));
            return;
          }

          if (!data.user) {
            reject(new Error('Failed to create user'));
            return;
          }

          const authUser: AuthUser = {
            id: data.user.id,
            email: data.user.email!,
            name: userData.name,
            role: userData.role || 'user',
          };

          resolve({ data: authUser, error: null });
        } catch (error) {
          console.error('Sign up error:', error);
          reject(
            error instanceof Error
              ? error
              : new Error('An unexpected error occurred during sign up')
          );
        }
      }),
      {
        loading: TOAST_MESSAGES.signUp.loading,
        success: () => TOAST_MESSAGES.signUp.success,
        error: (error) => {
          console.error('Sign up toast error:', error);
          return TOAST_MESSAGES.signUp.error;
        },
      }
    );

    return await toastPromise.unwrap();
  },

  // Sign in existing user with integrated toast notifications
  async signIn(
    credentials: SignInData,
    redirectTo?: string
  ): Promise<AuthResponse<AuthUser>> {
    const toastPromise = toast.promise(
      new Promise<AuthResponse<AuthUser>>(async (resolve, reject) => {
        try {
          const { data, error } = await supabaseClient.auth.signInWithPassword({
            email: credentials.email,
            password: credentials.password,
          });

          if (error) {
            reject(new Error(error.message));
            return;
          }

          if (!data.user) {
            reject(new Error('Failed to sign in'));
            return;
          }

          // Get user role from subscribers table
          const { data: subscriber } = await supabaseClient
            .from('subscribers')
            .select('role, name')
            .eq('user_id', data.user.id)
            .single();

          const authUser: AuthUser = {
            id: data.user.id,
            email: data.user.email!,
            name: subscriber?.name || data.user.user_metadata?.name,
            role: subscriber?.role || 'user',
          };

          // Update user store
          if (typeof window !== 'undefined') {
            const { useUserStore } = await import('@/lib/store/users');
            const { updateUser } = useUserStore.getState();
            updateUser(data.user);

            // Add user to audience for email marketing (non-blocking)
            try {
              if (authUser.email) {
                const { addUserToAudience } = await import(
                  '@/lib/actions/audience'
                );
                addUserToAudience(authUser.email).catch((audienceError) => {
                  console.error(
                    'Failed to add user to audience during sign-in:',
                    audienceError
                  );
                });
              }
            } catch (audienceError) {
              console.error(
                'Error adding user to audience during sign-in:',
                audienceError
              );
            }

            // Auto-redirect on successful sign in with role-based routing
            const finalRedirectTo =
              redirectTo ||
              (authUser.role === 'admin' ? '/admin' : '/dashboard');
            setTimeout(() => {
              window.location.href = finalRedirectTo;
            }, 1000);
          }

          resolve({ data: authUser, error: null });
        } catch (error) {
          console.error('Sign in error:', error);
          reject(
            error instanceof Error
              ? error
              : new Error('An unexpected error occurred during sign in')
          );
        }
      }),
      {
        loading: TOAST_MESSAGES.signIn.loading,
        success: (result) => TOAST_MESSAGES.signIn.success(result.data?.name),
        error: (error) => {
          console.error('Sign in toast error:', error);
          return TOAST_MESSAGES.signIn.error;
        },
      }
    );

    return await toastPromise.unwrap();
  },

  // Sign out user with integrated toast notifications
  async signOut(): Promise<AuthResponse<null>> {
    const toastPromise = toast.promise(
      new Promise<AuthResponse<null>>(async (resolve, reject) => {
        try {
          // Clear user store first for immediate UI feedback
          if (typeof window !== 'undefined') {
            const { useUserStore } = await import('@/lib/store/users');
            const { removeUser } = useUserStore.getState();
            removeUser(null);
          }

          // Then perform the actual sign out
          const { error } = await supabaseClient.auth.signOut();

          if (error) {
            reject(new Error(error.message));
            return;
          }

          resolve({ data: null, error: null });
        } catch (error) {
          console.error('Sign out error:', error);
          reject(
            error instanceof Error
              ? error
              : new Error('An unexpected error occurred during sign out')
          );
        }
      }),
      {
        loading: TOAST_MESSAGES.signOut.loading,
        success: () => TOAST_MESSAGES.signOut.success,
        error: (error) => {
          console.error('Sign out toast error:', error);
          return TOAST_MESSAGES.signOut.error;
        },
      }
    );

    return await toastPromise.unwrap();
  },

  // Get current user
  async getCurrentUser(): Promise<AuthResponse<AuthUser>> {
    try {
      const {
        data: { user },
        error,
      } = await supabaseClient.auth.getUser();

      if (error) {
        return { data: null, error: { message: error.message } };
      }

      if (!user) {
        return { data: null, error: { message: 'No user found' } };
      }

      // Get user role from subscribers table
      const { data: subscriber } = await supabaseClient
        .from('subscribers')
        .select('role, name')
        .eq('user_id', user.id)
        .single();

      return {
        data: {
          id: user.id,
          email: user.email!,
          name: subscriber?.name || user.user_metadata?.name,
          role: subscriber?.role || 'user',
        },
        error: null,
      };
    } catch (error) {
      console.error('Get current user error:', error);
      return {
        data: null,
        error: { message: 'An unexpected error occurred while getting user' },
      };
    }
  },

  // Reset password with integrated toast notifications
  async resetPassword(email: string): Promise<AuthResponse<null>> {
    const toastPromise = toast.promise(
      new Promise<AuthResponse<null>>(async (resolve, reject) => {
        try {
          const { error } = await supabaseClient.auth.resetPasswordForEmail(
            email,
            {
              redirectTo: `${window.location.origin}/auth/callback?next=/auth/reset-password`,
            }
          );

          if (error) {
            reject(new Error(error.message));
            return;
          }

          resolve({ data: null, error: null });
        } catch (error) {
          console.error('Reset password error:', error);
          reject(
            error instanceof Error
              ? error
              : new Error('An unexpected error occurred during password reset')
          );
        }
      }),
      {
        loading: TOAST_MESSAGES.forgotPassword.loading,
        success: () => TOAST_MESSAGES.forgotPassword.success(email),
        error: (error) => {
          console.error('Reset password toast error:', error);
          return TOAST_MESSAGES.forgotPassword.error;
        },
      }
    );

    return await toastPromise.unwrap();
  },

  // Update password with integrated toast notifications
  async updatePassword(newPassword: string): Promise<AuthResponse<null>> {
    const toastPromise = toast.promise(
      new Promise<AuthResponse<null>>(async (resolve, reject) => {
        try {
          const { error } = await supabaseClient.auth.updateUser({
            password: newPassword,
          });

          if (error) {
            reject(new Error(error.message));
            return;
          }

          resolve({ data: null, error: null });
        } catch (error) {
          console.error('Update password error:', error);
          reject(
            error instanceof Error
              ? error
              : new Error('An unexpected error occurred during password update')
          );
        }
      }),
      {
        loading: TOAST_MESSAGES.resetPassword.loading,
        success: () => TOAST_MESSAGES.resetPassword.success,
        error: (error) => {
          console.error('Update password toast error:', error);
          return TOAST_MESSAGES.resetPassword.error;
        },
      }
    );

    return await toastPromise.unwrap();
  },
};

// Utility functions
export const authUtils = {
  // Validate email format
  isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  // Validate password strength
  isValidPassword(password: string): { isValid: boolean; message?: string } {
    if (password.length < 8) {
      return {
        isValid: false,
        message: 'Password must be at least 8 characters long',
      };
    }
    if (!/(?=.*[a-z])/.test(password)) {
      return {
        isValid: false,
        message: 'Password must contain at least one lowercase letter',
      };
    }
    if (!/(?=.*[A-Z])/.test(password)) {
      return {
        isValid: false,
        message: 'Password must contain at least one uppercase letter',
      };
    }
    if (!/(?=.*\d)/.test(password)) {
      return {
        isValid: false,
        message: 'Password must contain at least one number',
      };
    }
    return { isValid: true };
  },

  // Format auth error messages
  formatAuthError(error: string): string {
    switch (error) {
      case 'Invalid login credentials':
        return 'Invalid email or password. Please try again.';
      case 'Email not confirmed':
        return 'Please check your email and click the confirmation link.';
      case 'User already registered':
        return 'An account with this email already exists.';
      case 'Password should be at least 6 characters':
        return 'Password must be at least 6 characters long.';
      default:
        return error;
    }
  },

  // Generate redirect URL for auth flows
  getRedirectUrl(path: string = '/'): string {
    const baseUrl =
      typeof window !== 'undefined'
        ? window.location.origin
        : process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
    return `${baseUrl}${path}`;
  },

  // Get role-based redirect path
  getRoleBasedRedirect(role?: string): string {
    return role === 'admin' ? '/admin' : '/dashboard';
  },

  // Perform role-based redirect
  redirectBasedOnRole(user: AuthUser, fallbackPath?: string): void {
    if (typeof window !== 'undefined') {
      const redirectPath = fallbackPath || this.getRoleBasedRedirect(user.role);
      window.location.href = redirectPath;
    }
  },
};

// Toast messages for different auth actions
const TOAST_MESSAGES = {
  signIn: {
    loading: 'Signing you in...',
    success: (name?: string) => `Welcome back${name ? `, ${name}` : ''}! 🎉`,
    error: 'Failed to sign in. Please check your credentials.',
  },
  signUp: {
    loading: 'Creating your account...',
    success:
      'Account created successfully! Please check your email to verify your account. 📧',
    error:
      'Failed to create account. Please check your information and try again.',
  },
  signOut: {
    loading: 'Signing you out...',
    success: 'You have been signed out successfully. See you soon! 👋',
    error: 'Failed to sign out. Please try again.',
  },
  forgotPassword: {
    loading: 'Sending reset link...',
    success: (email: string) =>
      `Password reset link sent to ${email}! Check your inbox. 📧`,
    error: 'Failed to send reset link. Please try again.',
  },
  resetPassword: {
    loading: 'Updating your password...',
    success:
      'Password updated successfully! You can now sign in with your new password. ✅',
    error: 'Failed to update password. Please try again.',
  },
} as const;

// Custom toast functions for specific scenarios
export const authToast = {
  // Success toasts
  success: {
    emailVerificationSent: (email: string) =>
      toast.success(
        `Verification email sent to ${email}! Please check your inbox. 📧`
      ),

    passwordResetSent: (email: string) =>
      toast.success(
        `Password reset link sent to ${email}! Check your inbox. 📧`
      ),

    accountCreated: () =>
      toast.success(
        'Account created successfully! Please verify your email. ✅'
      ),

    passwordUpdated: () => toast.success('Password updated successfully! ✅'),

    profileUpdated: () => toast.success('Profile updated successfully! ✅'),

    welcomeBack: (name?: string) =>
      toast.success(`Welcome back${name ? `, ${name}` : ''}! 🎉`),

    signedOut: () =>
      toast.success('You have been signed out successfully. See you soon! 👋'),
  },

  // Error toasts
  error: {
    invalidCredentials: () =>
      toast.error('Invalid email or password. Please try again.'),

    emailAlreadyExists: () =>
      toast.error('An account with this email already exists.'),

    weakPassword: () =>
      toast.error('Password is too weak. Please choose a stronger password.'),

    emailNotVerified: () =>
      toast.error('Please verify your email before signing in.'),

    invalidResetLink: () =>
      toast.error('Invalid or expired reset link. Please request a new one.'),

    networkError: () =>
      toast.error('Network error. Please check your connection and try again.'),

    unexpectedError: () =>
      toast.error('An unexpected error occurred. Please try again.'),

    sessionExpired: () =>
      toast.error('Your session has expired. Please sign in again.'),
  },

  // Info toasts
  info: {
    checkEmail: (email: string) =>
      toast.info(
        `Please check your email (${email}) for further instructions.`
      ),

    redirecting: (destination: string) =>
      toast.info(`Redirecting you to ${destination}...`),

    sessionRefreshed: () => toast.info('Session refreshed successfully.'),
  },
};

// Helper function to handle auth errors with appropriate toasts
export const handleAuthError = (error: any) => {
  const errorMessage = error?.message || error || 'Unknown error';
  const formattedError = authUtils.formatAuthError(errorMessage);

  // Map specific errors to custom toasts
  switch (errorMessage) {
    case 'Invalid login credentials':
      return authToast.error.invalidCredentials();
    case 'User already registered':
      return authToast.error.emailAlreadyExists();
    case 'Email not confirmed':
      return authToast.error.emailNotVerified();
    case 'Password should be at least 6 characters':
      return authToast.error.weakPassword();
    default:
      return toast.error(formattedError);
  }
};

// Backward compatibility aliases - these point to the simplified authClient functions
export const authWithToast = {
  signIn: authClient.signIn,
  signUp: authClient.signUp,
  signOut: authClient.signOut,
  forgotPassword: authClient.resetPassword,
  resetPassword: authClient.updatePassword,
};
