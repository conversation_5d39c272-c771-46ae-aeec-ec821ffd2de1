// Subscription model constants and types
export const SHARE_SIZES = {
  standard: {
    id: 'standard',
    name: 'Fresh Produce Box',
    price: 44.0,
    priceCents: 4400,
    description: 'Fresh seasonal produce',
    servings: 'Perfect for families',
    subtitle: 'Locally sourced, seasonal produce',
    isPopular: true,
  },
} as const;

// Backward compatibility
export const BOX_SIZES = SHARE_SIZES;

export const FREQUENCIES = {
  weekly: {
    id: 'weekly',
    name: 'Weekly Delivery',
    description: 'Fresh produce every week',
    discount: 0,
  },
  biweekly: {
    id: 'biweekly',
    name: 'Bi-weekly Delivery',
    description: 'Fresh produce every two weeks',
    discount: 0,
  },
  // monthly: {
  //   id: 'monthly',
  //   name: 'Monthly Delivery',
  //   description: 'Fresh produce once a month',
  //   discount: 10,
  // },
} as const;

export const PAYMENT_PLANS = {
  '4_boxes': {
    id: '4_boxes',
    name: 'Prepay for 4 boxes',
    deliveries: 4,
    discount: 0,
    description: '$44.00 /ea',
    pricePerShare: 44.0,
    pricePerShareCents: 4400,
  },
  '13_boxes': {
    id: '13_boxes',
    name: 'Prepay for 13 boxes',
    deliveries: 13,
    discount: 5,
    description: 'Save 5% - $42.00 /ea',
    pricePerShare: 42.0,
    pricePerShareCents: 4200,
    popular: true,
  },
  '25_boxes': {
    id: '25_boxes',
    name: 'Prepay for 25 boxes',
    deliveries: 25,
    discount: 9,
    description: 'Save 9% - $40.00 /ea',
    pricePerShare: 40.0,
    pricePerShareCents: 4000,
    bestValue: true,
  },
} as const;

export const PICKUP_LOCATIONS = {
  elite_bodies: {
    id: 'elite_bodies',
    name: 'Elite Bodies',
    address: '10111 Colesville Road, Silver Spring, MD 20910',
    description: 'Silver Spring location',
  },
  shabach_ministries: {
    id: 'shabach_ministries',
    name: 'Shabach! Ministries',
    address: '3600 Brightseat Rd, Glenarden, MD 20706',
    description: 'Glenarden location',
  },
} as const;

export const DELIVERY_TYPES = {
  pickup: {
    id: 'pickup',
    name: 'Pickup Location',
    description: 'Pick up at one of our convenient locations',
  },
} as const;

// Discount calculation based on frequency and payment plan (Farmhand model)
export const DISCOUNT_TIERS = {
  weekly: {
    '4_boxes': { discount: 0 },
    '13_boxes': { discount: 5 },
    '25_shares': { discount: 9 },
  },
  biweekly: {
    '4_shares': { discount: 0 },
    '13_shares': { discount: 7 },
    '25_shares': { discount: 9 },
  },
  // monthly: {
  //   '4_shares': { discount: 0 },
  //   '13_shares': { discount: 5 },
  //   '25_shares': { discount: 9 },
  // },
} as const;

// Type definitions
export type ShareSizeId = keyof typeof SHARE_SIZES;
export type BoxSizeId = ShareSizeId; // Backward compatibility
export type FrequencyId = keyof typeof FREQUENCIES;
export type PaymentPlanId = keyof typeof PAYMENT_PLANS;
export type DeliveryTypeId = keyof typeof DELIVERY_TYPES;
export type PickupLocationId = keyof typeof PICKUP_LOCATIONS;

export interface SubscriptionData {
  shareSize: ShareSizeId;
  boxSize?: BoxSizeId; // Backward compatibility - maps to shareSize
  frequency: FrequencyId;
  paymentPlan: PaymentPlanId;
  deliveryType: DeliveryTypeId;
  pickupLocation: PickupLocationId;
  specialInstructions?: string;
}

export interface PricingCalculation {
  basePrice: number;
  basePriceCents: number;
  frequencyDiscount: number;
  paymentPlanDiscount: number;
  totalDiscount: number;
  discountAmount: number;
  finalPrice: number;
  finalPriceCents: number;
  totalPrice: number;
  totalPriceCents: number;
  savings: number;
  pricePerBox: number;
}

// Utility functions
export const calculatePrice = (
  shareSize: ShareSizeId,
  frequency: FrequencyId,
  paymentPlan: PaymentPlanId
): PricingCalculation => {
  // Validate inputs to prevent undefined access errors
  if (!shareSize || !SHARE_SIZES[shareSize]) {
    throw new Error(`Invalid share size: ${shareSize}`);
  }

  if (!frequency || !FREQUENCIES[frequency]) {
    throw new Error(`Invalid frequency: ${frequency}`);
  }

  if (!paymentPlan || !PAYMENT_PLANS[paymentPlan]) {
    throw new Error(`Invalid payment plan: ${paymentPlan}`);
  }

  // Farmhand model: Price per share is predetermined by payment plan
  const basePrice = SHARE_SIZES[shareSize].price;
  const basePriceCents = SHARE_SIZES[shareSize].priceCents;
  const deliveries = PAYMENT_PLANS[paymentPlan].deliveries;

  // Get the actual price per share from the payment plan (already discounted)
  const finalPrice = PAYMENT_PLANS[paymentPlan].pricePerShare;
  const finalPriceCents = PAYMENT_PLANS[paymentPlan].pricePerShareCents;

  // Get discount percentage for display purposes
  const totalDiscount = PAYMENT_PLANS[paymentPlan].discount;
  const frequencyDiscount = 0; // Not used in Farmhand model
  const paymentPlanDiscount = totalDiscount;

  // Calculate total price for all shares
  const totalPrice = finalPrice * deliveries;
  const totalPriceCents = finalPriceCents * deliveries;

  // Calculate savings compared to base price
  const savings = (basePrice - finalPrice) * deliveries;

  // Calculate discount amount per share
  const discountAmount = basePrice - finalPrice;

  return {
    basePrice,
    basePriceCents,
    frequencyDiscount,
    paymentPlanDiscount,
    totalDiscount,
    discountAmount,
    finalPrice,
    finalPriceCents,
    totalPrice,
    totalPriceCents,
    savings,
    pricePerBox: finalPrice, // Keep for backward compatibility
  };
};

export const formatPrice = (price: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(price);
};

export const getNextPickupDate = (
  frequency: FrequencyId,
  startDate?: Date
): Date => {
  // Validate frequency input
  if (!frequency || !FREQUENCIES[frequency]) {
    throw new Error(`Invalid frequency: ${frequency}`);
  }

  const start = startDate || new Date();
  const nextPickup = new Date(start);

  switch (frequency) {
    case 'weekly':
      nextPickup.setDate(nextPickup.getDate() + 7);
      break;
    case 'biweekly':
      nextPickup.setDate(nextPickup.getDate() + 14);
      break;
    // case 'monthly':
    //   nextPickup.setMonth(nextPickup.getMonth() + 1);
    //   break;
    default:
      throw new Error(`Unsupported frequency: ${frequency}`);
  }

  return nextPickup;
};

// Keep the old function name for backward compatibility
export const getNextDeliveryDate = getNextPickupDate;
