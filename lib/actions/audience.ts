'use server';

import { EmailService } from '@/lib/services/email';

/**
 * Server action to add a user to the general audience
 * This is used when users sign in to ensure they're added to the mailing list
 */
export async function addUserToAudience(email: string) {
  try {
    console.log('Adding user to audience via server action:', email);

    const result = await EmailService.addToGeneralAudience(email);

    if (result.success) {
      console.log('User successfully added to audience:', {
        email,
        contactId: result.data?.contactId,
      });
      return {
        success: true,
        message: 'User added to audience successfully',
        data: result.data,
      };
    } else {
      console.error('Failed to add user to audience:', {
        email,
        error: result.error,
      });
      return {
        success: false,
        message: 'Failed to add user to audience',
        error: result.error,
      };
    }
  } catch (error) {
    console.error('Exception in addUserToAudience server action:', error);
    return {
      success: false,
      message: 'An unexpected error occurred',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Server action to remove a user from the general audience
 * This can be used for unsubscribe functionality
 */
export async function removeUserFromAudience(email: string) {
  try {
    console.log('Removing user from audience via server action:', email);

    const result = await EmailService.removeFromGeneralAudience(email);

    if (result.success) {
      console.log('User successfully removed from audience:', {
        email,
        deleted: result.data?.deleted,
      });
      return {
        success: true,
        message: 'User removed from audience successfully',
        data: result.data,
      };
    } else {
      console.error('Failed to remove user from audience:', {
        email,
        error: result.error,
      });
      return {
        success: false,
        message: 'Failed to remove user from audience',
        error: result.error,
      };
    }
  } catch (error) {
    console.error('Exception in removeUserFromAudience server action:', error);
    return {
      success: false,
      message: 'An unexpected error occurred',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}
