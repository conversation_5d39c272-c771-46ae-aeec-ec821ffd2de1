'use server';

import { createClient } from '@/lib/supabase/server';
import { authServer } from '@/lib/utils/auth-server';
import { revalidatePath } from 'next/cache';
import { EmailService } from '@/lib/services/email';

export interface SubscriptionActionResult {
  success: boolean;
  error?: string;
  data?: any;
}

/**
 * Cancel a user's subscription
 */
export async function cancelSubscriptionAction(
  subscriptionId: string
): Promise<SubscriptionActionResult> {
  try {
    console.log('cancelSubscriptionAction called with:', { subscriptionId });
    console.time('cancelSubscriptionAction-total');

    console.time('cancelSubscriptionAction-auth');
    const { data: user } = await authServer.getCurrentUser();
    console.timeEnd('cancelSubscriptionAction-auth');

    if (!user) {
      console.log('cancelSubscriptionAction: User not authenticated');
      console.timeEnd('cancelSubscriptionAction-total');
      return { success: false, error: 'Unauthorized' };
    }

    console.log('cancelSubscriptionAction: User authenticated:', user.id);

    console.time('cancelSubscriptionAction-createClient');
    const supabase = await createClient();
    console.timeEnd('cancelSubscriptionAction-createClient');

    // Verify the subscription belongs to the user
    console.time('cancelSubscriptionAction-fetchSubscription');
    const { data: subscription, error: fetchError } = await supabase
      .from('subscriptions')
      .select('*, subscribers!inner(user_id)')
      .eq('id', subscriptionId)
      .eq('subscribers.user_id', user.id)
      .single();
    console.timeEnd('cancelSubscriptionAction-fetchSubscription');

    if (fetchError) {
      console.error('cancelSubscriptionAction: Fetch error:', fetchError);
      return {
        success: false,
        error: `Subscription fetch failed: ${fetchError.message}`,
      };
    }

    if (!subscription) {
      console.log('cancelSubscriptionAction: Subscription not found');
      return { success: false, error: 'Subscription not found' };
    }

    console.log(
      'cancelSubscriptionAction: Subscription found, current status:',
      subscription.status
    );

    // Check if subscription is already cancelled
    if (subscription.status === 'cancelled') {
      console.log('cancelSubscriptionAction: Subscription already cancelled');
      return { success: false, error: 'Subscription is already cancelled' };
    }

    // Update subscription status to cancelled (permanent)
    console.time('cancelSubscriptionAction-updateSubscription');
    const { data, error } = await supabase
      .from('subscriptions')
      .update({
        status: 'cancelled',
        is_active: false,
        auto_renew: false,
        updated_at: new Date().toISOString(),
      })
      .eq('id', subscriptionId)
      .select()
      .single();
    console.timeEnd('cancelSubscriptionAction-updateSubscription');

    if (error) {
      console.error('cancelSubscriptionAction: Update error:', error);
      return { success: false, error: error.message };
    }

    console.log('cancelSubscriptionAction: Update successful:', data);

    // Send cancellation email
    try {
      console.time('cancelSubscriptionAction-email');
      const { data: subscriberDetails } = await supabase
        .from('subscribers')
        .select('name, email')
        .eq('user_id', user.id)
        .single();

      if (subscriberDetails) {
        await EmailService.sendSubscriptionCancellation(
          subscriberDetails.email,
          subscriberDetails.name || 'there',
          {
            boxSize: subscription.box_size,
            frequency: subscription.frequency,
            cancellationDate: new Date(),
            lastDeliveryDate: subscription.next_delivery_date
              ? new Date(subscription.next_delivery_date)
              : undefined,
          }
        );
        console.log('Cancellation email sent successfully');
      }
      console.timeEnd('cancelSubscriptionAction-email');
    } catch (emailError) {
      // Don't fail the cancellation if email sending fails
      console.error('Error sending cancellation email:', emailError);
    }

    console.time('cancelSubscriptionAction-revalidate');
    revalidatePath('/dashboard');
    console.timeEnd('cancelSubscriptionAction-revalidate');

    console.timeEnd('cancelSubscriptionAction-total');
    return { success: true, data };
  } catch (error) {
    console.error('Error cancelling subscription:', error);
    console.timeEnd('cancelSubscriptionAction-total');
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Failed to cancel subscription',
    };
  }
}

/**
 * Pause a user's subscription
 */
export async function pauseSubscriptionAction(
  subscriptionId: string,
  pauseUntil: string
): Promise<SubscriptionActionResult> {
  try {
    const { data: user } = await authServer.getCurrentUser();
    if (!user) {
      return { success: false, error: 'Unauthorized' };
    }

    const supabase = await createClient();

    // Verify the subscription belongs to the user
    const { data: subscription, error: fetchError } = await supabase
      .from('subscriptions')
      .select('*, subscribers!inner(user_id)')
      .eq('id', subscriptionId)
      .eq('subscribers.user_id', user.id)
      .single();

    if (fetchError || !subscription) {
      return { success: false, error: 'Subscription not found' };
    }

    // Update subscription status
    const { data, error } = await supabase
      .from('subscriptions')
      .update({
        status: 'paused',
        is_active: false,
        updated_at: new Date().toISOString(),
      })
      .eq('id', subscriptionId)
      .select()
      .single();

    if (error) {
      return { success: false, error: error.message };
    }

    // Create pause request record
    await supabase.from('pause_requests').insert({
      subscription_id: subscriptionId,
      pause_start_date: new Date().toISOString().split('T')[0],
      pause_end_date: pauseUntil,
    });

    // Send pause email
    try {
      const { data: subscriberDetails } = await supabase
        .from('subscribers')
        .select('name, email')
        .eq('user_id', user.id)
        .single();

      if (subscriberDetails) {
        const pauseStartDate = new Date();
        const pauseEndDate = new Date(pauseUntil);
        const resumeDate = new Date(pauseEndDate);
        resumeDate.setDate(resumeDate.getDate() + 1); // Resume the day after pause ends

        await EmailService.sendSubscriptionPause(
          subscriberDetails.email,
          subscriberDetails.name || 'there',
          {
            boxSize: subscription.box_size,
            frequency: subscription.frequency,
            pauseStartDate,
            pauseEndDate,
            resumeDate,
          }
        );
        console.log('Pause email sent successfully');
      }
    } catch (emailError) {
      // Don't fail the pause if email sending fails
      console.error('Error sending pause email:', emailError);
    }

    revalidatePath('/dashboard');
    return { success: true, data };
  } catch (error) {
    console.error('Error pausing subscription:', error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : 'Failed to pause subscription',
    };
  }
}

/**
 * Resume a user's subscription
 */
export async function resumeSubscriptionAction(
  subscriptionId: string
): Promise<SubscriptionActionResult> {
  try {
    console.log('resumeSubscriptionAction called with:', { subscriptionId });
    console.time('resumeSubscriptionAction-total');

    console.time('resumeSubscriptionAction-auth');
    const { data: user } = await authServer.getCurrentUser();
    console.timeEnd('resumeSubscriptionAction-auth');

    if (!user) {
      console.log('resumeSubscriptionAction: User not authenticated');
      console.timeEnd('resumeSubscriptionAction-total');
      return { success: false, error: 'Unauthorized' };
    }

    console.log('resumeSubscriptionAction: User authenticated:', user.id);

    console.time('resumeSubscriptionAction-createClient');
    const supabase = await createClient();
    console.timeEnd('resumeSubscriptionAction-createClient');

    // Verify the subscription belongs to the user
    console.time('resumeSubscriptionAction-fetchSubscription');
    const { data: subscription, error: fetchError } = await supabase
      .from('subscriptions')
      .select('*, subscribers!inner(user_id)')
      .eq('id', subscriptionId)
      .eq('subscribers.user_id', user.id)
      .single();
    console.timeEnd('resumeSubscriptionAction-fetchSubscription');

    if (fetchError) {
      console.error('resumeSubscriptionAction: Fetch error:', fetchError);
      console.timeEnd('resumeSubscriptionAction-total');
      return {
        success: false,
        error: `Subscription fetch failed: ${fetchError.message}`,
      };
    }

    if (!subscription) {
      console.log('resumeSubscriptionAction: Subscription not found');
      console.timeEnd('resumeSubscriptionAction-total');
      return { success: false, error: 'Subscription not found' };
    }

    console.log(
      'resumeSubscriptionAction: Subscription found, current status:',
      subscription.status
    );

    // Check if subscription is already active
    if (subscription.status === 'active') {
      return { success: false, error: 'Subscription is already active' };
    }

    // Check if subscription is paused (can only resume paused subscriptions)
    if (subscription.status !== 'paused') {
      return {
        success: false,
        error: 'Only paused subscriptions can be resumed',
      };
    }

    // Check for existing active subscriptions (should not have any when resuming)
    const { data: activeSubscriptions, error: activeError } = await supabase
      .from('subscriptions')
      .select('*, subscribers!inner(user_id)')
      .eq('subscribers.user_id', user.id)
      .eq('status', 'active')
      .eq('is_active', true);

    if (activeError) {
      return {
        success: false,
        error: 'Failed to check for active subscriptions',
      };
    }

    if (activeSubscriptions && activeSubscriptions.length > 0) {
      return {
        success: false,
        error:
          'You already have an active subscription. Please cancel it before resuming this one.',
      };
    }

    // Update subscription status to active
    console.time('resumeSubscriptionAction-updateSubscription');
    const { data, error } = await supabase
      .from('subscriptions')
      .update({
        status: 'active',
        is_active: true,
        updated_at: new Date().toISOString(),
      })
      .eq('id', subscriptionId)
      .select()
      .single();
    console.timeEnd('resumeSubscriptionAction-updateSubscription');

    if (error) {
      console.error('resumeSubscriptionAction: Update error:', error);
      console.timeEnd('resumeSubscriptionAction-total');
      return { success: false, error: error.message };
    }

    console.log('resumeSubscriptionAction: Update successful:', data);

    console.time('resumeSubscriptionAction-revalidate');
    revalidatePath('/dashboard');
    console.timeEnd('resumeSubscriptionAction-revalidate');

    console.timeEnd('resumeSubscriptionAction-total');
    return { success: true, data };
  } catch (error) {
    console.error('Error resuming subscription:', error);
    console.timeEnd('resumeSubscriptionAction-total');
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Failed to resume subscription',
    };
  }
}

/**
 * Toggle auto-renewal for a subscription
 */
export async function toggleAutoRenewAction(
  subscriptionId: string,
  autoRenew: boolean
): Promise<SubscriptionActionResult> {
  try {
    console.log('toggleAutoRenewAction called with:', {
      subscriptionId,
      autoRenew,
    });

    const { data: user } = await authServer.getCurrentUser();
    if (!user) {
      console.log('toggleAutoRenewAction: User not authenticated');
      return { success: false, error: 'Unauthorized' };
    }

    console.log('toggleAutoRenewAction: User authenticated:', user.id);
    const supabase = await createClient();

    // Verify the subscription belongs to the user
    const { data: subscription, error: fetchError } = await supabase
      .from('subscriptions')
      .select('*, subscribers!inner(user_id)')
      .eq('id', subscriptionId)
      .eq('subscribers.user_id', user.id)
      .single();

    if (fetchError) {
      console.error('toggleAutoRenewAction: Fetch error:', fetchError);
      return {
        success: false,
        error: `Subscription fetch failed: ${fetchError.message}`,
      };
    }

    if (!subscription) {
      console.log('toggleAutoRenewAction: Subscription not found');
      return { success: false, error: 'Subscription not found' };
    }

    console.log(
      'toggleAutoRenewAction: Subscription found, current auto_renew:',
      subscription.auto_renew
    );

    // Update auto-renewal setting
    const { data, error } = await supabase
      .from('subscriptions')
      .update({
        auto_renew: autoRenew,
        updated_at: new Date().toISOString(),
      })
      .eq('id', subscriptionId)
      .select()
      .single();

    if (error) {
      console.error('toggleAutoRenewAction: Update error:', error);
      return { success: false, error: error.message };
    }

    console.log('toggleAutoRenewAction: Update successful:', data);
    revalidatePath('/dashboard');
    return { success: true, data };
  } catch (error) {
    console.error('Error updating auto-renewal:', error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Failed to update auto-renewal',
    };
  }
}

/**
 * Get subscription payment history
 */
export async function getSubscriptionPaymentHistoryAction(
  subscriptionId: string
): Promise<SubscriptionActionResult> {
  try {
    console.log('getSubscriptionPaymentHistoryAction called with:', {
      subscriptionId,
    });
    console.time('getPaymentHistory-total');

    console.time('getPaymentHistory-auth');
    const { data: user } = await authServer.getCurrentUser();
    console.timeEnd('getPaymentHistory-auth');

    if (!user) {
      console.log(
        'getSubscriptionPaymentHistoryAction: User not authenticated'
      );
      console.timeEnd('getPaymentHistory-total');
      return { success: false, error: 'Unauthorized' };
    }

    console.log(
      'getSubscriptionPaymentHistoryAction: User authenticated:',
      user.id
    );

    console.time('getPaymentHistory-createClient');
    const supabase = await createClient();
    console.timeEnd('getPaymentHistory-createClient');

    // First verify the subscription belongs to the user
    console.time('getPaymentHistory-verifySubscription');
    const { data: subscription, error: subscriptionError } = await supabase
      .from('subscriptions')
      .select('*, subscribers!inner(user_id)')
      .eq('id', subscriptionId)
      .eq('subscribers.user_id', user.id)
      .single();
    console.timeEnd('getPaymentHistory-verifySubscription');

    if (subscriptionError) {
      console.error(
        'getSubscriptionPaymentHistoryAction: Subscription fetch error:',
        subscriptionError
      );
      return {
        success: false,
        error: `Subscription fetch failed: ${subscriptionError.message}`,
      };
    }

    if (!subscription) {
      console.log(
        'getSubscriptionPaymentHistoryAction: Subscription not found'
      );
      return { success: false, error: 'Subscription not found' };
    }

    console.log(
      'getSubscriptionPaymentHistoryAction: Subscription found, fetching payments...'
    );

    // Get payment history for this subscription
    console.time('getPaymentHistory-fetchPayments');
    const { data: payments, error } = await supabase
      .from('payments')
      .select('*')
      .eq('subscription_id', subscriptionId)
      .order('created_at', { ascending: false });
    console.timeEnd('getPaymentHistory-fetchPayments');

    if (error) {
      console.error(
        'getSubscriptionPaymentHistoryAction: Payment fetch error:',
        error
      );
      return { success: false, error: error.message };
    }

    console.log(
      'getSubscriptionPaymentHistoryAction: Found payments:',
      payments?.length || 0
    );
    console.timeEnd('getPaymentHistory-total');
    return { success: true, data: payments || [] };
  } catch (error) {
    console.error('Error fetching payment history:', error);
    console.timeEnd('getPaymentHistory-total');
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Failed to fetch payment history',
    };
  }
}

/**
 * Get all subscription history for the current user
 */
export async function getSubscriptionHistoryAction(): Promise<SubscriptionActionResult> {
  try {
    const { data: user } = await authServer.getCurrentUser();

    if (!user) {
      return { success: false, error: 'Unauthorized' };
    }

    const supabase = await createClient();

    // Get all subscriptions for the user with subscriber info
    const { data: subscriptions, error: subscriptionsError } = await supabase
      .from('subscriptions')
      .select(
        `
        *,
        subscribers!inner(user_id)
      `
      )
      .eq('subscribers.user_id', user.id)
      .order('created_at', { ascending: false });

    if (subscriptionsError) {
      return {
        success: false,
        error: `Failed to fetch subscriptions: ${subscriptionsError.message}`,
      };
    }

    // Fetch payments for each subscription
    const subscriptionsWithPayments = await Promise.all(
      (subscriptions || []).map(async (subscription) => {
        try {
          const { data: payments, error: paymentsError } = await supabase
            .from('payments')
            .select('*')
            .eq('subscription_id', subscription.id)
            .order('created_at', { ascending: false });

          if (paymentsError) {
            return {
              ...subscription,
              payments: [],
            };
          }

          return {
            ...subscription,
            payments: payments || [],
          };
        } catch {
          return {
            ...subscription,
            payments: [],
          };
        }
      })
    );

    return { success: true, data: subscriptionsWithPayments };
  } catch (error) {
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Failed to fetch subscription history',
    };
  }
}
