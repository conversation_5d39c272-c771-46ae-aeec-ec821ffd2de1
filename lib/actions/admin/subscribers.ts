'use server';

import { createSubscribersAdminService } from '@/lib/services/admin/subscribers';
import { PaginationParams, TableFilters, Subscriber } from '@/lib/types/admin';

export async function getSubscribersAction(
  params: PaginationParams,
  filters?: TableFilters
) {
  try {
    const subscribersService = createSubscribersAdminService();
    const result = await subscribersService.getSubscribers(params, filters);

    return {
      success: true,
      data: result.data,
      error: result.error,
    };
  } catch (error) {
    console.error('Error in getSubscribersAction:', error);
    return {
      success: false,
      data: null,
      error:
        error instanceof Error ? error.message : 'Failed to fetch subscribers',
    };
  }
}

export async function getSubscriberAction(id: string) {
  try {
    const subscribersService = createSubscribersAdminService();
    const result = await subscribersService.getSubscriber(id);

    return {
      success: true,
      data: result.data,
      error: result.error,
    };
  } catch (error) {
    console.error('Error in getSubscriberAction:', error);
    return {
      success: false,
      data: null,
      error:
        error instanceof Error ? error.message : 'Failed to fetch subscriber',
    };
  }
}

export async function createSubscriberAction(
  subscriber: Omit<Subscriber, 'id' | 'created_at'>
) {
  try {
    const subscribersService = createSubscribersAdminService();
    const result = await subscribersService.createSubscriber(subscriber);

    return {
      success: true,
      data: result.data,
      error: result.error,
    };
  } catch (error) {
    console.error('Error in createSubscriberAction:', error);
    return {
      success: false,
      data: null,
      error:
        error instanceof Error ? error.message : 'Failed to create subscriber',
    };
  }
}

export async function updateSubscriberAction(
  id: string,
  updates: Partial<Subscriber>
) {
  try {
    const subscribersService = createSubscribersAdminService();
    const result = await subscribersService.updateSubscriber(id, updates);

    return {
      success: true,
      data: result.data,
      error: result.error,
    };
  } catch (error) {
    console.error('Error in updateSubscriberAction:', error);
    return {
      success: false,
      data: null,
      error:
        error instanceof Error ? error.message : 'Failed to update subscriber',
    };
  }
}

export async function deleteSubscriberAction(id: string) {
  try {
    const subscribersService = createSubscribersAdminService();
    const result = await subscribersService.deleteSubscriber(id);

    return {
      success: true,
      data: result.data,
      error: result.error,
    };
  } catch (error) {
    console.error('Error in deleteSubscriberAction:', error);
    return {
      success: false,
      data: false,
      error:
        error instanceof Error ? error.message : 'Failed to delete subscriber',
    };
  }
}

export async function getSubscribersForExportAction(filters?: TableFilters) {
  try {
    const subscribersService = createSubscribersAdminService();
    const result = await subscribersService.getSubscribersForExport(filters);

    return {
      success: true,
      data: result.data,
      error: result.error,
    };
  } catch (error) {
    console.error('Error in getSubscribersForExportAction:', error);
    return {
      success: false,
      data: null,
      error:
        error instanceof Error ? error.message : 'Failed to export subscribers',
    };
  }
}
