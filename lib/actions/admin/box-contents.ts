'use server';

import { createBoxContentsAdminService } from '@/lib/services/admin/box-contents';
import { PaginationParams, TableFilters, BoxContent } from '@/lib/types/admin';

export async function getBoxContentsAction(
  params: PaginationParams,
  filters?: TableFilters
) {
  try {
    const boxContentsService = createBoxContentsAdminService();
    const result = await boxContentsService.getBoxContents(params, filters);
    
    return {
      success: true,
      data: result.data,
      error: result.error,
    };
  } catch (error) {
    console.error('Error in getBoxContentsAction:', error);
    return {
      success: false,
      data: null,
      error: error instanceof Error ? error.message : 'Failed to fetch box contents',
    };
  }
}

export async function getBoxContentAction(id: string) {
  try {
    const boxContentsService = createBoxContentsAdminService();
    const result = await boxContentsService.getBoxContent(id);
    
    return {
      success: true,
      data: result.data,
      error: result.error,
    };
  } catch (error) {
    console.error('Error in getBoxContentAction:', error);
    return {
      success: false,
      data: null,
      error: error instanceof Error ? error.message : 'Failed to fetch box content',
    };
  }
}

export async function createBoxContentAction(
  boxContent: Omit<BoxContent, 'id' | 'created_at'>
) {
  try {
    const boxContentsService = createBoxContentsAdminService();
    const result = await boxContentsService.createBoxContent(boxContent);
    
    return {
      success: true,
      data: result.data,
      error: result.error,
    };
  } catch (error) {
    console.error('Error in createBoxContentAction:', error);
    return {
      success: false,
      data: null,
      error: error instanceof Error ? error.message : 'Failed to create box content',
    };
  }
}

export async function updateBoxContentAction(
  id: string,
  updates: Partial<BoxContent>
) {
  try {
    const boxContentsService = createBoxContentsAdminService();
    const result = await boxContentsService.updateBoxContent(id, updates);
    
    return {
      success: true,
      data: result.data,
      error: result.error,
    };
  } catch (error) {
    console.error('Error in updateBoxContentAction:', error);
    return {
      success: false,
      data: null,
      error: error instanceof Error ? error.message : 'Failed to update box content',
    };
  }
}

export async function deleteBoxContentAction(id: string) {
  try {
    const boxContentsService = createBoxContentsAdminService();
    const result = await boxContentsService.deleteBoxContent(id);
    
    return {
      success: true,
      data: result.data,
      error: result.error,
    };
  } catch (error) {
    console.error('Error in deleteBoxContentAction:', error);
    return {
      success: false,
      data: false,
      error: error instanceof Error ? error.message : 'Failed to delete box content',
    };
  }
}

export async function getCurrentWeekBoxContentAction() {
  try {
    const boxContentsService = createBoxContentsAdminService();
    const result = await boxContentsService.getCurrentWeekBoxContent();
    
    return {
      success: true,
      data: result.data,
      error: result.error,
    };
  } catch (error) {
    console.error('Error in getCurrentWeekBoxContentAction:', error);
    return {
      success: false,
      data: null,
      error: error instanceof Error ? error.message : 'Failed to fetch current week box content',
    };
  }
}

export async function getUpcomingBoxContentsAction() {
  try {
    const boxContentsService = createBoxContentsAdminService();
    const result = await boxContentsService.getUpcomingBoxContents();
    
    return {
      success: true,
      data: result.data,
      error: result.error,
    };
  } catch (error) {
    console.error('Error in getUpcomingBoxContentsAction:', error);
    return {
      success: false,
      data: null,
      error: error instanceof Error ? error.message : 'Failed to fetch upcoming box contents',
    };
  }
}
