'use server';

import { createContactFormsAdminService } from '@/lib/services/admin/contact-forms';
import { PaginationParams, TableFilters } from '@/lib/types/admin';

export async function getContactFormsAction(
  params: PaginationParams,
  filters?: TableFilters
) {
  try {
    const contactFormsService = createContactFormsAdminService();
    const result = await contactFormsService.getContactForms(params, filters);

    return {
      success: true,
      data: result.data,
      error: result.error,
    };
  } catch (error) {
    console.error('Error in getContactFormsAction:', error);
    return {
      success: false,
      data: null,
      error:
        error instanceof Error
          ? error.message
          : 'Failed to fetch contact forms',
    };
  }
}

export async function getContactFormAction(id: string) {
  try {
    const contactFormsService = createContactFormsAdminService();
    const result = await contactFormsService.getContactForm(id);

    return {
      success: true,
      data: result.data,
      error: result.error,
    };
  } catch (error) {
    console.error('Error in getContactFormAction:', error);
    return {
      success: false,
      data: null,
      error:
        error instanceof Error ? error.message : 'Failed to fetch contact form',
    };
  }
}

export async function updateContactFormAction(
  id: string,
  updates: { status?: 'new' | 'read' | 'responded'; notes?: string }
) {
  try {
    const contactFormsService = createContactFormsAdminService();
    const result = await contactFormsService.updateContactForm(id, updates);

    return {
      success: true,
      data: result.data,
      error: result.error,
    };
  } catch (error) {
    console.error('Error in updateContactFormAction:', error);
    return {
      success: false,
      data: null,
      error:
        error instanceof Error
          ? error.message
          : 'Failed to update contact form',
    };
  }
}

export async function deleteContactFormAction(id: string) {
  try {
    const contactFormsService = createContactFormsAdminService();
    const result = await contactFormsService.deleteContactForm(id);

    return {
      success: true,
      data: result.data,
      error: result.error,
    };
  } catch (error) {
    console.error('Error in deleteContactFormAction:', error);
    return {
      success: false,
      data: false,
      error:
        error instanceof Error
          ? error.message
          : 'Failed to delete contact form',
    };
  }
}
