'use server';

import { createDeliveriesAdminService } from '@/lib/services/admin/deliveries';
import { PaginationParams } from '@/lib/types/admin';
import { DeliveryFilters } from '@/lib/types/delivery';

export async function getDeliveriesAction(
  params: PaginationParams,
  filters?: DeliveryFilters
) {
  try {
    const deliveriesService = createDeliveriesAdminService();
    const result = await deliveriesService.getDeliveries(params, filters);

    return {
      success: true,
      data: result.data,
      error: result.error,
    };
  } catch (error) {
    console.error('Error in getDeliveriesAction:', error);
    return {
      success: false,
      data: null,
      error:
        error instanceof Error ? error.message : 'Failed to fetch deliveries',
    };
  }
}

export async function getDeliveryAction(id: string) {
  try {
    const deliveriesService = createDeliveriesAdminService();
    const result = await deliveriesService.getDelivery(id);

    return {
      success: true,
      data: result.data,
      error: result.error,
    };
  } catch (error) {
    console.error('Error in getDeliveryAction:', error);
    return {
      success: false,
      data: null,
      error:
        error instanceof Error ? error.message : 'Failed to fetch delivery',
    };
  }
}

export async function updateDeliveryStatusAction(
  id: string,
  status: 'scheduled' | 'delivered' | 'cancelled',
  reason?: string
) {
  try {
    const deliveriesService = createDeliveriesAdminService();
    const result = await deliveriesService.updateDeliveryStatus(
      id,
      status,
      reason
    );

    return {
      success: true,
      data: result.data,
      error: result.error,
    };
  } catch (error) {
    console.error('Error in updateDeliveryStatusAction:', error);
    return {
      success: false,
      data: null,
      error:
        error instanceof Error
          ? error.message
          : 'Failed to update delivery status',
    };
  }
}

export async function markDeliveryAsDeliveredAction(id: string) {
  try {
    const deliveriesService = createDeliveriesAdminService();
    const result = await deliveriesService.markAsDelivered(id);

    return {
      success: true,
      data: result.data,
      error: result.error,
    };
  } catch (error) {
    console.error('Error in markDeliveryAsDeliveredAction:', error);
    return {
      success: false,
      data: null,
      error:
        error instanceof Error
          ? error.message
          : 'Failed to mark delivery as delivered',
    };
  }
}

export async function cancelDeliveryAction(id: string) {
  try {
    const deliveriesService = createDeliveriesAdminService();
    const result = await deliveriesService.cancelDelivery(id);

    return {
      success: true,
      data: result.data,
      error: result.error,
    };
  } catch (error) {
    console.error('Error in cancelDeliveryAction:', error);
    return {
      success: false,
      data: null,
      error:
        error instanceof Error ? error.message : 'Failed to cancel delivery',
    };
  }
}

export async function rescheduleDeliveryAction(
  id: string,
  newDate: string,
  reason?: string
) {
  try {
    const deliveriesService = createDeliveriesAdminService();
    const result = await deliveriesService.rescheduleDelivery(
      id,
      newDate,
      reason
    );

    return {
      success: true,
      data: result.data,
      error: result.error,
    };
  } catch (error) {
    console.error('Error in rescheduleDeliveryAction:', error);
    return {
      success: false,
      data: null,
      error:
        error instanceof Error
          ? error.message
          : 'Failed to reschedule delivery',
    };
  }
}

export async function getDeliverySummaryAction(
  startDate?: Date,
  endDate?: Date
) {
  try {
    const deliveriesService = createDeliveriesAdminService();
    const result = await deliveriesService.getDeliverySummary(
      startDate,
      endDate
    );

    return {
      success: true,
      data: result.data,
      error: result.error,
    };
  } catch (error) {
    console.error('Error in getDeliverySummaryAction:', error);
    return {
      success: false,
      data: null,
      error:
        error instanceof Error
          ? error.message
          : 'Failed to fetch delivery summary',
    };
  }
}

export async function getDeliveriesForSubscriptionAction(
  subscriptionId: string
) {
  try {
    const deliveriesService = createDeliveriesAdminService();
    const result =
      await deliveriesService.getDeliveriesForSubscription(subscriptionId);

    return {
      success: true,
      data: result.data,
      error: result.error,
    };
  } catch (error) {
    console.error('Error in getDeliveriesForSubscriptionAction:', error);
    return {
      success: false,
      data: null,
      error:
        error instanceof Error
          ? error.message
          : 'Failed to fetch subscription deliveries',
    };
  }
}
