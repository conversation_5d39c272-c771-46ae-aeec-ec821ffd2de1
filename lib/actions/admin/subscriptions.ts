'use server';

import { createSubscriptionsAdminService } from '@/lib/services/admin/subscriptions';
import { PaginationParams, TableFilters, Subscription } from '@/lib/types/admin';

export async function getSubscriptionsAction(
  params: PaginationParams,
  filters?: TableFilters
) {
  try {
    const subscriptionsService = createSubscriptionsAdminService();
    const result = await subscriptionsService.getSubscriptions(params, filters);
    
    return {
      success: true,
      data: result.data,
      error: result.error,
    };
  } catch (error) {
    console.error('Error in getSubscriptionsAction:', error);
    return {
      success: false,
      data: null,
      error: error instanceof Error ? error.message : 'Failed to fetch subscriptions',
    };
  }
}

export async function getSubscriptionAction(id: string) {
  try {
    const subscriptionsService = createSubscriptionsAdminService();
    const result = await subscriptionsService.getSubscription(id);
    
    return {
      success: true,
      data: result.data,
      error: result.error,
    };
  } catch (error) {
    console.error('Error in getSubscriptionAction:', error);
    return {
      success: false,
      data: null,
      error: error instanceof Error ? error.message : 'Failed to fetch subscription',
    };
  }
}

export async function updateSubscriptionAction(
  id: string,
  updates: Partial<Subscription>
) {
  try {
    const subscriptionsService = createSubscriptionsAdminService();
    const result = await subscriptionsService.updateSubscription(id, updates);
    
    return {
      success: true,
      data: result.data,
      error: result.error,
    };
  } catch (error) {
    console.error('Error in updateSubscriptionAction:', error);
    return {
      success: false,
      data: null,
      error: error instanceof Error ? error.message : 'Failed to update subscription',
    };
  }
}

export async function cancelSubscriptionAction(id: string) {
  try {
    const subscriptionsService = createSubscriptionsAdminService();
    const result = await subscriptionsService.cancelSubscription(id);
    
    return {
      success: true,
      data: result.data,
      error: result.error,
    };
  } catch (error) {
    console.error('Error in cancelSubscriptionAction:', error);
    return {
      success: false,
      data: null,
      error: error instanceof Error ? error.message : 'Failed to cancel subscription',
    };
  }
}

export async function pauseSubscriptionAction(id: string, pauseUntil: string) {
  try {
    const subscriptionsService = createSubscriptionsAdminService();
    const result = await subscriptionsService.pauseSubscription(id, pauseUntil);
    
    return {
      success: true,
      data: result.data,
      error: result.error,
    };
  } catch (error) {
    console.error('Error in pauseSubscriptionAction:', error);
    return {
      success: false,
      data: null,
      error: error instanceof Error ? error.message : 'Failed to pause subscription',
    };
  }
}

export async function resumeSubscriptionAction(id: string) {
  try {
    const subscriptionsService = createSubscriptionsAdminService();
    const result = await subscriptionsService.resumeSubscription(id);
    
    return {
      success: true,
      data: result.data,
      error: result.error,
    };
  } catch (error) {
    console.error('Error in resumeSubscriptionAction:', error);
    return {
      success: false,
      data: null,
      error: error instanceof Error ? error.message : 'Failed to resume subscription',
    };
  }
}

export async function processDeliveryAction(id: string) {
  try {
    const subscriptionsService = createSubscriptionsAdminService();
    const result = await subscriptionsService.processDelivery(id);
    
    return {
      success: true,
      data: result.data,
      error: result.error,
    };
  } catch (error) {
    console.error('Error in processDeliveryAction:', error);
    return {
      success: false,
      data: null,
      error: error instanceof Error ? error.message : 'Failed to process delivery',
    };
  }
}
