'use server';

import { createAdminServerService } from '@/lib/services/admin-server';

export async function getChartDataAction() {
  try {
    const adminService = createAdminServerService();

    // Get subscriber growth data (last 6 months)
    const subscriberGrowthResult = await adminService.getSubscriberGrowthData();
    if (subscriberGrowthResult.error) {
      return {
        success: false,
        data: null,
        error: `Subscriber growth: ${subscriberGrowthResult.error}`,
      };
    }

    // Get subscription status distribution
    const subscriptionStatusResult =
      await adminService.getSubscriptionStatusData();
    if (subscriptionStatusResult.error) {
      return {
        success: false,
        data: null,
        error: `Subscription status: ${subscriptionStatusResult.error}`,
      };
    }

    // Get delivery metrics (last 4 weeks)
    const deliveryMetricsResult = await adminService.getDeliveryMetricsData();
    if (deliveryMetricsResult.error) {
      return {
        success: false,
        data: null,
        error: `Delivery metrics: ${deliveryMetricsResult.error}`,
      };
    }

    // Get contact form trends (last 6 months)
    const contactFormsResult = await adminService.getContactFormTrendsData();
    if (contactFormsResult.error) {
      return {
        success: false,
        data: null,
        error: `Contact forms: ${contactFormsResult.error}`,
      };
    }
    return {
      success: true,
      data: {
        subscriberGrowth: subscriberGrowthResult.data || [],
        subscriptionStatus: subscriptionStatusResult.data || [],
        deliveryMetrics: deliveryMetricsResult.data || [],
        contactForms: contactFormsResult.data || [],
      },
      error: null,
    };
  } catch (error) {
    console.error('Error in getChartDataAction:', error);
    return {
      success: false,
      data: null,
      error:
        error instanceof Error ? error.message : 'Failed to fetch chart data',
    };
  }
}
