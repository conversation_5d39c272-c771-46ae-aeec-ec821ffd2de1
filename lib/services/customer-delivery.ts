import { createClient } from '@/lib/supabase/client';
import { Delivery } from '@/lib/types/delivery';

export class CustomerDeliveryService {
  private supabase = createClient();

  /**
   * Get user's upcoming deliveries
   */
  async getUpcomingDeliveries(userId: string): Promise<{
    data: Delivery[] | null;
    error: any;
  }> {
    try {
      const { data, error } = await this.supabase
        .from('deliveries')
        .select(`
          *,
          subscriptions!inner (
            id,
            box_size,
            frequency,
            subscribers!inner (
              user_id
            )
          )
        `)
        .eq('subscriptions.subscribers.user_id', userId)
        .eq('status', 'scheduled')
        .gte('delivery_date', new Date().toISOString().split('T')[0])
        .order('delivery_date', { ascending: true })
        .limit(5);

      return { data, error };
    } catch (error) {
      return { data: null, error };
    }
  }

  /**
   * Get user's delivery history
   */
  async getDeliveryHistory(
    userId: string,
    limit = 10,
    offset = 0
  ): Promise<{
    data: Delivery[] | null;
    error: any;
    count: number | null;
  }> {
    try {
      const { data, error, count } = await this.supabase
        .from('deliveries')
        .select(`
          *,
          subscriptions!inner (
            id,
            box_size,
            frequency,
            subscribers!inner (
              user_id
            )
          )
        `, { count: 'exact' })
        .eq('subscriptions.subscribers.user_id', userId)
        .order('delivery_date', { ascending: false })
        .range(offset, offset + limit - 1);

      return { data, error, count };
    } catch (error) {
      return { data: null, error, count: null };
    }
  }

  /**
   * Get next delivery for user
   */
  async getNextDelivery(userId: string): Promise<{
    data: Delivery | null;
    error: any;
  }> {
    try {
      const { data, error } = await this.supabase
        .from('deliveries')
        .select(`
          *,
          subscriptions!inner (
            id,
            box_size,
            frequency,
            pickup_location,
            subscribers!inner (
              user_id
            )
          )
        `)
        .eq('subscriptions.subscribers.user_id', userId)
        .eq('status', 'scheduled')
        .gte('delivery_date', new Date().toISOString().split('T')[0])
        .order('delivery_date', { ascending: true })
        .limit(1)
        .single();

      return { data, error };
    } catch (error) {
      return { data: null, error };
    }
  }

  /**
   * Get delivery statistics for user
   */
  async getUserDeliveryStats(userId: string): Promise<{
    data: {
      totalDeliveries: number;
      deliveredCount: number;
      scheduledCount: number;
      nextDeliveryDate: string | null;
    } | null;
    error: any;
  }> {
    try {
      // Get all deliveries for user
      const { data: allDeliveries, error: allError } = await this.supabase
        .from('deliveries')
        .select(`
          status,
          delivery_date,
          subscriptions!inner (
            subscribers!inner (
              user_id
            )
          )
        `)
        .eq('subscriptions.subscribers.user_id', userId);

      if (allError) {
        return { data: null, error: allError };
      }

      // Get next delivery
      const { data: nextDelivery, error: nextError } = await this.getNextDelivery(userId);

      if (nextError && nextError.code !== 'PGRST116') { // Ignore "no rows" error
        return { data: null, error: nextError };
      }

      const totalDeliveries = allDeliveries?.length || 0;
      const deliveredCount = allDeliveries?.filter(d => d.status === 'delivered').length || 0;
      const scheduledCount = allDeliveries?.filter(d => d.status === 'scheduled').length || 0;

      return {
        data: {
          totalDeliveries,
          deliveredCount,
          scheduledCount,
          nextDeliveryDate: nextDelivery?.delivery_date || null,
        },
        error: null,
      };
    } catch (error) {
      return { data: null, error };
    }
  }

  /**
   * Get deliveries for a specific subscription
   */
  async getSubscriptionDeliveries(
    subscriptionId: string,
    userId: string
  ): Promise<{
    data: Delivery[] | null;
    error: any;
  }> {
    try {
      const { data, error } = await this.supabase
        .from('deliveries')
        .select(`
          *,
          subscriptions!inner (
            id,
            box_size,
            frequency,
            subscribers!inner (
              user_id
            )
          )
        `)
        .eq('subscription_id', subscriptionId)
        .eq('subscriptions.subscribers.user_id', userId)
        .order('delivery_date', { ascending: false });

      return { data, error };
    } catch (error) {
      return { data: null, error };
    }
  }
}

// Export a default instance for client-side usage
export const customerDeliveryService = new CustomerDeliveryService();

// Export a factory function
export const createCustomerDeliveryService = () => new CustomerDeliveryService();
