import { calculatePrice } from '@/lib/constants/subscription';
import { stripe } from '@/lib/stripe/server-config';
import {
  CreatePaymentIntentData,
  PaymentError,
  PaymentIntentResponse,
  PaymentStatus,
} from '@/lib/types/payment';
import Stripe from 'stripe';

export class StripePaymentService {
  /**
   * Create a payment intent for subscription
   */
  async createPaymentIntent(data: CreatePaymentIntentData): Promise<{
    data: PaymentIntentResponse | null;
    error: PaymentError | null;
  }> {
    try {
      // Use shareSize with fallback to boxSize for backward compatibility
      const shareSize =
        data.subscriptionData.shareSize ||
        data.subscriptionData.boxSize ||
        'standard';

      let pricing;
      try {
        pricing = calculatePrice(
          shareSize,
          data.subscriptionData.frequency,
          data.subscriptionData.paymentPlan
        );
      } catch (error) {
        return {
          data: null,
          error: {
            code: 'invalid_subscription_data',
            message: `Invalid subscription data: ${error instanceof Error ? error.message : 'Unknown error'}`,
            type: 'validation_error',
          },
        };
      }

      const paymentIntent = await stripe.paymentIntents.create({
        amount: pricing.totalPriceCents,
        currency: data.currency.toLowerCase(),
        automatic_payment_methods: {
          enabled: true,
        },
        metadata: {
          subscriberId: data.subscriberId,
          boxSize: shareSize,
          frequency: data.subscriptionData.frequency,
          paymentPlan: data.subscriptionData.paymentPlan,
          deliveryType: data.subscriptionData.deliveryType,
          pickupLocation: data.subscriptionData.pickupLocation,
          ...data.metadata,
        },
        description: `AsedaFoods Subscription - ${shareSize} box, ${data.subscriptionData.frequency} delivery`,
      });

      return {
        data: {
          clientSecret: paymentIntent.client_secret!,
          paymentIntentId: paymentIntent.id,
          amount: paymentIntent.amount,
          currency: paymentIntent.currency,
        },
        error: null,
      };
    } catch (error) {
      console.error('Error creating payment intent:', error);

      if (error instanceof Stripe.errors.StripeError) {
        return {
          data: null,
          error: {
            code: error.code || 'unknown_error',
            message: error.message,
            type: error.type as any,
            param: error.param,
          },
        };
      }

      return {
        data: null,
        error: {
          code: 'unknown_error',
          message: 'An unexpected error occurred while creating payment intent',
          type: 'api_error',
        },
      };
    }
  }

  /**
   * Retrieve payment intent
   */
  async getPaymentIntent(paymentIntentId: string): Promise<{
    data: Stripe.PaymentIntent | null;
    error: PaymentError | null;
  }> {
    try {
      const paymentIntent =
        await stripe.paymentIntents.retrieve(paymentIntentId);
      return { data: paymentIntent, error: null };
    } catch (error) {
      console.error('Error retrieving payment intent:', error);

      if (error instanceof Stripe.errors.StripeError) {
        return {
          data: null,
          error: {
            code: error.code || 'unknown_error',
            message: error.message,
            type: error.type as any,
            param: error.param,
          },
        };
      }

      return {
        data: null,
        error: {
          code: 'unknown_error',
          message:
            'An unexpected error occurred while retrieving payment intent',
          type: 'api_error',
        },
      };
    }
  }

  /**
   * Confirm payment intent
   */
  async confirmPaymentIntent(
    paymentIntentId: string,
    paymentMethodId?: string
  ): Promise<{
    data: Stripe.PaymentIntent | null;
    error: PaymentError | null;
  }> {
    try {
      const confirmData: Stripe.PaymentIntentConfirmParams = {};

      if (paymentMethodId) {
        confirmData.payment_method = paymentMethodId;
      }

      const paymentIntent = await stripe.paymentIntents.confirm(
        paymentIntentId,
        confirmData
      );

      return { data: paymentIntent, error: null };
    } catch (error) {
      console.error('Error confirming payment intent:', error);

      if (error instanceof Stripe.errors.StripeError) {
        return {
          data: null,
          error: {
            code: error.code || 'unknown_error',
            message: error.message,
            type: error.type as any,
            param: error.param,
          },
        };
      }

      return {
        data: null,
        error: {
          code: 'unknown_error',
          message: 'An unexpected error occurred while confirming payment',
          type: 'api_error',
        },
      };
    }
  }

  /**
   * Cancel payment intent
   */
  async cancelPaymentIntent(paymentIntentId: string): Promise<{
    data: Stripe.PaymentIntent | null;
    error: PaymentError | null;
  }> {
    try {
      const paymentIntent = await stripe.paymentIntents.cancel(paymentIntentId);
      return { data: paymentIntent, error: null };
    } catch (error) {
      console.error('Error canceling payment intent:', error);

      if (error instanceof Stripe.errors.StripeError) {
        return {
          data: null,
          error: {
            code: error.code || 'unknown_error',
            message: error.message,
            type: error.type as any,
            param: error.param,
          },
        };
      }

      return {
        data: null,
        error: {
          code: 'unknown_error',
          message: 'An unexpected error occurred while canceling payment',
          type: 'api_error',
        },
      };
    }
  }

  /**
   * Convert Stripe payment intent status to our payment status
   */
  mapStripeStatusToPaymentStatus(stripeStatus: string): PaymentStatus {
    switch (stripeStatus) {
      case 'requires_payment_method':
        return 'requires_payment_method';
      case 'requires_confirmation':
      case 'requires_action':
        return 'requires_action';
      case 'processing':
        return 'processing';
      case 'succeeded':
        return 'succeeded';
      case 'canceled':
        return 'canceled';
      default:
        return 'failed';
    }
  }
}

// Export singleton instance
export const stripePaymentService = new StripePaymentService();
