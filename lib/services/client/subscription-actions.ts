'use client';

/**
 * Client-side subscription action services
 * These services handle user actions and MUST keep loading states for user feedback
 */

export interface SubscriptionUpdate {
  box_size?: string;
  frequency?: string;
  pickup_location?: string;
  auto_renew?: boolean;
}

export interface CreateSubscriptionData {
  subscriber_id: string;
  box_size: string;
  frequency: string;
  pickup_location: string;
  payment_plan: string;
  auto_renew?: boolean;
}

export const subscriptionActions = {
  /**
   * Create a new subscription
   * Loading state needed for form submission feedback
   */
  async createSubscription(data: CreateSubscriptionData) {
    const response = await fetch('/api/subscriptions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to create subscription');
    }

    return response.json();
  },

  /**
   * Update an existing subscription
   * Loading state needed for form submission feedback
   */
  async updateSubscription(subscriptionId: string, updates: SubscriptionUpdate) {
    const response = await fetch(`/api/subscriptions/${subscriptionId}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updates),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update subscription');
    }

    return response.json();
  },

  /**
   * Pause a subscription
   * Loading state needed for user action feedback
   */
  async pauseSubscription(subscriptionId: string, reason?: string) {
    const response = await fetch(`/api/subscriptions/${subscriptionId}/pause`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ reason }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to pause subscription');
    }

    return response.json();
  },

  /**
   * Resume a paused subscription
   * Loading state needed for user action feedback
   */
  async resumeSubscription(subscriptionId: string) {
    const response = await fetch(`/api/subscriptions/${subscriptionId}/resume`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to resume subscription');
    }

    return response.json();
  },

  /**
   * Cancel a subscription
   * Loading state needed for user action feedback
   */
  async cancelSubscription(subscriptionId: string, reason?: string) {
    const response = await fetch(`/api/subscriptions/${subscriptionId}/cancel`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ reason }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to cancel subscription');
    }

    return response.json();
  },

  /**
   * Reactivate a cancelled subscription
   * Loading state needed for user action feedback
   */
  async reactivateSubscription(subscriptionId: string) {
    const response = await fetch(`/api/subscriptions/${subscriptionId}/reactivate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to reactivate subscription');
    }

    return response.json();
  },

  /**
   * Update payment method for subscription
   * Loading state needed for payment processing feedback
   */
  async updatePaymentMethod(subscriptionId: string, paymentMethodId: string) {
    const response = await fetch(`/api/subscriptions/${subscriptionId}/payment-method`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ payment_method_id: paymentMethodId }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update payment method');
    }

    return response.json();
  },

  /**
   * Skip next delivery
   * Loading state needed for user action feedback
   */
  async skipNextDelivery(subscriptionId: string, reason?: string) {
    const response = await fetch(`/api/subscriptions/${subscriptionId}/skip-delivery`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ reason }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to skip delivery');
    }

    return response.json();
  },

  /**
   * Change delivery frequency
   * Loading state needed for user action feedback
   */
  async changeDeliveryFrequency(subscriptionId: string, newFrequency: string) {
    const response = await fetch(`/api/subscriptions/${subscriptionId}/frequency`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ frequency: newFrequency }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to change delivery frequency');
    }

    return response.json();
  },

  /**
   * Change box size
   * Loading state needed for user action feedback
   */
  async changeBoxSize(subscriptionId: string, newBoxSize: string) {
    const response = await fetch(`/api/subscriptions/${subscriptionId}/box-size`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ box_size: newBoxSize }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to change box size');
    }

    return response.json();
  },

  /**
   * Change pickup location
   * Loading state needed for user action feedback
   */
  async changePickupLocation(subscriptionId: string, newLocation: string) {
    const response = await fetch(`/api/subscriptions/${subscriptionId}/pickup-location`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ pickup_location: newLocation }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to change pickup location');
    }

    return response.json();
  },
};
