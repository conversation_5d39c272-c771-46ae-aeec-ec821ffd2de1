import { stripe } from '@/lib/stripe/server-config';
import {
  FIELD_DAY_2025_PRODUCT,
  PAYMENT_STATUSES,
} from '@/lib/field-day-2025-config';
import { PaymentError } from '@/lib/types/payment';
import Strip<PERSON> from 'stripe';

export interface FieldDay2025PaymentIntentData {
  orderId: string;
  orderNumber: string;
  amount: number;
  customerEmail: string;
  customerName: string;
  quantity: number;
  metadata?: Record<string, string>;
}

export interface FieldDay2025PaymentIntentResponse {
  clientSecret: string;
  paymentIntentId: string;
  amount: number;
  currency: string;
}

export class FieldDay2025PaymentService {
  /**
   * Create a payment intent for Field Day 2025 order
   */
  async createPaymentIntent(data: FieldDay2025PaymentIntentData): Promise<{
    data: FieldDay2025PaymentIntentResponse | null;
    error: PaymentError | null;
  }> {
    try {
      console.log('💳 Creating Field Day 2025 payment intent:', {
        orderId: data.orderId,
        orderNumber: data.orderNumber,
        amount: data.amount,
        customerEmail: data.customerEmail,
      });

      const paymentIntent = await stripe.paymentIntents.create({
        amount: data.amount,
        currency: 'usd',
        automatic_payment_methods: {
          enabled: true,
        },
        metadata: {
          orderId: data.orderId,
          orderNumber: data.orderNumber,
          productType: 'field-day-2025',
          productName: FIELD_DAY_2025_PRODUCT.name,
          customerEmail: data.customerEmail,
          customerName: data.customerName,
          quantity: data.quantity.toString(),
          ...data.metadata,
        },
        description: `Field Day 2025 - ${FIELD_DAY_2025_PRODUCT.name} (${data.quantity} box${data.quantity > 1 ? 'es' : ''})`,
        receipt_email: data.customerEmail,
        shipping: {
          name: data.customerName,
          address: {
            line1: 'Pickup at Elite Bodies Fitness',
            city: 'Event Location',
            state: 'TBD',
            postal_code: '00000',
            country: 'US',
          },
        },
      });

      console.log('✅ Payment intent created successfully:', {
        paymentIntentId: paymentIntent.id,
        amount: paymentIntent.amount,
        status: paymentIntent.status,
      });

      return {
        data: {
          clientSecret: paymentIntent.client_secret!,
          paymentIntentId: paymentIntent.id,
          amount: paymentIntent.amount,
          currency: paymentIntent.currency,
        },
        error: null,
      };
    } catch (error) {
      console.error('❌ Error creating Field Day 2025 payment intent:', error);

      if (error instanceof Stripe.errors.StripeError) {
        return {
          data: null,
          error: {
            code: error.code || 'unknown_error',
            message: error.message,
            type: error.type as any,
            param: error.param,
          },
        };
      }

      return {
        data: null,
        error: {
          code: 'unknown_error',
          message: 'An unexpected error occurred while creating payment intent',
          type: 'api_error',
        },
      };
    }
  }

  /**
   * Retrieve a payment intent
   */
  async getPaymentIntent(paymentIntentId: string): Promise<{
    data: Stripe.PaymentIntent | null;
    error: PaymentError | null;
  }> {
    try {
      const paymentIntent =
        await stripe.paymentIntents.retrieve(paymentIntentId);
      return { data: paymentIntent, error: null };
    } catch (error) {
      console.error('❌ Error retrieving payment intent:', error);

      if (error instanceof Stripe.errors.StripeError) {
        return {
          data: null,
          error: {
            code: error.code || 'unknown_error',
            message: error.message,
            type: error.type as any,
            param: error.param,
          },
        };
      }

      return {
        data: null,
        error: {
          code: 'unknown_error',
          message:
            'An unexpected error occurred while retrieving payment intent',
          type: 'api_error',
        },
      };
    }
  }

  /**
   * Cancel a payment intent
   */
  async cancelPaymentIntent(paymentIntentId: string): Promise<{
    data: Stripe.PaymentIntent | null;
    error: PaymentError | null;
  }> {
    try {
      const paymentIntent = await stripe.paymentIntents.cancel(paymentIntentId);

      console.log('🚫 Payment intent cancelled:', {
        paymentIntentId: paymentIntent.id,
        status: paymentIntent.status,
      });

      return { data: paymentIntent, error: null };
    } catch (error) {
      console.error('❌ Error cancelling payment intent:', error);

      if (error instanceof Stripe.errors.StripeError) {
        return {
          data: null,
          error: {
            code: error.code || 'unknown_error',
            message: error.message,
            type: error.type as any,
            param: error.param,
          },
        };
      }

      return {
        data: null,
        error: {
          code: 'unknown_error',
          message:
            'An unexpected error occurred while cancelling payment intent',
          type: 'api_error',
        },
      };
    }
  }

  /**
   * Convert Stripe payment intent status to Field Day 2025 payment status
   */
  mapStripeStatusToFieldDay2025Status(stripeStatus: string): string {
    switch (stripeStatus) {
      case 'requires_payment_method':
      case 'requires_confirmation':
        return PAYMENT_STATUSES.PENDING;
      case 'requires_action':
        return PAYMENT_STATUSES.REQUIRES_ACTION;
      case 'processing':
        return PAYMENT_STATUSES.PENDING;
      case 'succeeded':
        return PAYMENT_STATUSES.SUCCEEDED;
      case 'canceled':
        return PAYMENT_STATUSES.CANCELED;
      default:
        return PAYMENT_STATUSES.FAILED;
    }
  }

  /**
   * Extract order ID from payment intent metadata
   */
  extractOrderIdFromPaymentIntent(
    paymentIntent: Stripe.PaymentIntent
  ): string | null {
    return paymentIntent.metadata?.orderId || null;
  }

  /**
   * Validate that payment intent is for Field Day 2025
   */
  isFieldDay2025PaymentIntent(paymentIntent: Stripe.PaymentIntent): boolean {
    return paymentIntent.metadata?.productType === 'field-day-2025';
  }

  /**
   * Create refund for Field Day 2025 order
   */
  async createRefund(
    paymentIntentId: string,
    amount?: number,
    reason?: string
  ): Promise<{
    data: Stripe.Refund | null;
    error: PaymentError | null;
  }> {
    try {
      const refundData: Stripe.RefundCreateParams = {
        payment_intent: paymentIntentId,
        reason: 'requested_by_customer',
      };

      if (amount) {
        refundData.amount = amount;
      }

      if (reason) {
        refundData.metadata = { reason };
      }

      const refund = await stripe.refunds.create(refundData);

      console.log('💰 Refund created for Field Day 2025 order:', {
        refundId: refund.id,
        paymentIntentId,
        amount: refund.amount,
        status: refund.status,
      });

      return { data: refund, error: null };
    } catch (error) {
      console.error('❌ Error creating refund:', error);

      if (error instanceof Stripe.errors.StripeError) {
        return {
          data: null,
          error: {
            code: error.code || 'unknown_error',
            message: error.message,
            type: error.type as any,
            param: error.param,
          },
        };
      }

      return {
        data: null,
        error: {
          code: 'unknown_error',
          message: 'An unexpected error occurred while creating refund',
          type: 'api_error',
        },
      };
    }
  }
}

// Export singleton instance
export const fieldDay2025PaymentService = new FieldDay2025PaymentService();
