import { authClient, type SignInData, type SignUpData } from '@/lib/utils/auth';

export const authService = {
  async signIn(email: string, password: string) {
    const credentials: SignInData = { email, password };
    const result = await authClient.signIn(credentials);
    
    if (result.error) {
      return { error: result.error.message };
    }
    
    return { data: result.data, error: null };
  },

  async signUp(email: string, password: string, name: string) {
    const userData: SignUpData = { email, password, name };
    const result = await authClient.signUp(userData);
    
    if (result.error) {
      return { error: result.error.message };
    }
    
    return { data: result.data, error: null };
  },

  async signOut() {
    const result = await authClient.signOut();
    
    if (result.error) {
      return { error: result.error.message };
    }
    
    return { error: null };
  },

  async getCurrentUser() {
    const result = await authClient.getCurrentUser();
    
    if (result.error) {
      return { data: null, error: result.error.message };
    }
    
    return { data: result.data, error: null };
  }
};
