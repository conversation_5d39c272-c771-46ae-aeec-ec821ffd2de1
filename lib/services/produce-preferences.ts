import { createClient } from '@/lib/supabase/client';
import {
  ProduceItem,
  UserProducePreference,
  UserProducePreferenceInsert,
  ProduceItemWithPreference,
  UserProducePreferenceWithItem,
  PreferenceType,
  ProduceCategory,
} from '@/lib/types/produce';

export class ProducePreferencesService {
  private supabase = createClient();

  /**
   * Get all produce items with user preferences
   */
  async getProduceItemsWithPreferences(
    userId: string,
    category?: ProduceCategory
  ): Promise<{ data: ProduceItemWithPreference[] | null; error: any }> {
    try {
      let query = this.supabase
        .from('produce_items')
        .select(
          `
          *,
          user_produce_preferences!left(*)
        `
        )
        .eq('user_produce_preferences.user_id', userId)
        .order('name');

      if (category) {
        query = query.eq('category', category);
      }

      const { data, error } = await query;

      if (error) {
        return { data: null, error };
      }

      // Transform the data to include preference flags
      const transformedData = data?.map((item: any) => ({
        ...item,
        is_never_send:
          item.user_produce_preferences?.some(
            (pref: any) => pref.preference_type === 'never_send'
          ) || false,
        is_preferred:
          item.user_produce_preferences?.some(
            (pref: any) => pref.preference_type === 'preferred'
          ) || false,
        user_preference: item.user_produce_preferences?.[0] || null,
      }));

      return { data: transformedData, error: null };
    } catch (error) {
      return { data: null, error };
    }
  }

  /**
   * Get all produce items (without user preferences)
   */
  async getProduceItems(
    category?: ProduceCategory
  ): Promise<{ data: ProduceItem[] | null; error: any }> {
    try {
      let query = this.supabase.from('produce_items').select('*').order('name');

      if (category) {
        query = query.eq('category', category);
      }

      const { data, error } = await query;
      return { data, error };
    } catch (error) {
      return { data: null, error };
    }
  }

  /**
   * Get user's produce preferences
   */
  async getUserPreferences(
    userId: string,
    preferenceType?: PreferenceType
  ): Promise<{ data: UserProducePreferenceWithItem[] | null; error: any }> {
    try {
      let query = this.supabase
        .from('user_produce_preferences')
        .select(
          `
          *,
          produce_items(*)
        `
        )
        .eq('user_id', userId);

      if (preferenceType) {
        query = query.eq('preference_type', preferenceType);
      }

      const { data, error } = await query;
      return { data, error };
    } catch (error) {
      return { data: null, error };
    }
  }

  /**
   * Get user's "never send" items
   */
  async getNeverSendItems(
    userId: string
  ): Promise<{ data: UserProducePreferenceWithItem[] | null; error: any }> {
    return this.getUserPreferences(userId, 'never_send');
  }

  /**
   * Add a "never send" preference
   */
  async addNeverSendItem(
    userId: string,
    produceItemId: string
  ): Promise<{ data: UserProducePreference | null; error: any }> {
    try {
      // First check if user already has 3 never send items
      const { data: existingPrefs } = await this.getNeverSendItems(userId);

      if (existingPrefs && existingPrefs.length >= 3) {
        return {
          data: null,
          error: new Error('You can only have up to 3 "never send" items'),
        };
      }

      const preferenceData: UserProducePreferenceInsert = {
        user_id: userId,
        produce_item_id: produceItemId,
        preference_type: 'never_send',
      };

      const { data, error } = await this.supabase
        .from('user_produce_preferences')
        .insert(preferenceData)
        .select()
        .single();

      return { data, error };
    } catch (error) {
      return { data: null, error };
    }
  }

  /**
   * Remove a "never send" preference
   */
  async removeNeverSendItem(
    userId: string,
    produceItemId: string
  ): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await this.supabase
        .from('user_produce_preferences')
        .delete()
        .eq('user_id', userId)
        .eq('produce_item_id', produceItemId)
        .eq('preference_type', 'never_send');

      return { data, error };
    } catch (error) {
      return { data: null, error };
    }
  }

  /**
   * Update user's "never send" items (replace all)
   */
  async updateNeverSendItems(
    userId: string,
    produceItemIds: string[]
  ): Promise<{ data: any; error: any }> {
    try {
      if (produceItemIds.length > 3) {
        return {
          data: null,
          error: new Error('You can only have up to 3 "never send" items'),
        };
      }

      // Remove all existing "never send" preferences
      await this.supabase
        .from('user_produce_preferences')
        .delete()
        .eq('user_id', userId)
        .eq('preference_type', 'never_send');

      // Add new preferences
      if (produceItemIds.length > 0) {
        const preferences: UserProducePreferenceInsert[] = produceItemIds.map(
          (produceItemId) => ({
            user_id: userId,
            produce_item_id: produceItemId,
            preference_type: 'never_send',
          })
        );

        const { data, error } = await this.supabase
          .from('user_produce_preferences')
          .insert(preferences)
          .select();

        return { data, error };
      }

      return { data: [], error: null };
    } catch (error) {
      return { data: null, error };
    }
  }

  /**
   * Get common produce items (frequently appearing items)
   */
  async getCommonProduceItems(): Promise<{
    data: ProduceItem[] | null;
    error: any;
  }> {
    try {
      const { data, error } = await this.supabase
        .from('produce_items')
        .select('*')
        .eq('is_common', true)
        .order('name');

      return { data, error };
    } catch (error) {
      return { data: null, error };
    }
  }
}

// Create a singleton instance
export const producePreferencesService = new ProducePreferencesService();
