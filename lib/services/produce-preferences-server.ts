import { createClient } from '@/lib/supabase/server';
import {
  ProduceItem,
  UserProducePreferenceWithItem,
  PreferenceType,
  ProduceCategory,
} from '@/lib/types/produce';

export class ProducePreferencesServerService {
  private supabase = createClient();

  /**
export class ProducePreferencesServerService {
  private async getSupabase() {
    return createClient();
  }

  async getProduceItemsWithPreferences(
    userId: string,
    category?: ProduceCategory
  ): Promise<{ data: ProduceItemWithPreference[] | null; error: any }> {
    try {
      const supabase = await this.getSupabase();
        .from('produce_items')
        .select(
          `
          *,
          user_produce_preferences!left(user_id)
        `
        )
        .order('name');

      if (category) {
        query = query.eq('category', category);
      }

      const { data, error } = await query;

      if (error) {
        return { data: null, error };
      }

      // Transform the data to include preference flags
      const transformedData = data?.map((item: any) => ({
        ...item,
        is_never_send:
          item.user_produce_preferences?.some(
            (pref: any) => pref.preference_type === 'never_send'
          ) || false,
        is_preferred:
          item.user_produce_preferences?.some(
            (pref: any) => pref.preference_type === 'preferred'
          ) || false,
        user_preference: item.user_produce_preferences?.[0] || null,
      }));

      return { data: transformedData, error: null };
    } catch (error) {
      return { data: null, error };
    }
  }

  /**
   * Get all produce items (without user preferences)
   */
  async getProduceItems(
    category?: ProduceCategory
  ): Promise<{ data: ProduceItem[] | null; error: any }> {
    try {
      const supabase = await this.supabase;
      let query = supabase.from('produce_items').select('*').order('name');

      if (category) {
        query = query.eq('category', category);
      }

      const { data, error } = await query;
      return { data, error };
    } catch (error) {
      return { data: null, error };
    }
  }

  /**
   * Get user's produce preferences
   */
  async getUserPreferences(
    userId: string,
    preferenceType?: PreferenceType
  ): Promise<{ data: UserProducePreferenceWithItem[] | null; error: any }> {
    try {
      const supabase = await this.supabase;
      let query = supabase
        .from('user_produce_preferences')
        .select(
          `
          *,
          produce_items(*)
        `
        )
        .eq('user_id', userId);

      if (preferenceType) {
        query = query.eq('preference_type', preferenceType);
      }

      const { data, error } = await query;
      return { data, error };
    } catch (error) {
      return { data: null, error };
    }
  }

  /**
   * Get user's "never send" items
   */
  async getNeverSendItems(
    userId: string
  ): Promise<{ data: UserProducePreferenceWithItem[] | null; error: any }> {
    return this.getUserPreferences(userId, 'never_send');
  }

  /**
   * Get common produce items (frequently appearing items)
   */
  async getCommonProduceItems(): Promise<{
    data: ProduceItem[] | null;
    error: any;
  }> {
    try {
      const supabase = await this.supabase;
      const { data, error } = await supabase
        .from('produce_items')
        .select('*')
        .eq('is_common', true)
        .order('name');

      return { data, error };
    } catch (error) {
      return { data: null, error };
    }
  }
}

// Factory function to create service instance
export function createProducePreferencesServerService() {
  return new ProducePreferencesServerService();
}
