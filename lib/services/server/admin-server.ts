import { createClient } from '@/lib/supabase/server';

/**
 * Server-side admin services for PPR
 * These services don't need loading states as they're used in server components with Suspense
 */

export async function getAdminStats() {
  const supabase = await createClient();

  try {
    // Get total subscribers
    const { count: totalSubscribers, error: subscribersError } = await supabase
      .from('subscribers')
      .select('*', { count: 'exact', head: true });

    if (subscribersError) {
      throw new Error(
        `Failed to fetch subscribers count: ${subscribersError.message}`
      );
    }

    // Get new subscribers this month
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    const { count: newSubscribersThisMonth, error: newSubscribersError } =
      await supabase
        .from('subscribers')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', startOfMonth.toISOString());

    if (newSubscribersError) {
      throw new Error(
        `Failed to fetch new subscribers: ${newSubscribersError.message}`
      );
    }

    // Get active subscriptions
    const { count: activeSubscriptions, error: activeError } = await supabase
      .from('subscriptions')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'active');

    if (activeError) {
      throw new Error(
        `Failed to fetch active subscriptions: ${activeError.message}`
      );
    }

    // Get monthly revenue (this month's payments)
    const { data: payments, error: paymentsError } = await supabase
      .from('payments')
      .select('amount')
      .eq('status', 'succeeded')
      .gte('created_at', startOfMonth.toISOString());

    if (paymentsError) {
      throw new Error(`Failed to fetch payments: ${paymentsError.message}`);
    }

    const monthlyRevenue =
      payments?.reduce((sum, payment) => sum + (payment.amount || 0), 0) || 0;

    // Get pending contact forms (fallback to 0 if table doesn't exist)
    let pendingContactForms = 0;
    try {
      const { count, error: contactError } = await supabase
        .from('contact_submissions')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'pending');

      if (contactError) {
        console.warn('Contact submissions table not found, defaulting to 0');
        pendingContactForms = 0;
      } else {
        pendingContactForms = count || 0;
      }
    } catch {
      console.warn('Contact submissions table not accessible, defaulting to 0');
      pendingContactForms = 0;
    }

    return {
      totalSubscribers: totalSubscribers || 0,
      newSubscribersThisMonth: newSubscribersThisMonth || 0,
      activeSubscriptions: activeSubscriptions || 0,
      monthlyRevenue: monthlyRevenue,
      pendingContactForms: pendingContactForms,
    };
  } catch (error) {
    throw new Error(
      `Failed to fetch admin stats: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getSubscriberStats() {
  const supabase = await createClient();

  try {
    // Get subscriber growth over last 6 months
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const { data: subscriberGrowth, error: growthError } = await supabase
      .from('subscribers')
      .select('created_at')
      .gte('created_at', sixMonthsAgo.toISOString())
      .order('created_at', { ascending: true });

    if (growthError) {
      throw new Error(
        `Failed to fetch subscriber growth: ${growthError.message}`
      );
    }

    // Group by month
    const monthlyGrowth =
      subscriberGrowth?.reduce(
        (acc, subscriber) => {
          if (subscriber.created_at) {
            const month = new Date(subscriber.created_at)
              .toISOString()
              .slice(0, 7); // YYYY-MM
            acc[month] = (acc[month] || 0) + 1;
          }
          return acc;
        },
        {} as Record<string, number>
      ) || {};

    // Get subscription status distribution
    const { data: subscriptionStatus, error: statusError } = await supabase
      .from('subscriptions')
      .select('status');

    if (statusError) {
      throw new Error(
        `Failed to fetch subscription status: ${statusError.message}`
      );
    }

    const statusDistribution =
      subscriptionStatus?.reduce(
        (acc, subscription) => {
          if (subscription.status) {
            acc[subscription.status] = (acc[subscription.status] || 0) + 1;
          }
          return acc;
        },
        {} as Record<string, number>
      ) || {};

    return {
      monthlyGrowth,
      statusDistribution,
    };
  } catch (error) {
    throw new Error(
      `Failed to fetch subscriber stats: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getRevenueStats() {
  const supabase = await createClient();

  try {
    // Get revenue over last 6 months
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const { data: payments, error: paymentsError } = await supabase
      .from('payments')
      .select('amount, created_at')
      .eq('status', 'succeeded')
      .gte('created_at', sixMonthsAgo.toISOString())
      .order('created_at', { ascending: true });

    if (paymentsError) {
      throw new Error(
        `Failed to fetch revenue stats: ${paymentsError.message}`
      );
    }

    // Group by month
    const monthlyRevenue =
      payments?.reduce(
        (acc, payment) => {
          if (payment.created_at) {
            const month = new Date(payment.created_at)
              .toISOString()
              .slice(0, 7); // YYYY-MM
            acc[month] = (acc[month] || 0) + (payment.amount || 0);
          }
          return acc;
        },
        {} as Record<string, number>
      ) || {};

    return {
      monthlyRevenue,
    };
  } catch (error) {
    throw new Error(
      `Failed to fetch revenue stats: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getDeliveryMetrics() {
  const supabase = await createClient();

  try {
    // Get delivery stats for last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const { data: deliveries, error: deliveriesError } = await supabase
      .from('deliveries')
      .select('status, delivery_date')
      .gte('delivery_date', thirtyDaysAgo.toISOString().split('T')[0]);

    if (deliveriesError) {
      throw new Error(
        `Failed to fetch delivery metrics: ${deliveriesError.message}`
      );
    }

    const deliveryStats =
      deliveries?.reduce(
        (acc, delivery) => {
          acc[delivery.status] = (acc[delivery.status] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>
      ) || {};

    return {
      deliveryStats,
      totalDeliveries: deliveries?.length || 0,
    };
  } catch (error) {
    throw new Error(
      `Failed to fetch delivery metrics: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}
