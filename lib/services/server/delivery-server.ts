import { createClient } from '@/lib/supabase/server';
import { authServer } from '@/lib/utils/auth-server';

/**
 * Server-side delivery services for PPR
 * These services don't need loading states as they're used in server components with Suspense
 */

export async function getDeliveries(params: {
  page?: number;
  pageSize?: number;
  status?: string;
  subscriberId?: string;
}) {
  const supabase = await createClient();
  const { page = 1, pageSize = 10, status, subscriberId } = params;
  const offset = (page - 1) * pageSize;

  let query = supabase
    .from('deliveries')
    .select(`
      *,
      subscriptions (
        id,
        box_size,
        frequency,
        subscribers (
          id,
          name,
          email
        )
      )
    `)
    .order('delivery_date', { ascending: false })
    .range(offset, offset + pageSize - 1);

  if (status) {
    query = query.eq('status', status);
  }

  if (subscriberId) {
    query = query.eq('subscriptions.subscriber_id', subscriberId);
  }

  const { data, error, count } = await query;

  if (error) {
    throw new Error(`Failed to fetch deliveries: ${error.message}`);
  }

  return {
    deliveries: data || [],
    totalCount: count || 0,
    page,
    pageSize,
    totalPages: Math.ceil((count || 0) / pageSize),
  };
}

export async function getDeliveryById(id: string) {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from('deliveries')
    .select(`
      *,
      subscriptions (
        id,
        box_size,
        frequency,
        pickup_location,
        subscribers (
          id,
          name,
          email
        )
      )
    `)
    .eq('id', id)
    .single();

  if (error) {
    throw new Error(`Failed to fetch delivery: ${error.message}`);
  }

  return data;
}

export async function getDeliveryStats() {
  const supabase = await createClient();

  // Get delivery counts by status
  const { data: statusCounts, error: statusError } = await supabase
    .from('deliveries')
    .select('status')
    .gte('delivery_date', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString());

  if (statusError) {
    throw new Error(`Failed to fetch delivery stats: ${statusError.message}`);
  }

  const stats = {
    scheduled: 0,
    delivered: 0,
    cancelled: 0,
    total: statusCounts?.length || 0,
  };

  statusCounts?.forEach(delivery => {
    if (delivery.status in stats) {
      stats[delivery.status as keyof typeof stats]++;
    }
  });

  return stats;
}

export async function getUserDeliveries(userId: string, limit = 10) {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from('deliveries')
    .select(`
      *,
      subscriptions!inner (
        id,
        box_size,
        frequency,
        subscribers!inner (
          id,
          name,
          email,
          user_id
        )
      )
    `)
    .eq('subscriptions.subscribers.user_id', userId)
    .order('delivery_date', { ascending: false })
    .limit(limit);

  if (error) {
    throw new Error(`Failed to fetch user deliveries: ${error.message}`);
  }

  return data || [];
}

export async function getCurrentUserDeliveries(limit = 10) {
  const { data: user } = await authServer.getCurrentUser();
  
  if (!user) {
    throw new Error('User not authenticated');
  }

  return getUserDeliveries(user.id, limit);
}

export async function getUpcomingDeliveries(userId: string) {
  const supabase = await createClient();
  const today = new Date().toISOString().split('T')[0];

  const { data, error } = await supabase
    .from('deliveries')
    .select(`
      *,
      subscriptions!inner (
        id,
        box_size,
        frequency,
        pickup_location,
        subscribers!inner (
          id,
          name,
          email,
          user_id
        )
      )
    `)
    .eq('subscriptions.subscribers.user_id', userId)
    .eq('status', 'scheduled')
    .gte('delivery_date', today)
    .order('delivery_date', { ascending: true })
    .limit(5);

  if (error) {
    throw new Error(`Failed to fetch upcoming deliveries: ${error.message}`);
  }

  return data || [];
}

export async function getCurrentUserUpcomingDeliveries() {
  const { data: user } = await authServer.getCurrentUser();
  
  if (!user) {
    throw new Error('User not authenticated');
  }

  return getUpcomingDeliveries(user.id);
}
