import { AdminServerService } from '../admin-server';
import { PaginationParams, TableFilters, AdminResponse, NewsletterSubscription } from '@/lib/types/admin';

export class NewsletterAdminService extends AdminServerService {
  /**
   * Get paginated newsletter subscriptions
   */
  async getNewsletterSubscriptions(
    params: PaginationParams,
    filters?: TableFilters
  ): Promise<AdminResponse<{ data: NewsletterSubscription[]; count: number }>> {
    try {
      const supabase = await this.getSupabase();

      let query = supabase
        .from('newsletter_subscriptions')
        .select('*', { count: 'exact' });

      // Apply search filter (search by email)
      if (filters?.search && filters.search.trim() !== '') {
        query = query.ilike('email', `%${filters.search}%`);
      }

      // Apply active filter
      if (filters?.status) {
        const isActive = filters.status === 'active';
        query = query.eq('is_active', isActive);
      }

      // Apply date range filter
      if (filters?.dateRange) {
        query = query
          .gte('created_at', filters.dateRange.from.toISOString())
          .lte('created_at', filters.dateRange.to.toISOString());
      }

      // Apply sorting
      if (params.sortBy) {
        query = query.order(params.sortBy, {
          ascending: params.sortOrder === 'asc',
        });
      } else {
        // Default sort by created_at desc
        query = query.order('created_at', { ascending: false });
      }

      // Apply pagination
      const from = (params.page - 1) * params.pageSize;
      const to = from + params.pageSize - 1;
      query = query.range(from, to);

      const { data, error, count } = await query;

      if (error) {
        return { data: null, error: error.message };
      }

      return { data: { data: data || [], count: count || 0 }, error: null };
    } catch (error) {
      console.error('Error fetching newsletter subscriptions:', error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to fetch newsletter subscriptions',
      };
    }
  }

  /**
   * Get newsletter subscription stats
   */
  async getNewsletterStats(): Promise<AdminResponse<{
    totalSubscriptions: number;
    activeSubscriptions: number;
    newThisMonth: number;
  }>> {
    try {
      const supabase = await this.getSupabase();

      // Get total subscriptions
      const { count: totalSubscriptions } = await supabase
        .from('newsletter_subscriptions')
        .select('*', { count: 'exact', head: true });

      // Get active subscriptions
      const { count: activeSubscriptions } = await supabase
        .from('newsletter_subscriptions')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true);

      // Get new subscriptions this month
      const startOfMonth = new Date();
      startOfMonth.setDate(1);
      startOfMonth.setHours(0, 0, 0, 0);

      const { count: newThisMonth } = await supabase
        .from('newsletter_subscriptions')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', startOfMonth.toISOString());

      return {
        data: {
          totalSubscriptions: totalSubscriptions || 0,
          activeSubscriptions: activeSubscriptions || 0,
          newThisMonth: newThisMonth || 0,
        },
        error: null,
      };
    } catch (error) {
      console.error('Error fetching newsletter stats:', error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to fetch newsletter stats',
      };
    }
  }
}

export function createNewsletterAdminService() {
  return new NewsletterAdminService();
}
