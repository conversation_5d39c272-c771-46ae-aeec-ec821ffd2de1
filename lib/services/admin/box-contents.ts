import {
  BoxContent,
  AdminResponse,
  PaginationParams,
  TableFilters,
} from '@/lib/types/admin';
import { AdminServerService } from '../admin-server';

export class BoxContentsAdminService extends AdminServerService {
  /**
   * Get paginated box contents
   */
  async getBoxContents(
    params: PaginationParams,
    filters?: TableFilters
  ): Promise<AdminResponse<{ data: BoxContent[]; count: number }>> {
    try {
      const supabase = await this.getSupabase();

      let query = supabase
        .from('box_contents')
        .select('*', { count: 'exact' });

      // Apply search filter (search by contents)
      if (filters?.search) {
        query = query.ilike('contents', `%${filters.search}%`);
      }

      // Apply date range filter
      if (filters?.dateRange) {
        query = query
          .gte('week_start_date', filters.dateRange.from.toISOString().split('T')[0])
          .lte('week_start_date', filters.dateRange.to.toISOString().split('T')[0]);
      }

      // Apply sorting
      if (params.sortBy) {
        query = query.order(params.sortBy, {
          ascending: params.sortOrder === 'asc',
        });
      } else {
        query = query.order('week_start_date', { ascending: false });
      }

      // Apply pagination
      const from = (params.page - 1) * params.pageSize;
      const to = from + params.pageSize - 1;

      const { data, error, count } = await query.range(from, to);

      if (error) {
        return { data: null, error: error.message };
      }

      return {
        data: { data: data || [], count: count || 0 },
        error: null,
      };
    } catch (error) {
      console.error('Error fetching box contents:', error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to fetch box contents',
      };
    }
  }

  /**
   * Get single box content
   */
  async getBoxContent(id: string): Promise<AdminResponse<BoxContent>> {
    try {
      const supabase = await this.getSupabase();

      const { data, error } = await supabase
        .from('box_contents')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Error fetching box content:', error);
      return {
        data: null,
        error:
          error instanceof Error ? error.message : 'Failed to fetch box content',
      };
    }
  }

  /**
   * Create new box content
   */
  async createBoxContent(
    boxContent: Omit<BoxContent, 'id' | 'created_at'>
  ): Promise<AdminResponse<BoxContent>> {
    try {
      const supabase = await this.getSupabase();

      const { data, error } = await supabase
        .from('box_contents')
        .insert(boxContent)
        .select()
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Error creating box content:', error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to create box content',
      };
    }
  }

  /**
   * Update box content
   */
  async updateBoxContent(
    id: string,
    updates: Partial<BoxContent>
  ): Promise<AdminResponse<BoxContent>> {
    try {
      const supabase = await this.getSupabase();

      const { data, error } = await supabase
        .from('box_contents')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Error updating box content:', error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to update box content',
      };
    }
  }

  /**
   * Delete box content
   */
  async deleteBoxContent(id: string): Promise<AdminResponse<boolean>> {
    try {
      const supabase = await this.getSupabase();

      const { error } = await supabase
        .from('box_contents')
        .delete()
        .eq('id', id);

      if (error) {
        return { data: false, error: error.message };
      }

      return { data: true, error: null };
    } catch (error) {
      console.error('Error deleting box content:', error);
      return {
        data: false,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to delete box content',
      };
    }
  }

  /**
   * Get current week's box content
   */
  async getCurrentWeekBoxContent(): Promise<AdminResponse<BoxContent | null>> {
    try {
      const supabase = await this.getSupabase();

      // Get the start of current week (Monday)
      const now = new Date();
      const dayOfWeek = now.getDay();
      const daysToMonday = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
      const monday = new Date(now);
      monday.setDate(now.getDate() + daysToMonday);
      monday.setHours(0, 0, 0, 0);

      const { data, error } = await supabase
        .from('box_contents')
        .select('*')
        .eq('week_start_date', monday.toISOString().split('T')[0])
        .single();

      if (error && error.code !== 'PGRST116') {
        // PGRST116 is "not found" error, which is acceptable
        return { data: null, error: error.message };
      }

      return { data: data || null, error: null };
    } catch (error) {
      console.error('Error fetching current week box content:', error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to fetch current week box content',
      };
    }
  }

  /**
   * Get upcoming box contents (next 4 weeks)
   */
  async getUpcomingBoxContents(): Promise<AdminResponse<BoxContent[]>> {
    try {
      const supabase = await this.getSupabase();

      const today = new Date();
      const fourWeeksFromNow = new Date();
      fourWeeksFromNow.setDate(today.getDate() + 28);

      const { data, error } = await supabase
        .from('box_contents')
        .select('*')
        .gte('week_start_date', today.toISOString().split('T')[0])
        .lte('week_start_date', fourWeeksFromNow.toISOString().split('T')[0])
        .order('week_start_date', { ascending: true });

      if (error) {
        return { data: null, error: error.message };
      }

      return { data: data || [], error: null };
    } catch (error) {
      console.error('Error fetching upcoming box contents:', error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to fetch upcoming box contents',
      };
    }
  }
}

// Create a singleton instance
export const createBoxContentsAdminService = () =>
  new BoxContentsAdminService();
