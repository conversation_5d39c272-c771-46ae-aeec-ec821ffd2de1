'use client';

import { createClient } from '@/lib/supabase/client';
import { toast } from 'sonner';
import { AdminOrderUpdateData } from '@/lib/field-day-2025-config';
import type { Tables, TablesUpdate } from '@/lib/supabase/types';

export type FieldDay2025Order = Tables<'field_day_2025_orders'>;
export type FieldDay2025OrderUpdate = TablesUpdate<'field_day_2025_orders'>;

export interface OrderListResult {
  orders: FieldDay2025Order[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Toast messages for Field Day 2025 orders
const TOAST_MESSAGES = {
  updateOrder: {
    loading: 'Updating order status...',
    success: (action: string) => `Order ${action} successfully! ✅`,
    error: 'Failed to update order. Please try again.',
  },
  loadOrders: {
    loading: 'Loading Field Day 2025 orders...',
    success: (count: number) => `Loaded ${count} orders successfully`,
    error: 'Failed to load orders. Please refresh the page.',
  },
  deleteOrder: {
    loading: 'Deleting order...',
    success: 'Order deleted successfully! 🗑️',
    error: 'Failed to delete order. Please try again.',
  },
} as const;

export class FieldDay2025OrderClientService {
  private supabase = createClient();

  // Admin: Update order with promise toast
  async updateOrderWithToast(
    orderId: string,
    updateData: AdminOrderUpdateData
  ): Promise<any> {
    return toast.promise(
      new Promise<FieldDay2025Order>(async (resolve, reject) => {
        try {
          const result = await this.updateOrder(orderId, updateData);

          if (result.error) {
            reject(new Error(result.error));
          } else if (result.data) {
            resolve(result.data);
          } else {
            reject(new Error('Unknown error occurred'));
          }
        } catch (error) {
          reject(error);
        }
      }),
      {
        loading: TOAST_MESSAGES.updateOrder.loading,
        success: () => TOAST_MESSAGES.updateOrder.success('updated'),
        error: (error) => {
          console.error('Update order error:', error);
          return TOAST_MESSAGES.updateOrder.error;
        },
      }
    );
  }

  // Core order update method (without toast)
  async updateOrder(
    orderId: string,
    updateData: AdminOrderUpdateData
  ): Promise<{
    data: FieldDay2025Order | null;
    error: string | null;
  }> {
    try {
      const updatePayload: FieldDay2025OrderUpdate = {
        order_status: updateData.orderStatus,
        payment_status: updateData.paymentStatus,
        pickup_status: updateData.pickupStatus,
        admin_notes: updateData.adminNotes,
        updated_at: new Date().toISOString(),
      };

      const { data: order, error } = await this.supabase
        .from('field_day_2025_orders')
        .update(updatePayload)
        .eq('id', orderId)
        .select()
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data: order, error: null };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error ? error.message : 'Failed to update order',
      };
    }
  }

  // Admin: Get orders with pagination and filtering
  async getOrdersWithToast(
    params: {
      page?: number;
      limit?: number;
      orderStatus?: string;
      paymentStatus?: string;
      pickupStatus?: string;
      search?: string;
      startDate?: Date | null;
      endDate?: Date | null;
    } = {}
  ): Promise<any> {
    return toast.promise(
      new Promise<OrderListResult>(async (resolve, reject) => {
        try {
          const result = await this.getOrders(params);

          if (result.error) {
            reject(new Error(result.error));
          } else if (result.data) {
            resolve(result.data);
          } else {
            reject(new Error('Unknown error occurred'));
          }
        } catch (error) {
          reject(error);
        }
      }),
      {
        loading: TOAST_MESSAGES.loadOrders.loading,
        success: (result: OrderListResult) =>
          TOAST_MESSAGES.loadOrders.success(result.orders.length),
        error: (error) => {
          console.error('Load orders error:', error);
          return TOAST_MESSAGES.loadOrders.error;
        },
      }
    );
  }

  // Core get orders method (without toast)
  async getOrders(
    params: {
      page?: number;
      limit?: number;
      orderStatus?: string;
      paymentStatus?: string;
      pickupStatus?: string;
      search?: string;
      startDate?: Date | null;
      endDate?: Date | null;
    } = {}
  ): Promise<{
    data: OrderListResult | null;
    error: string | null;
  }> {
    try {
      const {
        page = 1,
        limit = 20,
        orderStatus,
        paymentStatus,
        pickupStatus,
        search,
        startDate,
        endDate,
      } = params;
      const offset = (page - 1) * limit;

      let query = this.supabase
        .from('field_day_2025_orders')
        .select('*', { count: 'exact' });

      // Apply filters
      if (orderStatus) {
        query = query.eq('order_status', orderStatus);
      }

      if (paymentStatus) {
        query = query.eq('payment_status', paymentStatus);
      }

      if (pickupStatus) {
        query = query.eq('pickup_status', pickupStatus);
      }

      if (search) {
        query = query.or(
          `first_name.ilike.%${search}%,last_name.ilike.%${search}%,email.ilike.%${search}%,order_number.ilike.%${search}%`
        );
      }

      if (startDate) {
        query = query.gte('created_at', startDate.toISOString());
      }

      if (endDate) {
        query = query.lte('created_at', endDate.toISOString());
      }

      // Apply pagination and ordering
      const {
        data: orders,
        error,
        count,
      } = await query
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        return { data: null, error: error.message };
      }

      const totalPages = Math.ceil((count || 0) / limit);

      return {
        data: {
          orders: orders || [],
          pagination: {
            page,
            limit,
            total: count || 0,
            totalPages,
          },
        },
        error: null,
      };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error ? error.message : 'Failed to fetch orders',
      };
    }
  }

  // Get single order by ID
  async getOrderById(orderId: string): Promise<{
    data: FieldDay2025Order | null;
    error: string | null;
  }> {
    try {
      const { data: order, error } = await this.supabase
        .from('field_day_2025_orders')
        .select('*')
        .eq('id', orderId)
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data: order, error: null };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to fetch order',
      };
    }
  }

  // Delete order with toast
  async deleteOrderWithToast(orderId: string): Promise<any> {
    return toast.promise(
      new Promise<boolean>(async (resolve, reject) => {
        try {
          const result = await this.deleteOrder(orderId);

          if (result.error) {
            reject(new Error(result.error));
          } else {
            resolve(result.success);
          }
        } catch (error) {
          reject(error);
        }
      }),
      {
        loading: TOAST_MESSAGES.deleteOrder.loading,
        success: () => TOAST_MESSAGES.deleteOrder.success,
        error: (error) => {
          console.error('Delete order error:', error);
          return TOAST_MESSAGES.deleteOrder.error;
        },
      }
    );
  }

  // Core delete order method (without toast)
  async deleteOrder(orderId: string): Promise<{
    success: boolean;
    error: string | null;
  }> {
    try {
      const { error } = await this.supabase
        .from('field_day_2025_orders')
        .delete()
        .eq('id', orderId);

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, error: null };
    } catch (error) {
      return {
        success: false,
        error:
          error instanceof Error ? error.message : 'Failed to delete order',
      };
    }
  }
}

// Export singleton instance
export const fieldDay2025OrderClientActions =
  new FieldDay2025OrderClientService();
