import {
  AdminOrderUpdateData,
  FIELD_DAY_2025_EVENT,
  FIELD_DAY_2025_PRODUCT,
  FieldDay2025OrderData,
  ORDER_STATUSES,
  PAYMENT_STATUSES,
  PICKUP_LOCATIONS,
  PICKUP_STATUSES,
  calculateOrderTotal,
} from '@/lib/field-day-2025-config';
import { EmailService } from '@/lib/services/email';
import { createClient } from '@/lib/supabase/server';
import type { Tables, TablesInsert, TablesUpdate } from '@/lib/supabase/types';
import { toast } from 'sonner';

export type FieldDay2025Order = Tables<'field_day_2025_orders'>;
export type FieldDay2025OrderInsert = TablesInsert<'field_day_2025_orders'>;
export type FieldDay2025OrderUpdate = TablesUpdate<'field_day_2025_orders'>;

export interface CreateOrderResult {
  orderId: string;
  orderNumber: string;
  totalAmountCents: number;
  paymentIntentId?: string;
}

export interface OrderListResult {
  orders: FieldDay2025Order[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Toast messages for Field Day 2025 orders
const TOAST_MESSAGES = {
  createOrder: {
    loading: 'Creating your Field Day 2025 order...',
    success: (orderNumber: string) =>
      `Order ${orderNumber} created successfully! Redirecting to payment... 🎉`,
    error:
      'Failed to create order. Please check your information and try again.',
  },
  updateOrder: {
    loading: 'Updating order status...',
    success: (action: string) => `Order ${action} successfully! ✅`,
    error: 'Failed to update order. Please try again.',
  },
  resendEmail: {
    loading: 'Sending confirmation email...',
    success: (email: string) => `Confirmation email sent to ${email}! 📧`,
    error: 'Failed to send email. Please try again.',
  },
  loadOrders: {
    loading: 'Loading Field Day 2025 orders...',
    success: (count: number) => `Loaded ${count} orders successfully`,
    error: 'Failed to load orders. Please refresh the page.',
  },
} as const;

export class FieldDay2025OrderService {
  private async getSupabase() {
    return await createClient();
  }

  // Create order with promise toast
  async createOrderWithToast(orderData: FieldDay2025OrderData) {
    return toast.promise(
      new Promise<CreateOrderResult>(async (resolve, reject) => {
        try {
          const result = await this.createOrder(orderData);

          if (result.error) {
            reject(new Error(result.error));
          } else if (result.data) {
            resolve(result.data);
          } else {
            reject(new Error('Unknown error occurred'));
          }
        } catch (error) {
          reject(error);
        }
      }),
      {
        loading: TOAST_MESSAGES.createOrder.loading,
        success: (result: CreateOrderResult) =>
          TOAST_MESSAGES.createOrder.success(result.orderNumber),
        error: (error) => {
          console.error('Create order error:', error);
          return TOAST_MESSAGES.createOrder.error;
        },
      }
    );
  }

  // Generate order number
  private generateOrderNumber(): string {
    const timestamp = Date.now().toString().slice(-6); // Last 6 digits of timestamp
    const random = Math.floor(Math.random() * 1000)
      .toString()
      .padStart(3, '0');
    return `FD2025-${timestamp}${random}`;
  }

  // Core order creation method (without toast)
  async createOrder(orderData: FieldDay2025OrderData): Promise<{
    data: CreateOrderResult | null;
    error: string | null;
  }> {
    try {
      const supabase = await this.getSupabase();

      // Calculate totals using helper function
      const { totalAmountCents, taxAmountCents } = calculateOrderTotal(
        orderData.quantity
      );

      // Generate unique order number
      const orderNumber = this.generateOrderNumber();

      const orderInsert: FieldDay2025OrderInsert = {
        product_name: FIELD_DAY_2025_PRODUCT.name,
        quantity: orderData.quantity,
        unit_price_cents: FIELD_DAY_2025_PRODUCT.unitPriceCents,
        total_amount_cents: totalAmountCents,
        tax_amount_cents: taxAmountCents,
        first_name: orderData.firstName,
        last_name: orderData.lastName,
        email: orderData.email,
        phone_number: orderData.phoneNumber,
        payment_status: PAYMENT_STATUSES.PENDING,
        order_status: ORDER_STATUSES.PENDING,
        pickup_status: PICKUP_STATUSES.NOT_READY,
        order_number: orderNumber, // Generated in application
      };

      const { data: order, error } = await supabase
        .from('field_day_2025_orders')
        .insert(orderInsert)
        .select()
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return {
        data: {
          orderId: order.id,
          orderNumber: order.order_number,
          totalAmountCents: order.total_amount_cents,
        },
        error: null,
      };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error ? error.message : 'Failed to create order',
      };
    }
  }

  // Admin: Update order with promise toast
  async updateOrderWithToast(
    orderId: string,
    updateData: AdminOrderUpdateData
    // actionDescription: string = 'updated'
  ): Promise<any> {
    return toast.promise(
      new Promise<FieldDay2025Order>(async (resolve, reject) => {
        try {
          const result = await this.updateOrder(orderId, updateData);

          if (result.error) {
            reject(new Error(result.error));
          } else if (result.data) {
            resolve(result.data);
          } else {
            reject(new Error('Unknown error occurred'));
          }
        } catch (error) {
          reject(error);
        }
      }),
      {
        loading: TOAST_MESSAGES.updateOrder.loading,
        success: () => TOAST_MESSAGES.updateOrder.success('updated'),
        error: (error) => {
          console.error('Update order error:', error);
          return TOAST_MESSAGES.updateOrder.error;
        },
      }
    );
  }

  // Core order update method (without toast)
  async updateOrder(
    orderId: string,
    updateData: AdminOrderUpdateData
  ): Promise<{
    data: FieldDay2025Order | null;
    error: string | null;
  }> {
    try {
      const supabase = await this.getSupabase();

      // Map camelCase properties to snake_case for database
      const updatePayload: FieldDay2025OrderUpdate = {
        order_status: updateData.orderStatus,
        payment_status: updateData.paymentStatus,
        pickup_status: updateData.pickupStatus,
        payment_method: updateData.paymentMethod,
        payment_timestamp: updateData.paymentTimestamp,
        payment_intent_id: updateData.paymentIntentId,
        admin_notes: updateData.adminNotes,
        updated_at: new Date().toISOString(),
      };

      const { data: order, error } = await supabase
        .from('field_day_2025_orders')
        .update(updatePayload)
        .eq('id', orderId)
        .select()
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      // Check if pickup status changed to ready and send notification email
      if (
        updateData.pickupStatus === PICKUP_STATUSES.READY &&
        order.pickup_status === PICKUP_STATUSES.READY &&
        order.payment_status === PAYMENT_STATUSES.SUCCEEDED
      ) {
        try {
          await this.sendPickupReadyEmail(order);
          console.log(
            '📧 Pickup ready email sent for order:',
            order.order_number
          );
        } catch (emailError) {
          console.error('❌ Failed to send pickup ready email:', emailError);
          // Don't fail the update for email errors
        }
      }

      return { data: order, error: null };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error ? error.message : 'Failed to update order',
      };
    }
  }

  // Admin: Get orders with pagination and filtering
  async getOrdersWithToast(
    params: {
      page?: number;
      limit?: number;
      status?: string;
      search?: string;
      startDate?: string;
      endDate?: string;
    } = {}
  ): Promise<any> {
    return toast.promise(
      new Promise<OrderListResult>(async (resolve, reject) => {
        try {
          const result = await this.getOrders(params);

          if (result.error) {
            reject(new Error(result.error));
          } else if (result.data) {
            resolve(result.data);
          } else {
            reject(new Error('Unknown error occurred'));
          }
        } catch (error) {
          reject(error);
        }
      }),
      {
        loading: TOAST_MESSAGES.loadOrders.loading,
        success: (result: OrderListResult) =>
          TOAST_MESSAGES.loadOrders.success(result.orders.length),
        error: (error) => {
          console.error('Load orders error:', error);
          return TOAST_MESSAGES.loadOrders.error;
        },
      }
    );
  }

  // Core get orders method (without toast)
  async getOrders(
    params: {
      page?: number;
      limit?: number;
      status?: string;
      search?: string;
      startDate?: string;
      endDate?: string;
    } = {}
  ): Promise<{
    data: OrderListResult | null;
    error: string | null;
  }> {
    try {
      const supabase = await this.getSupabase();
      const {
        page = 1,
        limit = 20,
        status,
        search,
        startDate,
        endDate,
      } = params;
      const offset = (page - 1) * limit;

      let query = supabase
        .from('field_day_2025_orders')
        .select('*', { count: 'exact' });

      // Apply filters
      if (status) {
        query = query.eq('order_status', status);
      }

      if (search) {
        query = query.or(
          `first_name.ilike.%${search}%,last_name.ilike.%${search}%,email.ilike.%${search}%,order_number.ilike.%${search}%`
        );
      }

      if (startDate) {
        query = query.gte('created_at', startDate);
      }

      if (endDate) {
        query = query.lte('created_at', endDate);
      }

      // Apply pagination and ordering
      const {
        data: orders,
        error,
        count,
      } = await query
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        return { data: null, error: error.message };
      }

      const totalPages = Math.ceil((count || 0) / limit);

      return {
        data: {
          orders: orders || [],
          pagination: {
            page,
            limit,
            total: count || 0,
            totalPages,
          },
        },
        error: null,
      };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error ? error.message : 'Failed to fetch orders',
      };
    }
  }

  // Get single order by ID
  async getOrderById(orderId: string): Promise<{
    data: FieldDay2025Order | null;
    error: string | null;
  }> {
    try {
      const supabase = await this.getSupabase();

      const { data: order, error } = await supabase
        .from('field_day_2025_orders')
        .select('*')
        .eq('id', orderId)
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data: order, error: null };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to fetch order',
      };
    }
  }

  // Get order by payment intent ID
  async getOrderByPaymentIntentId(paymentIntentId: string): Promise<{
    data: FieldDay2025Order | null;
    error: string | null;
  }> {
    try {
      const supabase = await this.getSupabase();

      const { data: order, error } = await supabase
        .from('field_day_2025_orders')
        .select('*')
        .eq('payment_intent_id', paymentIntentId)
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data: order, error: null };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to fetch order',
      };
    }
  }

  // Get order by order number
  async getOrderByOrderNumber(orderNumber: string): Promise<{
    data: FieldDay2025Order | null;
    error: string | null;
  }> {
    try {
      const supabase = await this.getSupabase();

      const { data: order, error } = await supabase
        .from('field_day_2025_orders')
        .select('*')
        .eq('order_number', orderNumber)
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data: order, error: null };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to fetch order',
      };
    }
  }

  // Update payment status (for webhook handling)
  async updatePaymentStatus(
    paymentIntentId: string,
    paymentStatus: string,
    paymentMethod?: string
  ): Promise<{
    data: FieldDay2025Order | null;
    error: string | null;
  }> {
    try {
      const supabase = await this.getSupabase();

      const updatePayload: FieldDay2025OrderUpdate = {
        payment_status: paymentStatus,
        payment_method: paymentMethod,
        payment_timestamp:
          paymentStatus === PAYMENT_STATUSES.SUCCEEDED
            ? new Date().toISOString()
            : undefined,
        order_status:
          paymentStatus === PAYMENT_STATUSES.SUCCEEDED
            ? ORDER_STATUSES.CONFIRMED
            : ORDER_STATUSES.PENDING,
        updated_at: new Date().toISOString(),
      };

      const { data: order, error } = await supabase
        .from('field_day_2025_orders')
        .update(updatePayload)
        .eq('payment_intent_id', paymentIntentId)
        .select()
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data: order, error: null };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to update payment status',
      };
    }
  }

  // Resend confirmation email with toast
  async resendConfirmationEmailWithToast(orderId: string): Promise<any> {
    return toast.promise(
      new Promise<boolean>(async (resolve, reject) => {
        try {
          const result = await this.resendConfirmationEmail(orderId);

          if (result.error) {
            reject(new Error(result.error));
          } else {
            resolve(result.success);
          }
        } catch (error) {
          reject(error);
        }
      }),
      {
        loading: TOAST_MESSAGES.resendEmail.loading,
        success: () => {
          // Get email from order data for success message
          return TOAST_MESSAGES.resendEmail.success('customer');
        },
        error: (error) => {
          console.error('Resend email error:', error);
          return TOAST_MESSAGES.resendEmail.error;
        },
      }
    );
  }

  // Core resend email method (without toast)
  async resendConfirmationEmail(orderId: string): Promise<{
    success: boolean;
    error: string | null;
  }> {
    try {
      // Get order details first
      const orderResult = await this.getOrderById(orderId);

      if (orderResult.error || !orderResult.data) {
        return { success: false, error: 'Order not found' };
      }

      const order = orderResult.data;

      // Check if order is in a valid state for email resend
      if (order.payment_status !== PAYMENT_STATUSES.SUCCEEDED) {
        return {
          success: false,
          error: 'Cannot resend confirmation email for unpaid orders',
        };
      }

      // Prepare email data
      const customerName = `${order.first_name} ${order.last_name}`;
      const orderData = {
        orderNumber: order.order_number,
        orderDate: new Date(order.created_at || new Date()),
        quantity: order.quantity,
        unitPrice: order.unit_price_cents,
        totalAmount: order.total_amount_cents,
        taxAmount: order.tax_amount_cents || 0,
        productName: order.product_name,
      };

      const pickupLocation = PICKUP_LOCATIONS[0]; // Use first pickup location
      const pickupData = {
        eventDate: FIELD_DAY_2025_EVENT.date,
        location: pickupLocation.name,
        address: pickupLocation.address,
        contactInfo: pickupLocation.contactInfo,
      };

      // Send confirmation email
      await EmailService.sendFieldDay2025OrderConfirmation(
        order.email,
        customerName,
        orderData,
        pickupData
      );

      console.log('✅ Resent confirmation email:', {
        orderId: order.id,
        orderNumber: order.order_number,
        customerEmail: order.email,
      });

      return { success: true, error: null };
    } catch (error) {
      return {
        success: false,
        error:
          error instanceof Error ? error.message : 'Failed to resend email',
      };
    }
  }

  // Send order confirmation email (called after successful payment)
  async sendOrderConfirmationEmail(order: FieldDay2025Order): Promise<{
    success: boolean;
    error: string | null;
  }> {
    try {
      const customerName = `${order.first_name} ${order.last_name}`;
      const orderData = {
        orderNumber: order.order_number,
        orderDate: new Date(order.created_at || new Date()),
        quantity: order.quantity,
        unitPrice: order.unit_price_cents,
        totalAmount: order.total_amount_cents,
        taxAmount: order.tax_amount_cents || 0,
        productName: order.product_name,
      };

      const pickupLocation = PICKUP_LOCATIONS[0]; // Use first pickup location
      const pickupData = {
        eventDate: FIELD_DAY_2025_EVENT.date,
        location: pickupLocation.name,
        address: pickupLocation.address,
        contactInfo: pickupLocation.contactInfo,
      };

      // Send confirmation email
      await EmailService.sendFieldDay2025OrderConfirmation(
        order.email,
        customerName,
        orderData,
        pickupData
      );

      console.log('✅ Sent order confirmation email:', {
        orderId: order.id,
        orderNumber: order.order_number,
        customerEmail: order.email,
      });

      return { success: true, error: null };
    } catch (error) {
      console.error('❌ Failed to send order confirmation email:', error);
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to send confirmation email',
      };
    }
  }

  // Send pickup ready notification email
  async sendPickupReadyEmail(order: FieldDay2025Order): Promise<{
    success: boolean;
    error: string | null;
  }> {
    try {
      const customerName = `${order.first_name} ${order.last_name}`;
      const orderData = {
        orderNumber: order.order_number,
        quantity: order.quantity,
        productName: order.product_name,
      };

      const pickupLocation = PICKUP_LOCATIONS[0]; // Use first pickup location
      const pickupData = {
        eventDate: FIELD_DAY_2025_EVENT.date,
        location: pickupLocation.name,
        address: pickupLocation.address,
        contactInfo: pickupLocation.contactInfo,
      };

      // Send pickup ready email
      await EmailService.sendFieldDay2025PickupReady(
        order.email,
        customerName,
        orderData,
        pickupData
      );

      console.log('✅ Sent pickup ready email:', {
        orderId: order.id,
        orderNumber: order.order_number,
        customerEmail: order.email,
      });

      return { success: true, error: null };
    } catch (error) {
      console.error('❌ Failed to send pickup ready email:', error);
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to send pickup ready email',
      };
    }
  }

  // Send admin notification for new orders
  async sendAdminNewOrderNotification(order: FieldDay2025Order): Promise<{
    success: boolean;
    error: string | null;
  }> {
    try {
      const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';

      const orderData = {
        orderNumber: order.order_number,
        orderDate: new Date(order.created_at || new Date()),
        customerName: `${order.first_name} ${order.last_name}`,
        customerEmail: order.email,
        customerPhone: order.phone_number,
        quantity: order.quantity,
        totalAmount: order.total_amount_cents,
        productName: order.product_name,
        paymentStatus: order.payment_status,
      };

      // Send admin notification
      await EmailService.sendFieldDay2025AdminNewOrder(adminEmail, orderData);

      console.log('✅ Sent admin new order notification:', {
        orderId: order.id,
        orderNumber: order.order_number,
        adminEmail,
      });

      return { success: true, error: null };
    } catch (error) {
      console.error('❌ Failed to send admin notification:', error);
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to send admin notification',
      };
    }
  }
}

// Export singleton instance
export const fieldDay2025OrderService = new FieldDay2025OrderService();

// Export toast-enabled methods for easy use in components
export const fieldDay2025OrderActions = {
  createOrder: fieldDay2025OrderService.createOrderWithToast.bind(
    fieldDay2025OrderService
  ),
  updateOrder: fieldDay2025OrderService.updateOrderWithToast.bind(
    fieldDay2025OrderService
  ),
  loadOrders: fieldDay2025OrderService.getOrdersWithToast.bind(
    fieldDay2025OrderService
  ),
  resendEmail: fieldDay2025OrderService.resendConfirmationEmailWithToast.bind(
    fieldDay2025OrderService
  ),
};
