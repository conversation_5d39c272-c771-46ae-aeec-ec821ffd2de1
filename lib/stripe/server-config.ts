import Stripe from 'stripe';
import stripeConfigUtils from '@/lib/utils/stripe-config';

// Get server configuration using the utility
const serverConfig = stripeConfigUtils.config.getServerConfig();

// Server-side Stripe instance
export const stripe = new Stripe(serverConfig.secretKey, {
  apiVersion: '2025-05-28.basil',
  typescript: true,
});

// Stripe webhook configuration
export const webhookConfig = {
  secret: serverConfig.webhookSecret,
} as const;

// Export configuration info for debugging/logging
export const stripeServerConfig = {
  environment: serverConfig.environment,
  keyType: serverConfig.keyType,
  isDevelopment: serverConfig.isDevelopment,
} as const;
