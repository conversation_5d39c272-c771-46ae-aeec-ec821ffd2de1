import stripeConfigUtils from '@/lib/utils/stripe-config';

// Get client configuration using the utility
const clientConfig = stripeConfigUtils.config.getClientConfig();

// Client-side Stripe configuration (safe to import on client)
export const stripeConfig = {
  publicKey: clientConfig.publicKey,
  currency: clientConfig.currency,
  country: clientConfig.country,
  environment: clientConfig.environment,
  keyType: clientConfig.keyType,
  isDevelopment: clientConfig.isDevelopment,
} as const;
