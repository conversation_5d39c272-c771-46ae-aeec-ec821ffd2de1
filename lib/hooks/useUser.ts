import { useAuth } from '@/components/auth/AuthProvider';

export function useUser() {
  const auth = useAuth();

  return {
    user: auth.user,
    loading: auth.loading,
    isAuthenticated: auth.isAuthenticated,
    isAdmin: auth.isAdmin,
    sessionState: auth.sessionState,
    isSessionValid: auth.isSessionValid,
    sessionError: auth.sessionError,
    lastValidated: auth.lastValidated,
    expiresAt: auth.expiresAt,
    refreshSession: auth.refreshSession,
    validateSession: auth.validateSession,
    signOut: auth.signOut,
  };
}
