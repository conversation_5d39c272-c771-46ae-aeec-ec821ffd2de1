import {
  Html,
  Body,
  Container,
  Section,
  Head,
  Preview,
} from '@react-email/components';
import { EmailHeader } from './EmailHeader';
import { EmailFooter } from './EmailFooter';

export interface EmailLayoutProps {
  children: React.ReactNode;
  preview: string;
}

export function EmailLayout({ children, preview }: EmailLayoutProps) {
  return (
    <Html>
      <Head />
      <Preview>{preview}</Preview>
      <Body
        style={{
          backgroundColor: '#f6f9fc',
          fontFamily: 'Arial, sans-serif',
          margin: '0',
        }}
      >
        <Container
          style={{
            maxWidth: '600px',
            margin: '0 auto',
            backgroundColor: '#ffffff',
            borderRadius: '8px',
            overflow: 'hidden',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          }}
        >
          <EmailHeader />
          <Section style={{ padding: '20px 30px' }}>{children}</Section>
          <EmailFooter />
        </Container>
      </Body>
    </Html>
  );
}
