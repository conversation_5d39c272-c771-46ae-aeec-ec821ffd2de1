import { Section, Text, Link } from '@react-email/components';

export function EmailFooter() {
  return (
    <Section
      style={{
        backgroundColor: '#f8fafc',
        padding: '20px 30px',
        borderTop: '1px solid #e2e8f0',
      }}
    >
      <Text
        style={{
          color: '#64748b',
          fontSize: '14px',
          lineHeight: '20px',
          margin: '0 0 10px 0',
        }}
      >
        AsedaFoods - Supporting local farmers and communities
      </Text>
      <Text
        style={{
          color: '#64748b',
          fontSize: '12px',
          lineHeight: '16px',
          margin: '0',
        }}
      >
        You received this email because you subscribed to our newsletter.{' '}
        <Link
          href='https://asedafoods.org/unsubscribe'
          style={{ color: '#16a34a', textDecoration: 'underline' }}
        >
          Unsubscribe
        </Link>{' '}
        or{' '}
        <Link
          href='https://asedafoods.org/contact'
          style={{ color: '#16a34a', textDecoration: 'underline' }}
        >
          contact us
        </Link>
        .
      </Text>
    </Section>
  );
}
