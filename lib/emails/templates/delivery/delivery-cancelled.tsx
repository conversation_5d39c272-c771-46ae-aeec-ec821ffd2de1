import {
  Body,
  But<PERSON>,
  Container,
  Head,
  Heading,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import * as React from 'react';

interface DeliveryCancelledProps {
  subscriberName: string;
  deliveryData: {
    deliveryDate: Date;
    pickupLocation: string;
    boxSize: string;
    cancellationReason?: string;
    nextDeliveryDate?: Date;
    deliveryId: string;
  };
}

const baseUrl = process.env.VERCEL_URL
  ? `https://${process.env.VERCEL_URL}`
  : 'http://localhost:3000';

export const DeliveryCancelled = ({
  subscriberName = 'Valued Customer',
  deliveryData = {
    deliveryDate: new Date(),
    pickupLocation: 'shabach_ministries',
    boxSize: 'medium',
    cancellationReason: 'Weather conditions',
    nextDeliveryDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    deliveryId: 'delivery-123',
  },
}: DeliveryCancelledProps) => {
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatLocation = (location: string) => {
    return location.replace('_', ' ').toUpperCase();
  };

  return (
    <Html>
      <Head />
      <Preview>Important: Your delivery has been cancelled</Preview>
      <Body style={main}>
        <Container style={container}>
          <Section style={logoContainer}>
            <Img
              src={`${baseUrl}/logo.png`}
              width='120'
              height='36'
              alt='AsedaFoods'
              style={logo}
            />
          </Section>

          <Heading style={h1}>Delivery Cancelled</Heading>

          <Text style={text}>Hi {subscriberName},</Text>

          <Text style={text}>
            We&apos;re sorry to inform you that your scheduled delivery has been
            cancelled. We apologize for any inconvenience this may cause.
          </Text>

          <Section style={cancelledCard}>
            <Heading style={h2}>Cancelled Delivery Details</Heading>

            <div style={detailRow}>
              <strong>📅 Original Date:</strong>{' '}
              {formatDate(deliveryData.deliveryDate)}
            </div>

            <div style={detailRow}>
              <strong>📍 Location:</strong>{' '}
              {formatLocation(deliveryData.pickupLocation)}
            </div>

            <div style={detailRow}>
              <strong>📦 Box Size:</strong>{' '}
              {deliveryData.boxSize.charAt(0).toUpperCase() +
                deliveryData.boxSize.slice(1)}
            </div>

            {deliveryData.cancellationReason && (
              <div style={detailRow}>
                <strong>❗ Reason:</strong> {deliveryData.cancellationReason}
              </div>
            )}
          </Section>

          {deliveryData.nextDeliveryDate ? (
            <Section style={nextDeliveryCard}>
              <Heading style={h2}>Your Next Scheduled Delivery</Heading>
              <div style={detailRow}>
                <strong>📅 Next Pickup:</strong>{' '}
                {formatDate(deliveryData.nextDeliveryDate)}
              </div>
              <Text style={nextDeliveryText}>
                Your next delivery is still scheduled as planned. We&apos;ll
                make sure to include extra fresh items to make up for this
                cancellation!
              </Text>
            </Section>
          ) : (
            <Section style={noNextDeliveryCard}>
              <Heading style={h2}>What&apos;s Next?</Heading>
              <Text style={noNextDeliveryText}>
                We don&apos;t currently have another delivery scheduled. Please
                contact us to reschedule or if you have any questions about your
                subscription.
              </Text>
            </Section>
          )}

          <Section style={buttonContainer}>
            <Button style={button} href={`${baseUrl}/dashboard`}>
              View Your Dashboard
            </Button>
          </Section>

          <Text style={text}>
            If you have any questions or concerns about this cancellation,
            please don&apos;t hesitate to reach out to us. We&apos;re here to
            help and want to ensure you have the best possible experience with
            AsedaFoods.
          </Text>

          <Section style={contactContainer}>
            <Button style={contactButton} href={`${baseUrl}/contact`}>
              Contact Support
            </Button>
          </Section>

          <Hr style={hr} />

          <Text style={footer}>
            We appreciate your understanding and continued support of local
            farmers! 🌱
          </Text>

          <Text style={footer}>
            Questions? Reply to this email or visit our{' '}
            <Link href={`${baseUrl}/contact`} style={link}>
              contact page
            </Link>
            .
          </Text>
        </Container>
      </Body>
    </Html>
  );
};

export default DeliveryCancelled;

const main = {
  backgroundColor: '#f6f9fc',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px 0 48px',
  marginBottom: '64px',
};

const logoContainer = {
  margin: '32px 0',
  textAlign: 'center' as const,
};

const logo = {
  margin: '0 auto',
};

const h1 = {
  color: '#dc2626',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
  fontSize: '24px',
  fontWeight: 'bold',
  margin: '40px 0',
  padding: '0',
  textAlign: 'center' as const,
};

const h2 = {
  color: '#333',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
  fontSize: '18px',
  fontWeight: 'bold',
  margin: '0 0 16px 0',
  padding: '0',
};

const text = {
  color: '#333',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
  fontSize: '16px',
  lineHeight: '26px',
  margin: '16px 32px',
};

const cancelledCard = {
  backgroundColor: '#fef2f2',
  border: '1px solid #fecaca',
  borderRadius: '8px',
  margin: '24px 32px',
  padding: '24px',
};

const nextDeliveryCard = {
  backgroundColor: '#f0fdf4',
  border: '1px solid #bbf7d0',
  borderRadius: '8px',
  margin: '24px 32px',
  padding: '24px',
};

const noNextDeliveryCard = {
  backgroundColor: '#fef3c7',
  border: '1px solid #fbbf24',
  borderRadius: '8px',
  margin: '24px 32px',
  padding: '24px',
};

const detailRow = {
  margin: '12px 0',
  fontSize: '16px',
  lineHeight: '24px',
};

const nextDeliveryText = {
  color: '#166534',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '16px 0 0 0',
};

const noNextDeliveryText = {
  color: '#92400e',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '0',
};

const buttonContainer = {
  textAlign: 'center' as const,
  margin: '32px 0',
};

const button = {
  backgroundColor: '#22c55e',
  borderRadius: '6px',
  color: '#fff',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
  fontSize: '16px',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  width: '200px',
  padding: '12px 0',
  margin: '0 auto',
};

const contactContainer = {
  textAlign: 'center' as const,
  margin: '16px 0',
};

const contactButton = {
  backgroundColor: '#dc2626',
  borderRadius: '6px',
  color: '#fff',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
  fontSize: '14px',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  width: '150px',
  padding: '10px 0',
  margin: '0 auto',
};

const hr = {
  borderColor: '#e6ebf1',
  margin: '32px 0',
};

const footer = {
  color: '#8898aa',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
  fontSize: '14px',
  lineHeight: '24px',
  margin: '16px 32px',
  textAlign: 'center' as const,
};

const link = {
  color: '#22c55e',
  textDecoration: 'underline',
};
