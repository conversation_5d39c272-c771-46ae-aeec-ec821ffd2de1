import {
  Text,
  Section,
  Row,
  Column,
} from '@react-email/components';
import { EmailLayout } from '../../components/EmailLayout';
import { EmailButton } from '../../components/EmailButton';
import { formatDate } from '@/lib/utils/format';

interface DeliveryProcessedProps {
  subscriberName: string;
  deliveryData: {
    deliveryDate: Date;
    pickupLocation: string;
    boxSize: string;
    boxContents?: string[];
    nextDeliveryDate?: Date;
    deliveriesRemaining?: number;
  };
}

export function DeliveryProcessed({
  subscriberName,
  deliveryData,
}: DeliveryProcessedProps) {
  const { 
    deliveryDate, 
    pickupLocation, 
    boxSize, 
    boxContents,
    nextDeliveryDate,
    deliveriesRemaining 
  } = deliveryData;

  return (
    <EmailLayout preview="Your fresh box has been processed">
      <Text
        style={{
          fontSize: '24px',
          fontWeight: 'bold',
          color: '#1f2937',
          margin: '0 0 20px 0',
        }}
      >
        Delivery Processed! ✅
      </Text>

      <Text
        style={{
          fontSize: '16px',
          color: '#4b5563',
          lineHeight: '24px',
          margin: '0 0 20px 0',
        }}
      >
        Hi {subscriberName}, your fresh box delivery has been processed and is ready for pickup!
      </Text>

      <Section
        style={{
          backgroundColor: '#dcfce7',
          padding: '20px',
          borderRadius: '8px',
          margin: '20px 0',
          border: '1px solid #16a34a',
        }}
      >
        <Text
          style={{
            fontSize: '18px',
            fontWeight: 'bold',
            color: '#15803d',
            margin: '0 0 15px 0',
          }}
        >
          Delivery Summary
        </Text>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '40%' }}>
            <Text style={{ fontSize: '14px', color: '#166534', margin: '0' }}>
              Delivery Date:
            </Text>
          </Column>
          <Column style={{ width: '60%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#15803d',
                margin: '0',
              }}
            >
              {formatDate(deliveryDate)}
            </Text>
          </Column>
        </Row>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '40%' }}>
            <Text style={{ fontSize: '14px', color: '#166534', margin: '0' }}>
              Pickup Location:
            </Text>
          </Column>
          <Column style={{ width: '60%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#15803d',
                margin: '0',
              }}
            >
              {pickupLocation}
            </Text>
          </Column>
        </Row>

        <Row>
          <Column style={{ width: '40%' }}>
            <Text style={{ fontSize: '14px', color: '#166534', margin: '0' }}>
              Box Size:
            </Text>
          </Column>
          <Column style={{ width: '60%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#15803d',
                margin: '0',
              }}
            >
              {boxSize}
            </Text>
          </Column>
        </Row>
      </Section>

      {boxContents && boxContents.length > 0 && (
        <Section
          style={{
            backgroundColor: '#f7f9fa',
            padding: '20px',
            borderRadius: '8px',
            margin: '20px 0',
          }}
        >
          <Text
            style={{
              fontSize: '18px',
              fontWeight: 'bold',
              color: '#1f2937',
              margin: '0 0 15px 0',
            }}
          >
            What Was in Your Box 📦
          </Text>

          <ul
            style={{
              fontSize: '14px',
              color: '#4b5563',
              lineHeight: '20px',
              margin: '0',
              paddingLeft: '20px',
            }}
          >
            {boxContents.map((item, index) => (
              <li key={index} style={{ marginBottom: '5px' }}>
                {item}
              </li>
            ))}
          </ul>
        </Section>
      )}

      {nextDeliveryDate && (
        <Section
          style={{
            backgroundColor: '#eff6ff',
            padding: '20px',
            borderRadius: '8px',
            margin: '20px 0',
            border: '1px solid #3b82f6',
          }}
        >
          <Text
            style={{
              fontSize: '18px',
              fontWeight: 'bold',
              color: '#1d4ed8',
              margin: '0 0 15px 0',
            }}
          >
            Next Delivery
          </Text>

          <Row style={{ marginBottom: '10px' }}>
            <Column style={{ width: '50%' }}>
              <Text style={{ fontSize: '14px', color: '#1e40af', margin: '0' }}>
                Next Delivery Date:
              </Text>
            </Column>
            <Column style={{ width: '50%' }}>
              <Text
                style={{
                  fontSize: '14px',
                  fontWeight: 'bold',
                  color: '#1d4ed8',
                  margin: '0',
                }}
              >
                {formatDate(nextDeliveryDate)}
              </Text>
            </Column>
          </Row>

          {deliveriesRemaining !== undefined && (
            <Row>
              <Column style={{ width: '50%' }}>
                <Text style={{ fontSize: '14px', color: '#1e40af', margin: '0' }}>
                  Deliveries Remaining:
                </Text>
              </Column>
              <Column style={{ width: '50%' }}>
                <Text
                  style={{
                    fontSize: '14px',
                    fontWeight: 'bold',
                    color: '#1d4ed8',
                    margin: '0',
                  }}
                >
                  {deliveriesRemaining}
                </Text>
              </Column>
            </Row>
          )}
        </Section>
      )}

      <Text
        style={{
          fontSize: '16px',
          color: '#4b5563',
          lineHeight: '24px',
          margin: '20px 0',
        }}
      >
        We hope you enjoy your fresh, local produce! Here are some tips to make the most of your box:
      </Text>

      <ul
        style={{
          fontSize: '16px',
          color: '#4b5563',
          lineHeight: '24px',
          margin: '0 0 20px 20px',
          paddingLeft: '0',
        }}
      >
        <li style={{ marginBottom: '8px' }}>
          Store leafy greens in the refrigerator with a damp paper towel
        </li>
        <li style={{ marginBottom: '8px' }}>
          Keep root vegetables in a cool, dark place
        </li>
        <li style={{ marginBottom: '8px' }}>
          Wash produce just before eating for best freshness
        </li>
        <li style={{ marginBottom: '8px' }}>
          Try new recipes with unfamiliar vegetables - it&apos;s part of the CSA adventure!
        </li>
      </ul>

      <EmailButton 
        href="https://asedafoods.org/dashboard" 
        text="View Dashboard" 
      />

      <Text
        style={{
          fontSize: '14px',
          color: '#6b7280',
          lineHeight: '20px',
          margin: '30px 0 0 0',
        }}
      >
        <strong>How was your box this week?</strong> We&apos;d love to hear your feedback! 
        Your input helps us improve and ensures you get the best possible produce.
      </Text>

      <EmailButton
        href="https://asedafoods.org/contact"
        text="Share Feedback"
        style={{
          backgroundColor: '#6b7280',
          margin: '10px 0',
        }}
      />

      <Text
        style={{
          fontSize: '14px',
          color: '#6b7280',
          lineHeight: '20px',
          margin: '20px 0 0 0',
        }}
      >
        Thank you for supporting local agriculture and sustainable farming practices. 
        Enjoy your fresh produce!
      </Text>
    </EmailLayout>
  );
}
