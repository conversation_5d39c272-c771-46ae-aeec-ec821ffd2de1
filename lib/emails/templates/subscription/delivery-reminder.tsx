import {
  Text,
  Section,
  Row,
  Column,
} from '@react-email/components';
import { EmailLayout } from '../../components/EmailLayout';
import { EmailButton } from '../../components/EmailButton';
import { formatDate } from '@/lib/utils/format';

interface DeliveryReminderProps {
  subscriberName: string;
  deliveryData: {
    deliveryDate: Date;
    pickupLocation: string;
    pickupHours: string;
    boxSize: string;
    boxContents?: string[];
    specialInstructions?: string;
  };
}

export function DeliveryReminder({
  subscriberName,
  deliveryData,
}: DeliveryReminderProps) {
  const { 
    deliveryDate, 
    pickupLocation, 
    pickupHours, 
    boxSize, 
    boxContents,
    specialInstructions 
  } = deliveryData;

  return (
    <EmailLayout preview="Your fresh box is ready for pickup soon!">
      <Text
        style={{
          fontSize: '24px',
          fontWeight: 'bold',
          color: '#1f2937',
          margin: '0 0 20px 0',
        }}
      >
        Your Fresh Box is Ready Soon! 🥕
      </Text>

      <Text
        style={{
          fontSize: '16px',
          color: '#4b5563',
          lineHeight: '24px',
          margin: '0 0 20px 0',
        }}
      >
        Hi {subscriberName}, your fresh box of local, seasonal produce will be ready for pickup in just a few days!
      </Text>

      <Section
        style={{
          backgroundColor: '#dcfce7',
          padding: '20px',
          borderRadius: '8px',
          margin: '20px 0',
          border: '1px solid #16a34a',
        }}
      >
        <Text
          style={{
            fontSize: '18px',
            fontWeight: 'bold',
            color: '#15803d',
            margin: '0 0 15px 0',
          }}
        >
          Pickup Details
        </Text>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '30%' }}>
            <Text style={{ fontSize: '14px', color: '#166534', margin: '0' }}>
              Date:
            </Text>
          </Column>
          <Column style={{ width: '70%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#15803d',
                margin: '0',
              }}
            >
              {formatDate(deliveryDate)}
            </Text>
          </Column>
        </Row>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '30%' }}>
            <Text style={{ fontSize: '14px', color: '#166534', margin: '0' }}>
              Location:
            </Text>
          </Column>
          <Column style={{ width: '70%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#15803d',
                margin: '0',
              }}
            >
              {pickupLocation}
            </Text>
          </Column>
        </Row>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '30%' }}>
            <Text style={{ fontSize: '14px', color: '#166534', margin: '0' }}>
              Hours:
            </Text>
          </Column>
          <Column style={{ width: '70%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#15803d',
                margin: '0',
              }}
            >
              {pickupHours}
            </Text>
          </Column>
        </Row>

        <Row>
          <Column style={{ width: '30%' }}>
            <Text style={{ fontSize: '14px', color: '#166534', margin: '0' }}>
              Box Size:
            </Text>
          </Column>
          <Column style={{ width: '70%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#15803d',
                margin: '0',
              }}
            >
              {boxSize}
            </Text>
          </Column>
        </Row>
      </Section>

      {boxContents && boxContents.length > 0 && (
        <Section
          style={{
            backgroundColor: '#f7f9fa',
            padding: '20px',
            borderRadius: '8px',
            margin: '20px 0',
          }}
        >
          <Text
            style={{
              fontSize: '18px',
              fontWeight: 'bold',
              color: '#1f2937',
              margin: '0 0 15px 0',
            }}
          >
            What&apos;s in Your Box This Week 📦
          </Text>

          <ul
            style={{
              fontSize: '14px',
              color: '#4b5563',
              lineHeight: '20px',
              margin: '0',
              paddingLeft: '20px',
            }}
          >
            {boxContents.map((item, index) => (
              <li key={index} style={{ marginBottom: '5px' }}>
                {item}
              </li>
            ))}
          </ul>
        </Section>
      )}

      {specialInstructions && (
        <Section
          style={{
            backgroundColor: '#fef3c7',
            padding: '15px',
            borderRadius: '8px',
            margin: '20px 0',
          }}
        >
          <Text
            style={{
              fontSize: '14px',
              fontWeight: 'bold',
              color: '#92400e',
              margin: '0 0 5px 0',
            }}
          >
            Your Special Instructions:
          </Text>
          <Text
            style={{
              fontSize: '14px',
              color: '#92400e',
              margin: '0',
            }}
          >
            {specialInstructions}
          </Text>
        </Section>
      )}

      <Text
        style={{
          fontSize: '16px',
          color: '#4b5563',
          lineHeight: '24px',
          margin: '20px 0',
        }}
      >
        <strong>Important Reminders:</strong>
      </Text>

      <ul
        style={{
          fontSize: '16px',
          color: '#4b5563',
          lineHeight: '24px',
          margin: '0 0 20px 20px',
          paddingLeft: '0',
        }}
      >
        <li style={{ marginBottom: '8px' }}>
          Please bring a bag or box to carry your produce
        </li>
        <li style={{ marginBottom: '8px' }}>
          Pickup is available during the specified hours only
        </li>
        <li style={{ marginBottom: '8px' }}>
          If you can&apos;t make it, please arrange for someone else to pick up for you
        </li>
        <li style={{ marginBottom: '8px' }}>
          Contact us if you have any questions or concerns
        </li>
      </ul>

      <EmailButton 
        href="https://asedafoods.org/dashboard" 
        text="View Pickup Details" 
      />

      <Text
        style={{
          fontSize: '14px',
          color: '#6b7280',
          lineHeight: '20px',
          margin: '30px 0 0 0',
        }}
      >
        Can&apos;t make your pickup time? Need to make changes to your subscription? 
        You can manage everything from your dashboard or contact us directly.
      </Text>

      <EmailButton
        href="https://asedafoods.org/contact"
        text="Contact Us"
        style={{
          backgroundColor: '#6b7280',
          margin: '10px 0',
        }}
      />

      <Text
        style={{
          fontSize: '14px',
          color: '#6b7280',
          lineHeight: '20px',
          margin: '20px 0 0 0',
        }}
      >
        Thank you for supporting local agriculture! We hope you enjoy your fresh, seasonal produce.
      </Text>
    </EmailLayout>
  );
}
