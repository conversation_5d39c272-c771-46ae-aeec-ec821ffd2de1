import {
  Text,
  Section,
  Row,
  Column,
} from '@react-email/components';
import { EmailLayout } from '../../components/EmailLayout';
import { EmailButton } from '../../components/EmailButton';
import { formatPrice, formatDate } from '@/lib/utils/format';

interface PaymentConfirmationProps {
  subscriberName: string;
  paymentData: {
    amount: number;
    paymentMethod: string;
    transactionId: string;
    paymentDate: Date;
  };
  subscriptionData: {
    boxSize: string;
    frequency: string;
    nextDeliveryDate: Date;
    deliveriesRemaining?: number;
  };
}

export function PaymentConfirmation({
  subscriberName,
  paymentData,
  subscriptionData,
}: PaymentConfirmationProps) {
  const { amount, paymentMethod, transactionId, paymentDate } = paymentData;
  const { boxSize, frequency, nextDeliveryDate, deliveriesRemaining } = subscriptionData;

  return (
    <EmailLayout preview="Payment received - Thank you!">
      <Text
        style={{
          fontSize: '24px',
          fontWeight: 'bold',
          color: '#1f2937',
          margin: '0 0 20px 0',
        }}
      >
        Payment Received, {subscriberName}!
      </Text>

      <Text
        style={{
          fontSize: '16px',
          color: '#4b5563',
          lineHeight: '24px',
          margin: '0 0 20px 0',
        }}
      >
        Thank you for your payment! Your subscription is active and your next fresh box will be ready soon.
      </Text>

      <Section
        style={{
          backgroundColor: '#dcfce7',
          padding: '20px',
          borderRadius: '8px',
          margin: '20px 0',
          border: '1px solid #16a34a',
        }}
      >
        <Text
          style={{
            fontSize: '18px',
            fontWeight: 'bold',
            color: '#15803d',
            margin: '0 0 15px 0',
          }}
        >
          Payment Details
        </Text>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', color: '#166534', margin: '0' }}>
              Amount Paid:
            </Text>
          </Column>
          <Column style={{ width: '50%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#15803d',
                margin: '0',
              }}
            >
              {formatPrice(amount)}
            </Text>
          </Column>
        </Row>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', color: '#166534', margin: '0' }}>
              Payment Method:
            </Text>
          </Column>
          <Column style={{ width: '50%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#15803d',
                margin: '0',
              }}
            >
              {paymentMethod}
            </Text>
          </Column>
        </Row>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', color: '#166534', margin: '0' }}>
              Transaction ID:
            </Text>
          </Column>
          <Column style={{ width: '50%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#15803d',
                margin: '0',
                fontFamily: 'monospace',
              }}
            >
              {transactionId}
            </Text>
          </Column>
        </Row>

        <Row>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', color: '#166534', margin: '0' }}>
              Payment Date:
            </Text>
          </Column>
          <Column style={{ width: '50%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#15803d',
                margin: '0',
              }}
            >
              {formatDate(paymentDate)}
            </Text>
          </Column>
        </Row>
      </Section>

      <Section
        style={{
          backgroundColor: '#f7f9fa',
          padding: '20px',
          borderRadius: '8px',
          margin: '20px 0',
        }}
      >
        <Text
          style={{
            fontSize: '18px',
            fontWeight: 'bold',
            color: '#1f2937',
            margin: '0 0 15px 0',
          }}
        >
          Your Subscription
        </Text>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', color: '#6b7280', margin: '0' }}>
              Box Size:
            </Text>
          </Column>
          <Column style={{ width: '50%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#1f2937',
                margin: '0',
              }}
            >
              {boxSize}
            </Text>
          </Column>
        </Row>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', color: '#6b7280', margin: '0' }}>
              Frequency:
            </Text>
          </Column>
          <Column style={{ width: '50%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#1f2937',
                margin: '0',
              }}
            >
              {frequency}
            </Text>
          </Column>
        </Row>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', color: '#6b7280', margin: '0' }}>
              Next Delivery:
            </Text>
          </Column>
          <Column style={{ width: '50%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#1f2937',
                margin: '0',
              }}
            >
              {formatDate(nextDeliveryDate)}
            </Text>
          </Column>
        </Row>

        {deliveriesRemaining && (
          <Row>
            <Column style={{ width: '50%' }}>
              <Text style={{ fontSize: '14px', color: '#6b7280', margin: '0' }}>
                Deliveries Remaining:
              </Text>
            </Column>
            <Column style={{ width: '50%' }}>
              <Text
                style={{
                  fontSize: '14px',
                  fontWeight: 'bold',
                  color: '#1f2937',
                  margin: '0',
                }}
              >
                {deliveriesRemaining}
              </Text>
            </Column>
          </Row>
        )}
      </Section>

      <Text
        style={{
          fontSize: '16px',
          color: '#4b5563',
          lineHeight: '24px',
          margin: '20px 0',
        }}
      >
        You can view your payment history and manage your subscription from your dashboard.
      </Text>

      <EmailButton href="https://asedafoods.org/dashboard" text="View Dashboard" />

      <Text
        style={{
          fontSize: '14px',
          color: '#6b7280',
          lineHeight: '20px',
          margin: '20px 0 0 0',
        }}
      >
        Keep this email as your receipt. If you have any questions about your payment or subscription, 
        please don&apos;t hesitate to contact us.
      </Text>
    </EmailLayout>
  );
}
