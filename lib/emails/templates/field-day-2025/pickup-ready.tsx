import {
  Body,
  Button,
  Container,
  Head,
  Heading,
  Hr,
  Html,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';

interface FieldDay2025PickupReadyProps {
  customerName: string;
  orderData: {
    orderNumber: string;
    quantity: number;
    productName: string;
  };
  pickupData: {
    eventDate: string;
    location: string;
    address: string;
    contactInfo?: string;
  };
}

const baseUrl = process.env.VERCEL_URL
  ? `https://${process.env.VERCEL_URL}`
  : 'http://localhost:3000';

export function FieldDay2025PickupReady({
  customerName,
  orderData,
  pickupData,
}: FieldDay2025PickupReadyProps) {
  const { orderNumber, quantity, productName } = orderData;
  const { eventDate, location, address, contactInfo } = pickupData;

  return (
    <Html>
      <Head />
      <Preview>
        Your Field Day 2025 order #{orderNumber} is ready for pickup! 📦
      </Preview>
      <Body style={main}>
        <Container style={container}>
          <Section style={logoContainer}>
            <Link href={'/'} style={logoText}>
              <span style={logoGreen}>Aseda</span>
              <span style={logoDark}>Foods</span>
            </Link>
          </Section>

          <Heading style={h1}>Your Order is Ready! 📦</Heading>

          <Text style={text}>Hi {customerName},</Text>

          <Text style={text}>
            Great news! Your Field Day 2025 produce box is ready for pickup.
            We&apos;ve carefully prepared your fresh, locally-sourced items and
            they&apos;re waiting for you at the event.
          </Text>

          {/* Order Summary */}
          <Section style={orderSection}>
            <Heading style={h2}>Ready for Pickup</Heading>

            <div style={orderDetails}>
              <div style={orderRow}>
                <span style={orderLabel}>Order Number:</span>
                <span style={orderValue}>{orderNumber}</span>
              </div>
              <div style={orderRow}>
                <span style={orderLabel}>Product:</span>
                <span style={orderValue}>{productName}</span>
              </div>
              <div style={orderRow}>
                <span style={orderLabel}>Quantity:</span>
                <span style={orderValue}>
                  {quantity} box{quantity > 1 ? 'es' : ''}
                </span>
              </div>
            </div>
          </Section>

          {/* Pickup Instructions */}
          <Section style={pickupSection}>
            <Heading style={h2}>Pickup Instructions</Heading>

            <div style={pickupDetails}>
              <Text style={pickupText}>
                <strong>📅 Event Date:</strong> {eventDate}
              </Text>
              <Text style={pickupText}>
                <strong>📍 Location:</strong> {location}
              </Text>
              <Text style={pickupText}>
                <strong>🏠 Address:</strong> {address}
              </Text>
              {contactInfo && (
                <Text style={pickupText}>
                  <strong>📞 Contact:</strong> {contactInfo}
                </Text>
              )}
            </div>

            <Section style={importantNotice}>
              <Text style={noticeText}>
                <strong>What to Bring:</strong>
              </Text>
              <ul style={noticeList}>
                <li>This confirmation email or your order number</li>
                <li>Valid photo ID for verification</li>
                <li>Reusable bags (optional but appreciated!)</li>
              </ul>
            </Section>
          </Section>
          {/* Action Buttons */}
          <Section style={buttonContainer}>
            <Button style={button} href={`${baseUrl}/field-day-2025`}>
              View Event Details
            </Button>
          </Section>

          <Hr style={hr} />

          {/* Footer */}
          <Text style={footer}>
            Can&apos;t make it to pickup? Please contact us immediately at{' '}
            <Link href='mailto:<EMAIL>' style={link}>
              <EMAIL>
            </Link>{' '}
            or {contactInfo || '(*************'}.
          </Text>

          <Text style={footer}>
            We&apos;re excited to see you at Field Day 2025! 🎉
          </Text>
        </Container>
      </Body>
    </Html>
  );
}

// Styles
const main = {
  backgroundColor: '#ffffff',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
};

const container = {
  margin: '0 auto',
  padding: '20px 0 48px',
  maxWidth: '560px',
};

const logoContainer = {
  textAlign: 'center' as const,
  margin: '0 0 40px',
};

const logoText = {
  fontSize: '24px',
  fontWeight: 'bold',
  textDecoration: 'none',
  display: 'block',
  textAlign: 'center' as const,
  margin: '0 auto',
};

const logoGreen = {
  color: '#16a34a',
};

const logoDark = {
  color: '#14532d',
};

const h1 = {
  color: '#1f2937',
  fontSize: '28px',
  fontWeight: 'bold',
  textAlign: 'center' as const,
  margin: '0 0 30px',
};

const h2 = {
  color: '#1f2937',
  fontSize: '20px',
  fontWeight: 'bold',
  margin: '0 0 16px',
};

const text = {
  color: '#4b5563',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '0 0 16px',
};

const orderSection = {
  backgroundColor: '#ecfdf5',
  borderRadius: '8px',
  padding: '24px',
  margin: '24px 0',
  border: '1px solid #a7f3d0',
};

const orderDetails = {
  margin: '16px 0',
};

const orderRow = {
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  padding: '8px 0',
  borderBottom: '1px solid #d1fae5',
};

const orderLabel = {
  color: '#065f46',
  fontSize: '14px',
  fontWeight: '500',
};

const orderValue = {
  color: '#1f2937',
  fontSize: '14px',
  fontWeight: 'bold',
};

const pickupSection = {
  backgroundColor: '#fef3c7',
  borderRadius: '8px',
  padding: '24px',
  margin: '24px 0',
  border: '1px solid #fcd34d',
};

const pickupDetails = {
  margin: '16px 0',
};

const pickupText = {
  color: '#92400e',
  fontSize: '14px',
  lineHeight: '20px',
  margin: '8px 0',
};

const importantNotice = {
  backgroundColor: '#ffffff',
  borderRadius: '6px',
  padding: '16px',
  margin: '16px 0',
  border: '1px solid #fbbf24',
};

const noticeText = {
  color: '#92400e',
  fontSize: '14px',
  fontWeight: 'bold',
  margin: '0 0 8px',
};

const noticeList = {
  color: '#92400e',
  fontSize: '14px',
  lineHeight: '20px',
  paddingLeft: '20px',
  margin: '0',
};

// const contentsSection = {
//   margin: '24px 0',
// };

// const contentsGrid = {
//   display: 'grid',
//   gridTemplateColumns: '1fr 1fr',
//   gap: '20px',
//   margin: '16px 0',
// };

// const contentsColumn = {
//   backgroundColor: '#f9fafb',
//   borderRadius: '6px',
//   padding: '16px',
// };

// const contentsHeader = {
//   color: '#1f2937',
//   fontSize: '14px',
//   fontWeight: 'bold',
//   margin: '0 0 8px',
// };

// const contentsList = {
//   color: '#4b5563',
//   fontSize: '13px',
//   lineHeight: '18px',
//   paddingLeft: '16px',
//   margin: '0',
// };

// const eventSection = {
//   backgroundColor: '#ede9fe',
//   borderRadius: '8px',
//   padding: '24px',
//   margin: '24px 0',
//   border: '1px solid #c4b5fd',
// };

// const eventList = {
//   color: '#5b21b6',
//   fontSize: '14px',
//   lineHeight: '20px',
//   paddingLeft: '20px',
//   margin: '16px 0',
// };

// const eventItem = {
//   margin: '6px 0',
// };

const buttonContainer = {
  textAlign: 'center' as const,
  margin: '32px 0',
};

const button = {
  backgroundColor: '#059669',
  borderRadius: '6px',
  color: '#ffffff',
  fontSize: '16px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '12px 24px',
};

const hr = {
  borderColor: '#e5e7eb',
  margin: '32px 0',
};

const footer = {
  color: '#6b7280',
  fontSize: '14px',
  lineHeight: '20px',
  textAlign: 'center' as const,
  margin: '16px 0',
};

const link = {
  color: '#059669',
  textDecoration: 'underline',
};
