import {
  Body,
  But<PERSON>,
  Container,
  Head,
  Heading,
  Hr,
  Html,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';

interface FieldDay2025OrderConfirmationProps {
  customerName: string;
  orderData: {
    orderNumber: string;
    orderDate: Date;
    quantity: number;
    unitPrice: number;
    totalAmount: number;
    taxAmount: number;
    productName: string;
  };
  pickupData: {
    eventDate: string;
    location: string;
    address: string;
    contactInfo?: string;
  };
}

const baseUrl = process.env.VERCEL_URL
  ? `https://${process.env.VERCEL_URL}`
  : 'http://localhost:3000';

export function FieldDay2025OrderConfirmation({
  customerName,
  orderData,
  pickupData,
}: FieldDay2025OrderConfirmationProps) {
  const {
    orderNumber,
    orderDate,
    quantity,
    unitPrice,
    totalAmount,
    taxAmount,
    productName,
  } = orderData;

  const { location, address, contactInfo } = pickupData;

  return (
    <Html>
      <Head />
      <Preview>
        Your Field Day 2025 order #{orderNumber} is confirmed! 🎉
      </Preview>
      <Body style={main}>
        <Container style={container}>
          <Section style={logoContainer}>
            <Link href={'/'} style={logoText}>
              <span style={logoGreen}>Aseda</span>
              <span style={logoDark}>Foods</span>
            </Link>
          </Section>

          <Heading style={h1}>Order Confirmed! 🎉</Heading>

          <Text style={text}>Hi {customerName},</Text>

          <Text style={text}>
            Thank you for your Field Day 2025 order! We&apos;re excited to have
            you join us for this special event. Your fresh produce box will be
            ready for pickup.
          </Text>

          {/* Order Details */}
          <Section style={orderSection}>
            <Heading style={h2}>Order Details</Heading>

            <div style={orderDetails}>
              <div style={orderRow}>
                <span style={orderLabel}>Order Number:</span>
                <span style={orderValue}>{orderNumber}</span>
              </div>
              <div style={orderRow}>
                <span style={orderLabel}>Order Date:</span>
                <span style={orderValue}>{orderDate.toLocaleDateString()}</span>
              </div>
              <div style={orderRow}>
                <span style={orderLabel}>Product:</span>
                <span style={orderValue}>{productName}</span>
              </div>
              <div style={orderRow}>
                <span style={orderLabel}>Quantity:</span>
                <span style={orderValue}>
                  {quantity} box{quantity > 1 ? 'es' : ''}
                </span>
              </div>
            </div>
            <div style={orderDetails}>
              <div style={orderRow}>
                <span style={orderLabel}>Subtotal:</span>
                <span style={orderValue}>
                  ${((unitPrice * quantity) / 100).toFixed(2)}
                </span>
              </div>
              <div style={orderRow}>
                <span style={orderLabel}>Tax:</span>
                <span style={orderValue}>${(taxAmount / 100).toFixed(2)}</span>
              </div>
              <div style={orderRowTotal}>
                <span style={orderLabelTotal}>Total:</span>
                <span style={orderValueTotal}>
                  ${(totalAmount / 100).toFixed(2)}
                </span>
              </div>
            </div>
          </Section>
          {/* Pickup Information */}
          <Section style={pickupSection}>
            <Heading style={h2}>Pickup Information</Heading>

            <div style={pickupDetails}>
              <Text style={pickupText}>
                <strong>📍 Location:</strong> {location}
              </Text>
              <Text style={pickupText}>
                <strong>🏠 Address:</strong> {address}
              </Text>
              {contactInfo && (
                <Text style={pickupText}>
                  <strong>📞 Contact:</strong> {contactInfo}
                </Text>
              )}
            </div>

            <Section style={importantNotice}>
              <Text style={noticeText}>
                <strong>Important:</strong> Please bring this confirmation email
                for order verification. Orders must be picked up during the
                event hours.
              </Text>
            </Section>
          </Section>
          {/* Action Button */}
          <Section style={buttonContainer}>
            <Button style={button} href={`${baseUrl}/field-day-2025`}>
              View Event Details
            </Button>
          </Section>

          <Hr style={hr} />

          {/* Footer */}
          <Text style={footer}>
            Questions about your order? Reply to this email or contact us at{' '}
            <Link href='mailto:<EMAIL>' style={link}>
              <EMAIL>
            </Link>
          </Text>

          <Text style={footer}>
            Thank you for supporting local agriculture and sustainable farming!
          </Text>
        </Container>
      </Body>
    </Html>
  );
}

// Styles
const main = {
  backgroundColor: '#ffffff',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
};

const container = {
  margin: '0 auto',
  padding: '20px 0 48px',
  maxWidth: '560px',
};

const logoContainer = {
  textAlign: 'center' as const,
  margin: '0 0 40px',
};

const logoText = {
  fontSize: '24px',
  fontWeight: 'bold',
  textDecoration: 'none',
  display: 'block',
  textAlign: 'center' as const,
  margin: '0 auto',
};

const logoGreen = {
  color: '#16a34a',
};

const logoDark = {
  color: '#14532d',
};

const h1 = {
  color: '#1f2937',
  fontSize: '28px',
  fontWeight: 'bold',
  textAlign: 'center' as const,
  margin: '0 0 30px',
};

const h2 = {
  color: '#1f2937',
  fontSize: '20px',
  fontWeight: 'bold',
  margin: '0 0 16px',
};

const text = {
  color: '#4b5563',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '0 0 16px',
};

const orderSection = {
  backgroundColor: '#f9fafb',
  borderRadius: '8px',
  padding: '24px',
  margin: '24px 0',
};

const orderDetails = {
  margin: '16px 0',
};

const orderRow = {
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  padding: '8px 0',
  borderBottom: '1px solid #e5e7eb',
};

const orderRowTotal = {
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  padding: '12px 0 8px',
  borderTop: '2px solid #d1d5db',
  marginTop: '8px',
};

const orderLabel = {
  color: '#6b7280',
  fontSize: '14px',
};

const orderValue = {
  color: '#1f2937',
  fontSize: '14px',
  fontWeight: '500',
};

const orderLabelTotal = {
  color: '#1f2937',
  fontSize: '16px',
  fontWeight: 'bold',
};

const orderValueTotal = {
  color: '#059669',
  fontSize: '16px',
  fontWeight: 'bold',
};

const pickupSection = {
  backgroundColor: '#fef3c7',
  borderRadius: '8px',
  padding: '24px',
  margin: '24px 0',
  border: '1px solid #fcd34d',
};

const pickupDetails = {
  margin: '16px 0',
};

const pickupText = {
  color: '#92400e',
  fontSize: '14px',
  lineHeight: '20px',
  margin: '8px 0',
};

const importantNotice = {
  backgroundColor: '#fef2f2',
  borderRadius: '6px',
  padding: '16px',
  margin: '16px 0',
  border: '1px solid #fecaca',
};

const noticeText = {
  color: '#991b1b',
  fontSize: '14px',
  lineHeight: '20px',
  margin: '0',
};

const buttonContainer = {
  textAlign: 'center' as const,
  margin: '32px 0',
};

const button = {
  backgroundColor: '#059669',
  borderRadius: '6px',
  color: '#ffffff',
  fontSize: '16px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '12px 24px',
};

const hr = {
  borderColor: '#e5e7eb',
  margin: '32px 0',
};

const footer = {
  color: '#6b7280',
  fontSize: '14px',
  lineHeight: '20px',
  textAlign: 'center' as const,
  margin: '16px 0',
};

const link = {
  color: '#059669',
  textDecoration: 'underline',
};
