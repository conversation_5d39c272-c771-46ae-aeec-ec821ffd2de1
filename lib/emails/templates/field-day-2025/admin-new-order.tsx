import {
  Body,
  Button,
  Container,
  Head,
  Heading,
  Hr,
  Html,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import * as React from 'react';

interface FieldDay2025AdminNewOrderProps {
  orderData: {
    orderNumber: string;
    orderDate: Date;
    customerName: string;
    customerEmail: string;
    customerPhone: string;
    quantity: number;
    totalAmount: number;
    productName: string;
    paymentStatus: string;
  };
}

const baseUrl = process.env.VERCEL_URL
  ? `https://${process.env.VERCEL_URL}`
  : 'http://localhost:3000';

export function FieldDay2025AdminNewOrder({
  orderData,
}: FieldDay2025AdminNewOrderProps) {
  const {
    orderNumber,
    orderDate,
    customerName,
    customerEmail,
    customerPhone,
    quantity,
    totalAmount,
    productName,
    paymentStatus,
  } = orderData;

  const isPaymentSuccessful = paymentStatus === 'succeeded';

  return (
    <Html>
      <Head />
      <Preview>
        New Field Day 2025 order #{orderNumber} from {customerName}
      </Preview>
      <Body style={main}>
        <Container style={container}>
          <Section style={logoContainer}>
            <Link href={'/'} style={logoText}>
              <span style={logoGreen}>Aseda</span>
              <span style={logoDark}>Foods</span>
            </Link>
          </Section>

          <Heading style={h1}>
            {isPaymentSuccessful
              ? '✅ New Order Received'
              : '⏳ New Order (Payment Pending)'}
          </Heading>

          <Text style={text}>
            A new Field Day 2025 order has been placed and requires your
            attention.
          </Text>

          {/* Order Summary */}
          <Section style={orderSection}>
            <Heading style={h2}>Order Summary</Heading>

            <div style={orderDetails}>
              <div style={orderRow}>
                <span style={orderLabel}>Order Number:</span>
                <span style={orderValue}>{orderNumber}</span>
              </div>
              <div style={orderRow}>
                <span style={orderLabel}>Order Date:</span>
                <span style={orderValue}>{orderDate.toLocaleString()}</span>
              </div>
              <div style={orderRow}>
                <span style={orderLabel}>Product:</span>
                <span style={orderValue}>{productName}</span>
              </div>
              <div style={orderRow}>
                <span style={orderLabel}>Quantity:</span>
                <span style={orderValue}>
                  {quantity} box{quantity > 1 ? 'es' : ''}
                </span>
              </div>
              <div style={orderRow}>
                <span style={orderLabel}>Total Amount:</span>
                <span style={orderValueTotal}>
                  ${(totalAmount / 100).toFixed(2)}
                </span>
              </div>
              <div style={orderRow}>
                <span style={orderLabel}>Payment Status:</span>
                <span
                  style={
                    isPaymentSuccessful ? paymentSuccessful : paymentPending
                  }
                >
                  {paymentStatus.charAt(0).toUpperCase() +
                    paymentStatus.slice(1)}
                </span>
              </div>
            </div>
          </Section>

          {/* Customer Information */}
          <Section style={customerSection}>
            <Heading style={h2}>Customer Information</Heading>

            <div style={customerDetails}>
              <div style={customerRow}>
                <span style={customerLabel}>Name:</span>
                <span style={customerValue}>{customerName}</span>
              </div>
              <div style={customerRow}>
                <span style={customerLabel}>Email:</span>
                <span style={customerValue}>
                  <Link href={`mailto:${customerEmail}`} style={emailLink}>
                    {customerEmail}
                  </Link>
                </span>
              </div>
              <div style={customerRow}>
                <span style={customerLabel}>Phone:</span>
                <span style={customerValue}>
                  <Link href={`tel:${customerPhone}`} style={phoneLink}>
                    {customerPhone}
                  </Link>
                </span>
              </div>
            </div>
          </Section>

          {/* Action Required */}
          {!isPaymentSuccessful && (
            <Section style={alertSection}>
              <Heading style={h2}>⚠️ Action Required</Heading>

              <Text style={alertText}>
                This order has a pending payment status. Please monitor the
                payment and follow up with the customer if necessary.
              </Text>
            </Section>
          )}

          {/* Next Steps */}
          <Section style={stepsSection}>
            <Heading style={h2}>Next Steps</Heading>

            <ul style={stepsList}>
              <li style={stepsItem}>
                {isPaymentSuccessful ? '✅' : '⏳'} Payment confirmation
              </li>
              <li style={stepsItem}>
                📦 Prepare produce box for Field Day 2025
              </li>
              <li style={stepsItem}>
                📧 Send pickup ready notification (when ready)
              </li>
              <li style={stepsItem}>📍 Ensure pickup location is prepared</li>
            </ul>
          </Section>

          {/* Action Buttons */}
          <Section style={buttonContainer}>
            <Button
              style={primaryButton}
              href={`${baseUrl}/admin/field-day-2025/orders?search=${orderNumber}`}
            >
              View Order Details
            </Button>

            <Button
              style={secondaryButton}
              href={`${baseUrl}/admin/field-day-2025`}
            >
              Admin Dashboard
            </Button>
          </Section>

          <Hr style={hr} />

          {/* Quick Stats */}
          <Section style={statsSection}>
            <Heading style={h3}>Quick Actions</Heading>

            <div style={quickActions}>
              <Link
                href={`${baseUrl}/admin/field-day-2025/orders`}
                style={quickLink}
              >
                📋 View All Orders
              </Link>
              <Link
                href={`${baseUrl}/admin/field-day-2025/export`}
                style={quickLink}
              >
                📊 Export Data
              </Link>
              <Link href={`mailto:${customerEmail}`} style={quickLink}>
                📧 Contact Customer
              </Link>
            </div>
          </Section>

          {/* Footer */}
          <Text style={footer}>
            This is an automated notification for Field Day 2025 order
            management.
          </Text>
        </Container>
      </Body>
    </Html>
  );
}

// Styles
const main = {
  backgroundColor: '#ffffff',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
};

const container = {
  margin: '0 auto',
  padding: '20px 0 48px',
  maxWidth: '560px',
};

const logoContainer = {
  textAlign: 'center' as const,
  margin: '0 0 40px',
};

const logoText = {
  fontSize: '24px',
  fontWeight: 'bold',
  textDecoration: 'none',
  display: 'block',
  textAlign: 'center' as const,
  margin: '0 auto',
};

const logoGreen = {
  color: '#16a34a',
};

const logoDark = {
  color: '#14532d',
};

const h1 = {
  color: '#1f2937',
  fontSize: '24px',
  fontWeight: 'bold',
  textAlign: 'center' as const,
  margin: '0 0 30px',
};

const h2 = {
  color: '#1f2937',
  fontSize: '18px',
  fontWeight: 'bold',
  margin: '0 0 16px',
};

const h3 = {
  color: '#1f2937',
  fontSize: '16px',
  fontWeight: 'bold',
  margin: '0 0 12px',
};

const text = {
  color: '#4b5563',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '0 0 16px',
};

const orderSection = {
  backgroundColor: '#f9fafb',
  borderRadius: '8px',
  padding: '20px',
  margin: '20px 0',
  border: '1px solid #e5e7eb',
};

const orderDetails = {
  margin: '16px 0',
};

const orderRow = {
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  padding: '6px 0',
  borderBottom: '1px solid #f3f4f6',
};

const orderLabel = {
  color: '#6b7280',
  fontSize: '14px',
  fontWeight: '500',
};

const orderValue = {
  color: '#1f2937',
  fontSize: '14px',
  fontWeight: '600',
};

const orderValueTotal = {
  color: '#059669',
  fontSize: '14px',
  fontWeight: 'bold',
};

const paymentSuccessful = {
  color: '#059669',
  fontSize: '14px',
  fontWeight: 'bold',
};

const paymentPending = {
  color: '#d97706',
  fontSize: '14px',
  fontWeight: 'bold',
};

const customerSection = {
  backgroundColor: '#eff6ff',
  borderRadius: '8px',
  padding: '20px',
  margin: '20px 0',
  border: '1px solid #bfdbfe',
};

const customerDetails = {
  margin: '16px 0',
};

const customerRow = {
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  padding: '6px 0',
};

const customerLabel = {
  color: '#1e40af',
  fontSize: '14px',
  fontWeight: '500',
};

const customerValue = {
  color: '#1f2937',
  fontSize: '14px',
  fontWeight: '600',
};

const emailLink = {
  color: '#2563eb',
  textDecoration: 'underline',
};

const phoneLink = {
  color: '#2563eb',
  textDecoration: 'underline',
};

const alertSection = {
  backgroundColor: '#fef3c7',
  borderRadius: '8px',
  padding: '20px',
  margin: '20px 0',
  border: '1px solid #fcd34d',
};

const alertText = {
  color: '#92400e',
  fontSize: '14px',
  lineHeight: '20px',
  margin: '0',
};

const stepsSection = {
  margin: '20px 0',
};

const stepsList = {
  color: '#4b5563',
  fontSize: '14px',
  lineHeight: '20px',
  paddingLeft: '20px',
  margin: '16px 0',
};

const stepsItem = {
  margin: '8px 0',
};

const buttonContainer = {
  textAlign: 'center' as const,
  margin: '32px 0',
};

const primaryButton = {
  backgroundColor: '#059669',
  borderRadius: '6px',
  color: '#ffffff',
  fontSize: '14px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '10px 20px',
  margin: '0 8px 8px 0',
};

const secondaryButton = {
  backgroundColor: '#6b7280',
  borderRadius: '6px',
  color: '#ffffff',
  fontSize: '14px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '10px 20px',
  margin: '0 8px 8px 0',
};

const hr = {
  borderColor: '#e5e7eb',
  margin: '32px 0',
};

const statsSection = {
  backgroundColor: '#f3f4f6',
  borderRadius: '8px',
  padding: '16px',
  margin: '20px 0',
};

const quickActions = {
  display: 'flex',
  flexDirection: 'column' as const,
  gap: '8px',
};

const quickLink = {
  color: '#374151',
  fontSize: '14px',
  textDecoration: 'underline',
  margin: '4px 0',
};

const footer = {
  color: '#9ca3af',
  fontSize: '12px',
  lineHeight: '16px',
  textAlign: 'center' as const,
  margin: '24px 0 0',
};
