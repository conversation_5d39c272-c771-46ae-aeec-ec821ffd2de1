import {
  Text,
  Section,
  Row,
  Column,
} from '@react-email/components';
import { EmailLayout } from '../../components/EmailLayout';
import { EmailButton } from '../../components/EmailButton';
import { formatPrice, formatDate } from '@/lib/utils/format';

interface NewSubscriptionAlertProps {
  subscriberData: {
    id: string;
    name: string;
    email: string;
    phone?: string;
    address?: string;
  };
  subscriptionData: {
    id: string;
    boxSize: string;
    frequency: string;
    paymentPlan: string;
    pickupLocation: string;
    nextDeliveryDate: Date;
    totalPrice: number;
    deliveriesRemaining: number;
    specialInstructions?: string;
    createdAt: Date;
  };
}

export function NewSubscriptionAlert({
  subscriberData,
  subscriptionData,
}: NewSubscriptionAlertProps) {
  const {
    id: subscriberId,
    name,
    email,
    phone,
    address,
  } = subscriberData;

  const {
    id: subscriptionId,
    boxSize,
    frequency,
    paymentPlan,
    pickupLocation,
    nextDeliveryDate,
    totalPrice,
    deliveriesRemaining,
    specialInstructions,
    createdAt,
  } = subscriptionData;

  return (
    <EmailLayout preview="New CSA subscription received">
      <Text
        style={{
          fontSize: '24px',
          fontWeight: 'bold',
          color: '#1f2937',
          margin: '0 0 20px 0',
        }}
      >
        🎉 New CSA Subscription!
      </Text>

      <Text
        style={{
          fontSize: '16px',
          color: '#4b5563',
          lineHeight: '24px',
          margin: '0 0 20px 0',
        }}
      >
        A new customer has just signed up for a CSA subscription. Here are the details:
      </Text>

      <Section
        style={{
          backgroundColor: '#dcfce7',
          padding: '20px',
          borderRadius: '8px',
          margin: '20px 0',
          border: '1px solid #16a34a',
        }}
      >
        <Text
          style={{
            fontSize: '18px',
            fontWeight: 'bold',
            color: '#15803d',
            margin: '0 0 15px 0',
          }}
        >
          Customer Information
        </Text>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '30%' }}>
            <Text style={{ fontSize: '14px', color: '#166534', margin: '0' }}>
              Name:
            </Text>
          </Column>
          <Column style={{ width: '70%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#15803d',
                margin: '0',
              }}
            >
              {name}
            </Text>
          </Column>
        </Row>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '30%' }}>
            <Text style={{ fontSize: '14px', color: '#166534', margin: '0' }}>
              Email:
            </Text>
          </Column>
          <Column style={{ width: '70%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#15803d',
                margin: '0',
              }}
            >
              {email}
            </Text>
          </Column>
        </Row>

        {phone && (
          <Row style={{ marginBottom: '10px' }}>
            <Column style={{ width: '30%' }}>
              <Text style={{ fontSize: '14px', color: '#166534', margin: '0' }}>
                Phone:
              </Text>
            </Column>
            <Column style={{ width: '70%' }}>
              <Text
                style={{
                  fontSize: '14px',
                  fontWeight: 'bold',
                  color: '#15803d',
                  margin: '0',
                }}
              >
                {phone}
              </Text>
            </Column>
          </Row>
        )}

        {address && (
          <Row style={{ marginBottom: '10px' }}>
            <Column style={{ width: '30%' }}>
              <Text style={{ fontSize: '14px', color: '#166534', margin: '0' }}>
                Address:
              </Text>
            </Column>
            <Column style={{ width: '70%' }}>
              <Text
                style={{
                  fontSize: '14px',
                  fontWeight: 'bold',
                  color: '#15803d',
                  margin: '0',
                }}
              >
                {address}
              </Text>
            </Column>
          </Row>
        )}

        <Row>
          <Column style={{ width: '30%' }}>
            <Text style={{ fontSize: '14px', color: '#166534', margin: '0' }}>
              Customer ID:
            </Text>
          </Column>
          <Column style={{ width: '70%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#15803d',
                margin: '0',
                fontFamily: 'monospace',
              }}
            >
              {subscriberId}
            </Text>
          </Column>
        </Row>
      </Section>

      <Section
        style={{
          backgroundColor: '#f7f9fa',
          padding: '20px',
          borderRadius: '8px',
          margin: '20px 0',
        }}
      >
        <Text
          style={{
            fontSize: '18px',
            fontWeight: 'bold',
            color: '#1f2937',
            margin: '0 0 15px 0',
          }}
        >
          Subscription Details
        </Text>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '40%' }}>
            <Text style={{ fontSize: '14px', color: '#6b7280', margin: '0' }}>
              Subscription ID:
            </Text>
          </Column>
          <Column style={{ width: '60%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#1f2937',
                margin: '0',
                fontFamily: 'monospace',
              }}
            >
              {subscriptionId}
            </Text>
          </Column>
        </Row>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '40%' }}>
            <Text style={{ fontSize: '14px', color: '#6b7280', margin: '0' }}>
              Box Size:
            </Text>
          </Column>
          <Column style={{ width: '60%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#1f2937',
                margin: '0',
              }}
            >
              {boxSize}
            </Text>
          </Column>
        </Row>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '40%' }}>
            <Text style={{ fontSize: '14px', color: '#6b7280', margin: '0' }}>
              Frequency:
            </Text>
          </Column>
          <Column style={{ width: '60%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#1f2937',
                margin: '0',
              }}
            >
              {frequency}
            </Text>
          </Column>
        </Row>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '40%' }}>
            <Text style={{ fontSize: '14px', color: '#6b7280', margin: '0' }}>
              Payment Plan:
            </Text>
          </Column>
          <Column style={{ width: '60%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#1f2937',
                margin: '0',
              }}
            >
              {paymentPlan}
            </Text>
          </Column>
        </Row>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '40%' }}>
            <Text style={{ fontSize: '14px', color: '#6b7280', margin: '0' }}>
              Pickup Location:
            </Text>
          </Column>
          <Column style={{ width: '60%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#1f2937',
                margin: '0',
              }}
            >
              {pickupLocation}
            </Text>
          </Column>
        </Row>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '40%' }}>
            <Text style={{ fontSize: '14px', color: '#6b7280', margin: '0' }}>
              Next Delivery:
            </Text>
          </Column>
          <Column style={{ width: '60%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#1f2937',
                margin: '0',
              }}
            >
              {formatDate(nextDeliveryDate)}
            </Text>
          </Column>
        </Row>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '40%' }}>
            <Text style={{ fontSize: '14px', color: '#6b7280', margin: '0' }}>
              Deliveries Remaining:
            </Text>
          </Column>
          <Column style={{ width: '60%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#1f2937',
                margin: '0',
              }}
            >
              {deliveriesRemaining}
            </Text>
          </Column>
        </Row>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '40%' }}>
            <Text style={{ fontSize: '14px', color: '#6b7280', margin: '0' }}>
              Total Paid:
            </Text>
          </Column>
          <Column style={{ width: '60%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#1f2937',
                margin: '0',
              }}
            >
              {formatPrice(totalPrice)}
            </Text>
          </Column>
        </Row>

        <Row>
          <Column style={{ width: '40%' }}>
            <Text style={{ fontSize: '14px', color: '#6b7280', margin: '0' }}>
              Created:
            </Text>
          </Column>
          <Column style={{ width: '60%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#1f2937',
                margin: '0',
              }}
            >
              {formatDate(createdAt)}
            </Text>
          </Column>
        </Row>
      </Section>

      {specialInstructions && (
        <Section
          style={{
            backgroundColor: '#fef3c7',
            padding: '15px',
            borderRadius: '8px',
            margin: '20px 0',
          }}
        >
          <Text
            style={{
              fontSize: '14px',
              fontWeight: 'bold',
              color: '#92400e',
              margin: '0 0 5px 0',
            }}
          >
            Special Instructions:
          </Text>
          <Text
            style={{
              fontSize: '14px',
              color: '#92400e',
              margin: '0',
            }}
          >
            {specialInstructions}
          </Text>
        </Section>
      )}

      <EmailButton 
        href={`https://asedafoods.org/admin/subscriptions/${subscriptionId}`}
        text="View in Admin Dashboard" 
      />

      <Text
        style={{
          fontSize: '14px',
          color: '#6b7280',
          lineHeight: '20px',
          margin: '20px 0 0 0',
        }}
      >
        This is an automated notification. The customer has been sent a confirmation email 
        and their subscription is now active in the system.
      </Text>
    </EmailLayout>
  );
}
