import { Text, Section, Row, Column } from '@react-email/components';
import { EmailLayout } from '../../components/EmailLayout';
import { EmailButton } from '../../components/EmailButton';
import { formatDate } from '@/lib/utils/format';

interface ContactFormNotificationProps {
  contactData: {
    id: string;
    name: string;
    email: string;
    message: string;
    submittedAt: Date;
  };
}

export function ContactFormNotification({
  contactData,
}: ContactFormNotificationProps) {
  const { id, name, email, message, submittedAt } = contactData;

  return (
    <EmailLayout preview='New contact form submission received'>
      <Text
        style={{
          fontSize: '24px',
          fontWeight: 'bold',
          color: '#1f2937',
          margin: '0 0 20px 0',
        }}
      >
        📧 New Contact Form Submission
      </Text>

      <Text
        style={{
          fontSize: '16px',
          color: '#4b5563',
          lineHeight: '24px',
          margin: '0 0 20px 0',
        }}
      >
        A new message has been submitted through the AsedaFoods contact form.
      </Text>

      <Section
        style={{
          backgroundColor: '#eff6ff',
          padding: '20px',
          borderRadius: '8px',
          margin: '20px 0',
          border: '1px solid #3b82f6',
        }}
      >
        <Text
          style={{
            fontSize: '18px',
            fontWeight: 'bold',
            color: '#1d4ed8',
            margin: '0 0 15px 0',
          }}
        >
          Contact Information
        </Text>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '25%' }}>
            <Text style={{ fontSize: '14px', color: '#1e40af', margin: '0' }}>
              Name:
            </Text>
          </Column>
          <Column style={{ width: '75%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#1d4ed8',
                margin: '0',
              }}
            >
              {name}
            </Text>
          </Column>
        </Row>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '25%' }}>
            <Text style={{ fontSize: '14px', color: '#1e40af', margin: '0' }}>
              Email:
            </Text>
          </Column>
          <Column style={{ width: '75%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#1d4ed8',
                margin: '0',
              }}
            >
              <a
                href={`mailto:${email}`}
                style={{ color: '#1d4ed8', textDecoration: 'underline' }}
              >
                {email}
              </a>
            </Text>
          </Column>
        </Row>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '25%' }}>
            <Text style={{ fontSize: '14px', color: '#1e40af', margin: '0' }}>
              Submitted:
            </Text>
          </Column>
          <Column style={{ width: '75%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#1d4ed8',
                margin: '0',
              }}
            >
              {formatDate(submittedAt)}
            </Text>
          </Column>
        </Row>

        <Row>
          <Column style={{ width: '25%' }}>
            <Text style={{ fontSize: '14px', color: '#1e40af', margin: '0' }}>
              Form ID:
            </Text>
          </Column>
          <Column style={{ width: '75%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#1d4ed8',
                margin: '0',
                fontFamily: 'monospace',
              }}
            >
              {id}
            </Text>
          </Column>
        </Row>
      </Section>

      <Section
        style={{
          backgroundColor: '#f7f9fa',
          padding: '20px',
          borderRadius: '8px',
          margin: '20px 0',
          border: '1px solid #e2e8f0',
        }}
      >
        <Text
          style={{
            fontSize: '18px',
            fontWeight: 'bold',
            color: '#1f2937',
            margin: '0 0 15px 0',
          }}
        >
          Message
        </Text>

        <Text
          style={{
            fontSize: '14px',
            color: '#4b5563',
            lineHeight: '20px',
            margin: '0',
            whiteSpace: 'pre-wrap',
            fontFamily: 'Arial, sans-serif',
            padding: '10px',
            backgroundColor: '#ffffff',
            borderRadius: '4px',
            border: '1px solid #d1d5db',
          }}
        >
          {message}
        </Text>
      </Section>

      <Section
        style={{
          backgroundColor: '#fef3c7',
          padding: '15px',
          borderRadius: '8px',
          margin: '20px 0',
        }}
      >
        <Text
          style={{
            fontSize: '14px',
            color: '#92400e',
            margin: '0',
          }}
        >
          <strong>Quick Actions:</strong> You can reply directly to this email
          to respond to {name}, or use the admin dashboard to manage this
          contact form submission.
        </Text>
      </Section>

      <div style={{ textAlign: 'center', margin: '20px 0' }}>
        <EmailButton
          href={`mailto:${email}?subject=Re: Your message to AsedaFoods&body=Hi ${name},%0D%0A%0D%0AThank you for contacting AsedaFoods. `}
          text='Reply to Customer'
          style={{ marginRight: '10px' }}
        />

        <EmailButton
          href={`https://asedafoods.org/admin/contact-forms/${id}`}
          text='View in Dashboard'
          style={{
            backgroundColor: '#6b7280',
            marginLeft: '10px',
          }}
        />
      </div>

      <Text
        style={{
          fontSize: '14px',
          color: '#6b7280',
          lineHeight: '20px',
          margin: '30px 0 0 0',
        }}
      >
        <strong>Response Guidelines:</strong>
      </Text>

      <ul
        style={{
          fontSize: '14px',
          color: '#6b7280',
          lineHeight: '20px',
          margin: '10px 0 0 20px',
          paddingLeft: '0',
        }}
      >
        <li style={{ marginBottom: '5px' }}>
          Aim to respond within 24 hours during business days
        </li>
        <li style={{ marginBottom: '5px' }}>
          Be friendly, helpful, and professional
        </li>
        <li style={{ marginBottom: '5px' }}>
          If it&apos;s a subscription question, direct them to their dashboard
        </li>
        <li style={{ marginBottom: '5px' }}>
          For complex issues, consider a phone call
        </li>
      </ul>

      <Text
        style={{
          fontSize: '14px',
          color: '#6b7280',
          lineHeight: '20px',
          margin: '20px 0 0 0',
        }}
      >
        This is an automated notification sent when someone submits the contact
        form on asedafoods.org.
      </Text>
    </EmailLayout>
  );
}
