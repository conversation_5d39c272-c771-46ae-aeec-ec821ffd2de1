import {
  Text,
  Section,
  Row,
  Column,
} from '@react-email/components';
import { EmailLayout } from '../../components/EmailLayout';
import { EmailButton } from '../../components/EmailButton';
import { formatDate } from '@/lib/utils/format';

interface NewsletterSignupNotificationProps {
  newsletterData: {
    id: string;
    email: string;
    signupDate: Date;
    source?: string;
    isReactivation?: boolean;
  };
}

export function NewsletterSignupNotification({
  newsletterData,
}: NewsletterSignupNotificationProps) {
  const { id, email, signupDate, source, isReactivation } = newsletterData;

  return (
    <EmailLayout preview="New newsletter subscription received">
      <Text
        style={{
          fontSize: '24px',
          fontWeight: 'bold',
          color: '#1f2937',
          margin: '0 0 20px 0',
        }}
      >
        📬 {isReactivation ? 'Newsletter Reactivation' : 'New Newsletter Signup'}
      </Text>

      <Text
        style={{
          fontSize: '16px',
          color: '#4b5563',
          lineHeight: '24px',
          margin: '0 0 20px 0',
        }}
      >
        {isReactivation 
          ? 'A previous subscriber has reactivated their newsletter subscription.'
          : 'Someone new has subscribed to the AsedaFoods newsletter!'
        }
      </Text>

      <Section
        style={{
          backgroundColor: isReactivation ? '#fef3c7' : '#dcfce7',
          padding: '20px',
          borderRadius: '8px',
          margin: '20px 0',
          border: `1px solid ${isReactivation ? '#fbbf24' : '#16a34a'}`,
        }}
      >
        <Text
          style={{
            fontSize: '18px',
            fontWeight: 'bold',
            color: isReactivation ? '#92400e' : '#15803d',
            margin: '0 0 15px 0',
          }}
        >
          Subscription Details
        </Text>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '30%' }}>
            <Text 
              style={{ 
                fontSize: '14px', 
                color: isReactivation ? '#78350f' : '#166534', 
                margin: '0' 
              }}
            >
              Email:
            </Text>
          </Column>
          <Column style={{ width: '70%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: isReactivation ? '#92400e' : '#15803d',
                margin: '0',
              }}
            >
              {email}
            </Text>
          </Column>
        </Row>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '30%' }}>
            <Text 
              style={{ 
                fontSize: '14px', 
                color: isReactivation ? '#78350f' : '#166534', 
                margin: '0' 
              }}
            >
              {isReactivation ? 'Reactivated:' : 'Signed Up:'}
            </Text>
          </Column>
          <Column style={{ width: '70%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: isReactivation ? '#92400e' : '#15803d',
                margin: '0',
              }}
            >
              {formatDate(signupDate)}
            </Text>
          </Column>
        </Row>

        {source && (
          <Row style={{ marginBottom: '10px' }}>
            <Column style={{ width: '30%' }}>
              <Text 
                style={{ 
                  fontSize: '14px', 
                  color: isReactivation ? '#78350f' : '#166534', 
                  margin: '0' 
                }}
              >
                Source:
              </Text>
            </Column>
            <Column style={{ width: '70%' }}>
              <Text
                style={{
                  fontSize: '14px',
                  fontWeight: 'bold',
                  color: isReactivation ? '#92400e' : '#15803d',
                  margin: '0',
                }}
              >
                {source}
              </Text>
            </Column>
          </Row>
        )}

        <Row>
          <Column style={{ width: '30%' }}>
            <Text 
              style={{ 
                fontSize: '14px', 
                color: isReactivation ? '#78350f' : '#166534', 
                margin: '0' 
              }}
            >
              Subscription ID:
            </Text>
          </Column>
          <Column style={{ width: '70%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: isReactivation ? '#92400e' : '#15803d',
                margin: '0',
                fontFamily: 'monospace',
              }}
            >
              {id}
            </Text>
          </Column>
        </Row>
      </Section>

      <Section
        style={{
          backgroundColor: '#eff6ff',
          padding: '15px',
          borderRadius: '8px',
          margin: '20px 0',
          border: '1px solid #3b82f6',
        }}
      >
        <Text
          style={{
            fontSize: '14px',
            color: '#1e40af',
            margin: '0',
          }}
        >
          <strong>Resend Integration:</strong> This email has been automatically added to the 
          &quot;General Audience&quot; list in Resend for future email campaigns. 
          {isReactivation 
            ? ' The subscriber has been sent a welcome back email.'
            : ' A welcome email has been sent to the new subscriber.'
          }
        </Text>
      </Section>

      {!isReactivation && (
        <Text
          style={{
            fontSize: '16px',
            color: '#4b5563',
            lineHeight: '24px',
            margin: '20px 0',
          }}
        >
          This new subscriber might be interested in:
        </Text>
      )}

      {!isReactivation && (
        <ul
          style={{
            fontSize: '16px',
            color: '#4b5563',
            lineHeight: '24px',
            margin: '0 0 20px 20px',
            paddingLeft: '0',
          }}
        >
          <li style={{ marginBottom: '8px' }}>
            Learning about CSA subscription options
          </li>
          <li style={{ marginBottom: '8px' }}>
            Seasonal produce information and recipes
          </li>
          <li style={{ marginBottom: '8px' }}>
            Farm updates and local agriculture news
          </li>
          <li style={{ marginBottom: '8px' }}>
            Special promotions or seasonal offers
          </li>
        </ul>
      )}

      <EmailButton 
        href="https://asedafoods.org/admin/newsletter"
        text="View Newsletter Subscribers" 
      />

      <Text
        style={{
          fontSize: '14px',
          color: '#6b7280',
          lineHeight: '20px',
          margin: '30px 0 0 0',
        }}
      >
        <strong>Next Steps:</strong>
      </Text>

      <ul
        style={{
          fontSize: '14px',
          color: '#6b7280',
          lineHeight: '20px',
          margin: '10px 0 0 20px',
          paddingLeft: '0',
        }}
      >
        <li style={{ marginBottom: '5px' }}>
          {isReactivation 
            ? 'Consider sending a personalized welcome back message'
            : 'Consider following up with information about CSA subscriptions'
          }
        </li>
        <li style={{ marginBottom: '5px' }}>
          Add this email to any relevant campaign segments in Resend
        </li>
        <li style={{ marginBottom: '5px' }}>
          Monitor engagement with future newsletter campaigns
        </li>
        <li style={{ marginBottom: '5px' }}>
          Track conversion from newsletter subscriber to CSA customer
        </li>
      </ul>

      <Text
        style={{
          fontSize: '14px',
          color: '#6b7280',
          lineHeight: '20px',
          margin: '20px 0 0 0',
        }}
      >
        This is an automated notification sent when someone subscribes to the newsletter 
        through the AsedaFoods website.
      </Text>
    </EmailLayout>
  );
}
