import { z } from 'zod';

// Base email validation
const emailSchema = z
  .string()
  .min(1, 'Email is required')
  .email('Please enter a valid email address');

// Base password validation
const passwordSchema = z
  .string()
  .min(8, 'Password must be at least 8 characters long')
  .regex(/(?=.*[a-z])/, 'Password must contain at least one lowercase letter')
  .regex(/(?=.*[A-Z])/, 'Password must contain at least one uppercase letter')
  .regex(/(?=.*\d)/, 'Password must contain at least one number');

// Sign in schema
export const signInSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Password is required'),
});

// Sign up schema
export const signUpSchema = z
  .object({
    name: z
      .string()
      .min(1, 'Name is required')
      .min(2, 'Name must be at least 2 characters long')
      .max(50, 'Name must be less than 50 characters'),
    email: emailSchema,
    password: passwordSchema,
    confirmPassword: z.string().min(1, 'Please confirm your password'),
    address: z
      .string()
      .min(1, 'Address is required for delivery')
      .min(10, 'Please enter a complete address')
      .max(200, 'Address must be less than 200 characters'),
    phone: z
      .string()
      .optional()
      .refine(
        (val) => !val || /^[\+]?[1-9][\d]{0,15}$/.test(val),
        'Please enter a valid phone number'
      ),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  });

// Forgot password schema
export const forgotPasswordSchema = z.object({
  email: emailSchema,
});

// Reset password schema
export const resetPasswordSchema = z
  .object({
    password: passwordSchema,
    confirmPassword: z.string().min(1, 'Please confirm your password'),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  });

// Update password schema
export const updatePasswordSchema = z
  .object({
    currentPassword: z.string().min(1, 'Current password is required'),
    newPassword: passwordSchema,
    confirmNewPassword: z.string().min(1, 'Please confirm your new password'),
  })
  .refine((data) => data.newPassword === data.confirmNewPassword, {
    message: 'Passwords do not match',
    path: ['confirmNewPassword'],
  })
  .refine((data) => data.currentPassword !== data.newPassword, {
    message: 'New password must be different from current password',
    path: ['newPassword'],
  });

// Type exports for form data
export type SignInFormData = z.infer<typeof signInSchema>;
export type SignUpFormData = z.infer<typeof signUpSchema>;
export type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;
export type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;
export type UpdatePasswordFormData = z.infer<typeof updatePasswordSchema>;

// Helper function to get password requirements
export const getPasswordRequirements = () => [
  'At least 8 characters long',
  'Contains at least one lowercase letter',
  'Contains at least one uppercase letter',
  'Contains at least one number',
];

// Helper function to validate password strength
export const validatePasswordStrength = (password: string) => {
  const requirements = [
    { test: password.length >= 8, message: 'At least 8 characters' },
    { test: /[a-z]/.test(password), message: 'One lowercase letter' },
    { test: /[A-Z]/.test(password), message: 'One uppercase letter' },
    { test: /\d/.test(password), message: 'One number' },
  ];

  return {
    score: requirements.filter((req) => req.test).length,
    total: requirements.length,
    requirements: requirements.map((req) => ({
      message: req.message,
      satisfied: req.test,
    })),
    isValid: requirements.every((req) => req.test),
  };
};
