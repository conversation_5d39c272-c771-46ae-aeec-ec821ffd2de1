import { Footer } from '@/components/layouts/footer';
import { NavbarWrapper } from '@/components/layouts/navbar/NavbarWrapper';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Aseda Foods - Farm Fresh Subscriptions',
  description:
    'Get the freshest, locally-sourced organic produce delivered straight to your door. Support local farmers while enjoying the best seasonal vegetables and fruits.',
};

export default function LandingLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <>
      <NavbarWrapper />
      {children}
      <Footer />
    </>
  );
}
