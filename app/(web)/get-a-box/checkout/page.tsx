'use client';

import StripePaymentForm from '@/components/payment/StripePaymentForm';
import SubscriptionSuccess from '@/components/subscription/SubscriptionSuccess';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import {
  BOX_SIZES,
  FREQUENCIES,
  PAYMENT_PLANS,
  PICKUP_LOCATIONS,
  SubscriptionData,
  calculatePrice,
  formatPrice,
  getNextDeliveryDate,
} from '@/lib/constants/subscription';
import { useUser } from '@/lib/hooks/useUser';
import { useSubscriptionStore } from '@/lib/store/subscription';
import { ArrowLeft, ArrowRight, CreditCard } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

export default function CheckoutPage() {
  const router = useRouter();
  const { user } = useUser();
  const [specialInstructions, setSpecialInstructions] = useState('');
  const [showPayment, setShowPayment] = useState(false);

  const {
    subscriptionData,
    subscriberId,
    updateSubscriptionData,
    paymentError,
    paymentSuccess,
    paymentData,
    setPaymentError,
    setPaymentSuccess,
    canProceedToCheckout,
  } = useSubscriptionStore();

  // Redirect if no subscription data
  useEffect(() => {
    if (
      !(subscriptionData.shareSize || subscriptionData.boxSize) ||
      !subscriptionData.frequency ||
      !subscriptionData.paymentPlan
    ) {
      toast.error('Please complete the subscription wizard first');
      router.push('/get-a-box');
      return;
    }
  }, [subscriptionData, router]);

  // Redirect if not authenticated or no subscriber ID
  useEffect(() => {
    if (!user || !subscriberId) {
      toast.error('Please sign in to complete your subscription');
      router.push('/get-a-box');
      return;
    }
  }, [user, subscriberId, router]);

  // Don't render anything if we're redirecting
  if (!canProceedToCheckout() || !subscriberId || !user) {
    return (
      <div className='min-h-screen bg-gray-50 flex items-center justify-center'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto'></div>
          <p className='mt-4 text-gray-600'>Loading...</p>
        </div>
      </div>
    );
  }

  // Show success page if payment was successful
  if (paymentSuccess && paymentData) {
    return (
      <div className='min-h-screen bg-gray-50 p-4'>
        <div className='container mx-auto py-8'>
          <SubscriptionSuccess
            subscriptionData={subscriptionData as SubscriptionData}
            paymentData={paymentData}
          />
          <div className='flex justify-center mt-8'>
            <Button
              onClick={() => router.push('/dashboard')}
              size='lg'
              className='bg-green-600 hover:bg-green-700'
            >
              Go to Dashboard
              <ArrowRight className='ml-2 h-4 w-4' />
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const shareSize =
    subscriptionData.shareSize || subscriptionData.boxSize || 'standard';

  const pricing = (() => {
    try {
      return calculatePrice(
        shareSize,
        subscriptionData.frequency!,
        subscriptionData.paymentPlan!
      );
    } catch (error) {
      console.error('Error calculating price:', error);
      // Redirect back to wizard if pricing calculation fails
      toast.error(
        'Invalid subscription data. Please complete the wizard again.'
      );
      router.push('/get-a-box');
      return null;
    }
  })();

  // Early return if pricing calculation failed
  if (!pricing) {
    return null;
  }

  const nextDeliveryDate = (() => {
    try {
      return getNextDeliveryDate(
        subscriptionData.frequency!
      ).toLocaleDateString('en-US', {
        month: 'long',
        day: 'numeric',
        year: 'numeric',
      });
    } catch (error) {
      console.error('Error calculating next delivery date:', error);
      return 'TBD';
    }
  })();

  const handleProceedToPayment = () => {
    // Update subscription data with special instructions
    updateSubscriptionData({
      specialInstructions: specialInstructions.trim(),
    });
    setShowPayment(true);
  };

  const handlePaymentSuccess = (data: any) => {
    setPaymentError(null);
    setPaymentSuccess(true, data);
  };

  const handlePaymentError = (error: string) => {
    setPaymentError(error);
  };

  const handleBackToWizard = () => {
    router.push('/get-a-box');
  };

  return (
    <div className='min-h-screen bg-gray-50 p-4'>
      <div className='container mx-auto py-8'>
        <div className='text-center mb-8'>
          <h1 className='text-3xl font-bold text-gray-900 mb-4'>
            Complete Your Subscription
          </h1>
          <p className='text-lg text-gray-600 max-w-2xl mx-auto'>
            Review your subscription details and complete your payment to start
            receiving fresh produce.
          </p>
        </div>

        {!showPayment ? (
          <Card className='max-w-4xl mx-auto'>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <CreditCard className='h-5 w-5' />
                Review Your Subscription
              </CardTitle>
            </CardHeader>
            <CardContent className='space-y-6'>
              {/* Subscription Summary */}
              <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
                {/* Left Column - Subscription Details */}
                <div className='space-y-4'>
                  <h3 className='text-lg font-semibold'>
                    Subscription Details
                  </h3>

                  <div className='space-y-3'>
                    <div className='flex justify-between'>
                      <span className='text-sm text-gray-600'>Share Type:</span>
                      <span className='font-medium'>
                        {BOX_SIZES[shareSize]?.name}
                      </span>
                    </div>

                    <div className='flex justify-between'>
                      <span className='text-sm text-gray-600'>Frequency:</span>
                      <span className='font-medium'>
                        {FREQUENCIES[subscriptionData.frequency!]?.name}
                      </span>
                    </div>

                    <div className='flex justify-between'>
                      <span className='text-sm text-gray-600'>
                        Payment Plan:
                      </span>
                      <span className='font-medium'>
                        {PAYMENT_PLANS[subscriptionData.paymentPlan!]?.name}
                      </span>
                    </div>

                    <div className='flex justify-between'>
                      <span className='text-sm text-gray-600'>
                        Pickup Location:
                      </span>
                      <span className='font-medium'>
                        {
                          PICKUP_LOCATIONS[subscriptionData.pickupLocation!]
                            ?.name
                        }
                      </span>
                    </div>

                    <div className='flex justify-between'>
                      <span className='text-sm text-gray-600'>
                        Next Delivery:
                      </span>
                      <span className='font-medium'>{nextDeliveryDate}</span>
                    </div>
                  </div>
                </div>

                {/* Right Column - Pricing */}
                <div className='space-y-4'>
                  <h3 className='text-lg font-semibold'>Pricing Summary</h3>

                  <div className='bg-gray-50 rounded-lg p-4 space-y-3'>
                    <div className='flex justify-between'>
                      <span className='text-sm text-gray-600'>Base Price:</span>
                      <span>{formatPrice(pricing.basePrice)}</span>
                    </div>

                    {pricing.frequencyDiscount > 0 && (
                      <div className='flex justify-between text-green-600'>
                        <span className='text-sm'>Frequency Discount:</span>
                        <span>-{formatPrice(pricing.frequencyDiscount)}</span>
                      </div>
                    )}

                    {pricing.paymentPlanDiscount > 0 && (
                      <div className='flex justify-between text-green-600'>
                        <span className='text-sm'>Payment Plan Discount:</span>
                        <span>-{formatPrice(pricing.paymentPlanDiscount)}</span>
                      </div>
                    )}

                    <Separator />

                    <div className='flex justify-between'>
                      <span className='text-sm text-gray-600'>
                        Number of pickups:
                      </span>
                      <span>
                        {
                          PAYMENT_PLANS[subscriptionData.paymentPlan!]
                            .deliveries
                        }
                      </span>
                    </div>

                    <Separator />

                    <div className='flex justify-between text-lg font-semibold'>
                      <span>Total:</span>
                      <span>{formatPrice(pricing.totalPrice)}</span>
                    </div>

                    {pricing.savings > 0 && (
                      <div className='flex justify-between text-green-600 font-medium'>
                        <span>You save:</span>
                        <span>{formatPrice(pricing.savings)}</span>
                      </div>
                    )}
                  </div>

                  {pricing.totalDiscount > 0 && (
                    <div className='pt-2'>
                      <Badge className='bg-green-100 text-green-800'>
                        {pricing.totalDiscount}% Total Discount Applied
                      </Badge>
                    </div>
                  )}
                </div>
              </div>

              {/* Special Instructions */}
              <div className='space-y-2'>
                <Label htmlFor='special-instructions'>
                  Special Instructions (Optional)
                </Label>
                <Textarea
                  id='special-instructions'
                  placeholder='Any special delivery instructions or dietary preferences...'
                  value={specialInstructions}
                  onChange={(e) => setSpecialInstructions(e.target.value)}
                  className='min-h-[100px]'
                />
              </div>

              {/* Action Buttons */}
              <div className='flex justify-between pt-4'>
                <Button variant='outline' onClick={handleBackToWizard}>
                  <ArrowLeft className='mr-2 h-4 w-4' />
                  Back
                </Button>

                <Button
                  onClick={handleProceedToPayment}
                  className='bg-green-600 hover:bg-green-700 text-white py-3 px-6'
                  size='lg'
                >
                  <CreditCard className='mr-2 h-4 w-4' />
                  Proceed to Payment - {formatPrice(pricing.totalPrice)}
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className='max-w-2xl mx-auto'>
            {paymentError && (
              <Alert variant='destructive' className='mb-6'>
                <AlertDescription>{paymentError}</AlertDescription>
              </Alert>
            )}

            <StripePaymentForm
              subscriptionData={subscriptionData as SubscriptionData}
              subscriberId={subscriberId}
              onSuccess={handlePaymentSuccess}
              onError={handlePaymentError}
            />
          </div>
        )}
      </div>
    </div>
  );
}
