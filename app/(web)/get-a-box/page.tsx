import { Suspense } from 'react';
import { authServer } from '@/lib/utils/auth-server';
import {
  getCurrentUserSubscriber,
  hasExistingSubscription,
} from '@/lib/services/server/subscription-server';
import { TriangleAlert } from 'lucide-react';
import { PPRErrorBoundary } from '@/components/ui/ppr-error-boundary';
import { SubscriptionWizardRenderer } from '@/components/subscription/SubscriptionWizardRenderer';
import { ExistingSubscriptionErrorButtons } from '@/components/subscription/ExistingSubscriptionErrorButtons';

// Enable Partial Prerendering for this page (when Next.js supports it)
// export const experimental_ppr = true;

// Dynamic components for PPR
async function SubscriptionChecker() {
  const { data: user } = await authServer.getCurrentUser();

  if (!user) {
    return <SubscriptionWizardRenderer userType='guest' />;
  }

  try {
    const subscriber = await getCurrentUserSubscriber();
    const { hasExisting, existingSubscription } = await hasExistingSubscription(
      subscriber.id
    );

    if (hasExisting && existingSubscription) {
      const status = existingSubscription.status || 'active';
      return (
        <ExistingSubscriptionError
          message={`You already have a ${status} subscription. Please cancel it before creating a new one. Note: Paused subscriptions also prevent new subscription creation.`}
        />
      );
    }

    return (
      <SubscriptionWizardRenderer
        userType='authenticated'
        subscriber={subscriber}
        user={user}
      />
    );
  } catch {
    return (
      <SubscriptionWizardRenderer
        userType='authenticated'
        subscriber={null}
        user={user}
      />
    );
  }
}

// This needs to be a separate client component since it uses onClick handlers
function ExistingSubscriptionError({ message }: { message: string }) {
  return (
    <div className='max-w-2xl mx-auto'>
      <div className='bg-red-50 border border-red-200 rounded-lg p-6 text-center'>
        <div className='text-red-600 mb-4'>
          <TriangleAlert className='h-12 w-12 mx-auto' />
        </div>
        <h3 className='text-lg font-semibold text-red-800 mb-2'>
          Cannot Create New Subscription
        </h3>
        <p className='text-red-700 mb-6'>{message}</p>
        <ExistingSubscriptionErrorButtons />
      </div>
    </div>
  );
}

export default function GetABoxPage() {
  return (
    <div className='min-h-screen bg-gray-50 p-4'>
      <div className='container mx-auto py-8'>
        {/* Static header */}
        <div className='text-center mb-8'>
          <h1 className='text-3xl font-bold text-gray-900 mb-4'>
            Get Your Fresh Produce Box
          </h1>
          <p className='text-lg text-gray-600 max-w-2xl mx-auto'>
            Choose your perfect subscription plan and start receiving fresh,
            locally-sourced produce delivered right to your door.
          </p>
        </div>

        {/* Dynamic subscription wizard with PPR */}
        <PPRErrorBoundary>
          <Suspense
            fallback={
              <div className='min-h-screen bg-gray-50 flex items-center justify-center'>
                <div className='text-center'>
                  <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto'></div>
                  <p className='mt-4 text-gray-600'>Loading...</p>
                </div>
              </div>
            }
          >
            <SubscriptionChecker />
          </Suspense>
        </PPRErrorBoundary>
      </div>
    </div>
  );
}
