import { FieldDay2025ErrorFallback } from '@/components/field-day-2025/FieldDay2025ErrorFallback';
import { FieldDay2025ProductPage } from '@/components/field-day-2025/FieldDay2025ProductPage';
import { FieldDay2025ProductSkeleton } from '@/components/field-day-2025/FieldDay2025ProductSkeleton';
import { PPRErrorBoundary } from '@/components/ui/ppr-error-boundary';
import { Metadata } from 'next';
import { Suspense } from 'react';

// SEO metadata for the Field Day 2025 product page
export const metadata: Metadata = {
  title: 'Field Day 2025 - Premium Produce Box | AsedaFoods',
  description:
    'Order your Field Day 2025 premium produce box. Fresh, locally-sourced vegetables and fruits perfect for the event. Pickup only - $35.99.',
  keywords:
    'field day 2025, produce box, local farm, fresh vegetables, organic, pickup only, event catering',
  openGraph: {
    title: 'Field Day 2025 - Premium Produce Box',
    description:
      'Fresh, locally-sourced produce box for Field Day 2025. Perfect for families and groups. Pickup only.',
    images: ['/img/diverse-local-vendors-preparing-healthy-market-counter.jpg'],
    type: 'website',
    url: '/field-day-2025',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Field Day 2025 - Premium Produce Box',
    description: 'Fresh, locally-sourced produce box for Field Day 2025',
    images: ['/img/diverse-local-vendors-preparing-healthy-market-counter.jpg'],
  },
  robots: {
    index: true,
    follow: true,
  },
  alternates: {
    canonical: '/field-day-2025',
  },
};

// Structured data for SEO
const structuredData = {
  '@context': 'https://schema.org',
  '@type': 'Product',
  name: 'Field Day 2025 - Premium Produce Box',
  description:
    'Premium seasonal produce box specially curated for Field Day 2025. Perfect for families and groups looking to enjoy fresh, locally-sourced vegetables and fruits.',
  image: [
    '/img/diverse-local-vendors-preparing-healthy-market-counter.jpg',
    '/img/african-man-harvesting-vegetables.jpg',
    '/img/womanfarmer.jpg',
  ],
  brand: {
    '@type': 'Brand',
    name: 'AsedaFoods',
  },
  offers: {
    '@type': 'Offer',
    price: '35.99',
    priceCurrency: 'USD',
    availability: 'https://schema.org/InStock',
    seller: {
      '@type': 'Organization',
      name: 'AsedaFoods',
    },
  },
  category: 'Food & Beverages',
  aggregateRating: {
    '@type': 'AggregateRating',
    ratingValue: '4.8',
    reviewCount: '127',
  },
};

export default function FieldDay2025Page() {
  return (
    <>
      {/* Structured Data for SEO */}
      <script
        type='application/ld+json'
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />

      <div className='min-h-screen bg-gray-50'>
        {/* Hero Section */}
        <section className='bg-gradient-to-br from-green-600 via-green-700 to-green-800 text-white'>
          <div className='container mx-auto px-4 py-16'>
            <div className='text-center max-w-4xl mx-auto'>
              <div className='mb-6'>
                <span className='inline-block bg-white/20 text-white px-4 py-2 rounded-full text-sm font-medium uppercase tracking-wide'>
                  Special Event Order
                </span>
              </div>
              <h1 className='text-4xl md:text-6xl font-bold mb-6'>
                Field Day 2025
                <span className='block text-green-200'>
                  Premium Produce Box
                </span>
              </h1>
              <p className='text-xl md:text-2xl text-green-100 mb-8 leading-relaxed'>
                Fresh, locally-sourced vegetables and fruits specially curated
                for Field Day 2025. Perfect for families and groups looking to
                enjoy the best seasonal produce.
              </p>
              <div className='flex flex-col sm:flex-row items-center justify-center gap-4 text-lg'>
                <div className='flex items-center gap-2'>
                  <span className='w-2 h-2 bg-green-300 rounded-full'></span>
                  <span>Pickup Only</span>
                </div>
                <div className='flex items-center gap-2'>
                  <span className='w-2 h-2 bg-green-300 rounded-full'></span>
                  <span>Seasonal Items</span>
                </div>
                <div className='flex items-center gap-2'>
                  <span className='w-2 h-2 bg-green-300 rounded-full'></span>
                  <span>Feeds 3-4 People</span>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Main Product Content */}
        <main className='container mx-auto px-4 py-12'>
          <PPRErrorBoundary
            fallback={<FieldDay2025ErrorFallback />}
            showRetry={true}
          >
            <Suspense fallback={<FieldDay2025ProductSkeleton />}>
              <FieldDay2025ProductPage />
            </Suspense>
          </PPRErrorBoundary>
        </main>

        {/* Event Information Section */}
        <section className='bg-white py-16'>
          <div className='container mx-auto px-4'>
            <div className='text-center max-w-3xl mx-auto'>
              <h2 className='text-3xl font-bold text-gray-900 mb-6'>
                About Field Day 2025
              </h2>
              <p className='text-lg text-gray-600 mb-8 leading-relaxed'>
                Join us for an amazing day of community, fresh food, and fun
                activities! Our specially curated produce boxes are perfect for
                sharing with family and friends during this exciting event.
              </p>
              <div className='grid md:grid-cols-3 gap-8'>
                <div className='text-center'>
                  <div className='w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4'>
                    <span className='text-2xl'>🌱</span>
                  </div>
                  <h3 className='font-semibold text-gray-900 mb-2'>
                    Fresh & Local
                  </h3>
                  <p className='text-gray-600'>
                    Sourced from our trusted local farm partners
                  </p>
                </div>
                <div className='text-center'>
                  <div className='w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4'>
                    <span className='text-2xl'>📅</span>
                  </div>
                  <h3 className='font-semibold text-gray-900 mb-2'>
                    Event Special
                  </h3>
                  <p className='text-gray-600'>
                    Specially curated for Field Day 2025 attendees
                  </p>
                </div>
                <div className='text-center'>
                  <div className='w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4'>
                    <span className='text-2xl'>🚚</span>
                  </div>
                  <h3 className='font-semibold text-gray-900 mb-2'>
                    Easy Pickup
                  </h3>
                  <p className='text-gray-600'>
                    Convenient pickup at Elite Bodies Fitness
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className='bg-gray-50 py-16'>
          <div className='container mx-auto px-4'>
            <div className='max-w-3xl mx-auto'>
              <h2 className='text-3xl font-bold text-center text-gray-900 mb-12'>
                Frequently Asked Questions
              </h2>
              <div className='space-y-6'>
                <div className='bg-white rounded-lg p-6 shadow-sm'>
                  <h3 className='font-semibold text-gray-900 mb-2'>
                    When can I pick up my order?
                  </h3>
                  <p className='text-gray-600'>
                    Orders will be ready for pickup on the day of Field Day
                    2025. You&apos;ll receive an email confirmation with
                    specific pickup times and instructions.
                  </p>
                </div>
                <div className='bg-white rounded-lg p-6 shadow-sm'>
                  <h3 className='font-semibold text-gray-900 mb-2'>
                    What&apos;s included in the medium box?
                  </h3>
                  <p className='text-gray-600'>
                    Each medium box contains seasonal items including fresh
                    vegetables, fruits, and herbs. All produce is locally
                    sourced and sustainably grown.
                  </p>
                </div>
                <div className='bg-white rounded-lg p-6 shadow-sm'>
                  <h3 className='font-semibold text-gray-900 mb-2'>
                    Can I modify my order after placing it?
                  </h3>
                  <p className='text-gray-600'>
                    Please contact us as soon as possible if you need to modify
                    your order. We&apos;ll do our best to accommodate changes
                    before the preparation deadline.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </>
  );
}
