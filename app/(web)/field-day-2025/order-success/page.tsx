import { Suspense } from 'react';
import { Metadata } from 'next';
import { OrderSuccessContent } from '@/components/field-day-2025/OrderSuccessContent';

export const metadata: Metadata = {
  title: 'Order Confirmed - Field Day 2025 | AsedaFoods',
  description:
    'Your Field Day 2025 produce box order has been confirmed. Check your email for pickup details.',
  robots: {
    index: false,
    follow: false,
  },
};

export default function OrderSuccessPage() {
  return (
    <div className='min-h-screen bg-gray-50'>
      <div className='container mx-auto px-4 py-16'>
        <Suspense
          fallback={
            <div className='max-w-2xl mx-auto text-center'>
              <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4'></div>
              <p className='text-gray-600'>Loading order confirmation...</p>
            </div>
          }
        >
          <OrderSuccessContent />
        </Suspense>
      </div>
    </div>
  );
}
