// import { Suspense } from 'react';
import NewsletterSubscription from '@/components/forms/NewsletterSubscription';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { buttonVariants } from '@/components/ui/button';

import { hero } from '@/lib/constants/imgs';
import { cn } from '@/lib/utils';
import { Check } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

// PPR Dynamic Components
// import { PersonalizedRecommendations } from '@/components/homepage/PersonalizedRecommendations';
// import { RealTimeInventoryStatus } from '@/components/homepage/RealTimeInventoryStatus';
// import {
//   PersonalizationSkeleton,
//   InventorySkeleton,
// } from '@/components/ui/skeletons';
// import { PPRErrorBoundary } from '@/components/ui/ppr-error-boundary';

// Enable Partial Prerendering for this page (when Next.js supports it)
// export const experimental_ppr = true;
import {
  aboutQualities,
  benefits,
  faqData,
  freshSelectionImages,
  processSteps,
  subscriptionPlans,
} from '@/lib/constants/data';

export default function Home() {
  return (
    <main>
      {/* hero */}
      <section className='bg-green-50 md:h-168 flex flex-col p-4 pt-10 md:pt-4 justify-center items-center w-full'>
        <div className='grid md:grid-cols-2 gap-4 max-w-7xl'>
          <div className='flex flex-col justify-center p-4'>
            <div className='flex flex-col justify-center space-y-6'>
              <h2 className='text-4xl md:text-6xl font-black'>
                Farm-fresh produce{' '}
                <span className='text-green-600'>
                  at pickup locations near you
                </span>
              </h2>
              <p className='text-xl  text-neutral-500'>
                Skip the grocery store and enjoy seasonal, locally grown
                vegetables and fruits. Support local farmers and eat healthier
                with Aseda Foods.
              </p>
            </div>
            <div className='py-4 pt-8 flex items-center space-x-4'>
              <Link href={'/get-a-box'} className={cn(buttonVariants({}), '')}>
                Start a Subscription
              </Link>
              <Link
                href={'#subscriptionplans'}
                className={cn(buttonVariants({ variant: 'secondary' }), '')}
              >
                View Plans
              </Link>
            </div>
          </div>
          <div className='flex p-4 items-center justify-center'>
            <Image alt='heroimg' src={hero} className='rounded-sm' />
          </div>
        </div>
      </section>

      {/* Dynamic personalized content */}
      {/* <PPRErrorBoundary>
        <Suspense fallback={<PersonalizationSkeleton />}>
          <PersonalizedRecommendations />
        </Suspense>
      </PPRErrorBoundary> */}

      {/* about */}
      <section id='aboutus'>
        <div className='flex flex-col p-4 items-center justify-center'>
          <div className='pt-20 pb-4'>
            <p className='uppercase text-green-600 text-lg font-medium'>
              About Us
            </p>
          </div>
          <div>
            <h2 className='text-4xl font-black'>About AsedaFoods</h2>
          </div>
          <div className='py-10 mx-auto max-w-4xl flex flex-col space-y-8 *:text-xl p-4'>
            <p>At AsedaFoods, gratitude is at the heart of everything we do.</p>
            <p>
              Aseda means gratitude in the Akan languages of West Africa.
              It&apos;s more than a word to us — it&apos;s the spirit behind our
              work. We believe food is one of the most powerful ways to express
              care, build trust, and strengthen communities. Whether we&apos;re
              sourcing fresh produce, preparing for delivery, or listening to
              the needs of our partners, we lead with appreciation — for the
              growers, for the people we serve, and for the land that makes it
              all possible.
            </p>
            <div className='p-6 bg-green-100/50 rounded-lg'>
              <p className='text-2xl font-bold text-green-600'>Our Mission</p>
              <p className='italic'>
                To make fresh, nourishing food more accessible by building a
                simple, respectful, and community-rooted delivery model — one
                powered by gratitude.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* how it works */}
      <section id='howitworks' className='py-20 bg-gray-50'>
        <div className='flex flex-col p-4 items-center justify-center'>
          <div className='pb-4'>
            <p className='uppercase text-green-600 text-lg font-medium'>
              Simple Process
            </p>
          </div>
          <div className='text-center max-w-4xl'>
            <h2 className='text-4xl font-black mb-4'>How Aseda Foods Works</h2>
            <p className='text-xl text-neutral-500 mb-16'>
              Getting fresh produce delivered to your door is easy with our
              simple 4-step process.
            </p>
          </div>
        </div>
        <div className='mx-auto max-w-7xl w-full px-4'>
          <div className='grid md:grid-cols-4 gap-8 mb-12'>
            {processSteps.map((step) => (
              <div
                key={step.id}
                className='flex flex-col items-center text-center space-y-4'
              >
                <div className='w-16 h-16 rounded-full bg-green-200 flex items-center justify-center mb-4'>
                  <span className='text-2xl font-bold text-green-700'>
                    {step.step}
                  </span>
                </div>
                <h3 className='text-xl font-semibold text-gray-900 mb-2'>
                  {step.title}
                </h3>
                <p className='text-neutral-600 text-base leading-relaxed'>
                  {step.description}
                </p>
              </div>
            ))}
          </div>
          <div className='flex justify-center'>
            <Link
              href={'/get-a-box'}
              className={cn(buttonVariants({}), 'px-8 py-3 text-lg')}
            >
              Choose Your Box
            </Link>
          </div>
        </div>
      </section>

      {/* Dynamic inventory status */}
      {/* <PPRErrorBoundary>
        <Suspense fallback={<InventorySkeleton />}>
          <RealTimeInventoryStatus />
        </Suspense>
      </PPRErrorBoundary> */}

      {/* subscription plans */}
      <section id='subscriptionplans' className='py-20'>
        <div className='flex flex-col p-4 items-center justify-center'>
          <div className='pb-4'>
            <p className='uppercase text-green-600 text-lg font-medium'>
              Subscription Plans
            </p>
          </div>
          <div className='text-center max-w-4xl'>
            <h2 className='text-4xl font-black mb-4'>
              Choose the Perfect Box for You
            </h2>
            <p className='text-xl text-neutral-500 mb-16'>
              Select the right size for your household and the delivery
              frequency that suits your lifestyle.
            </p>
          </div>
        </div>
        <div className='mx-auto max-w-7xl w-full px-4'>
          {/* Pricing Plans */}
          <div className='grid md:grid-cols-3 gap-8 mb-16'>
            <div></div>
            {subscriptionPlans.map((plan) => (
              <div
                key={plan.id}
                className={cn(
                  'relative flex flex-col bg-white p-8 rounded-xl border space-y-6',
                  plan.isPopular
                    ? 'border-green-500 shadow-lg'
                    : 'border-gray-200'
                )}
              >
                {plan.isPopular && (
                  <div className='absolute -top-4 left-1/2 transform -translate-x-1/2'>
                    <span className='bg-green-500 text-white px-4 py-2 rounded-full text-sm font-medium'>
                      Most Popular
                    </span>
                  </div>
                )}
                <div>
                  <h3 className='text-2xl font-bold text-gray-900 mb-2'>
                    {plan.name}
                  </h3>
                  <p className='text-neutral-600'>{plan.description}</p>
                  {/* <div className='flex items-baseline'>
                    <span className='text-4xl font-bold text-gray-900'>
                      {plan.price}
                    </span>
                    <span className='text-neutral-500 ml-2'>
                      {plan.priceNote}
                    </span>
                  </div> */}
                </div>
                <ul className='space-y-3 flex-1'>
                  {plan.features.map((feature, index) => (
                    <li key={index} className='flex items-center'>
                      <Check className='size-5 text-green-500 mr-3 shrink-0' />
                      <span className='text-neutral-700'>{feature}</span>
                    </li>
                  ))}
                </ul>
                <Link
                  href={'/get-a-box'}
                  className={cn(
                    buttonVariants({ variant: plan.buttonVariant }),
                    'w-full py-3'
                  )}
                >
                  {plan.buttonText}
                </Link>
              </div>
            ))}
          </div>
        </div>
      </section>
      {/* benefits */}
      <section className='py-20'>
        <div className='flex flex-col p-4 items-center justify-center'>
          <div className='pb-4'>
            <p className='uppercase text-green-600 text-lg font-medium'>
              Benefits
            </p>
          </div>
          <div className='text-center max-w-4xl'>
            <h2 className='text-4xl font-black mb-4'>
              Why Choose Aseda Foods?
            </h2>
            <p className='text-xl text-neutral-500 mb-16'>
              We&apos;re committed to connecting you directly with local farmers
              for the freshest produce possible.
            </p>
          </div>
        </div>
        <div className='mx-auto max-w-7xl w-full px-4'>
          <div className='grid md:grid-cols-3 gap-8'>
            {benefits.map((benefit) => (
              <div
                key={benefit.id}
                className='flex flex-col bg-white p-8 rounded-xl border border-gray-100 space-y-4'
              >
                <div className='flex items-center space-x-4'>
                  <div className='p-3 rounded-full bg-green-100'>
                    <benefit.icon className='size-6 text-green-600' />
                  </div>
                  <h3 className='text-xl font-semibold text-gray-900'>
                    {benefit.title}
                  </h3>
                </div>
                <p className='text-neutral-600 text-base leading-relaxed'>
                  {benefit.message}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>
      {/* fresh selection */}
      <section id='freshselection' className='py-20 bg-gray-50'>
        <div className='flex flex-col p-4 items-center justify-center'>
          <div className='pb-4'>
            <p className='uppercase text-green-600 text-lg font-medium'>
              Fresh Selection
            </p>
          </div>
          <div className='text-center max-w-4xl'>
            <h2 className='text-4xl font-black mb-4'>Farm-Fresh Produce</h2>
            <p className='text-xl text-neutral-500 mb-16'>
              Our boxes feature these fresh, locally grown items from
              sustainable farms.
            </p>
          </div>
        </div>
        <div className='mx-auto max-w-7xl w-full px-4'>
          <div className='grid md:grid-cols-3 gap-8 mb-8'>
            {freshSelectionImages.map((image) => (
              <div
                key={image.id}
                className='relative overflow-hidden rounded-xl shadow-lg'
              >
                <Image
                  src={image.src}
                  alt={image.alt}
                  className='w-full h-80 object-cover transition-transform duration-300 hover:scale-105'
                />
              </div>
            ))}
          </div>
          <div className='text-center'>
            <p className='text-neutral-500 text-sm'>
              * Contents vary weekly based on seasonal availability and farm
              harvest
            </p>
          </div>
        </div>
      </section>
      {/* faqs */}
      <section id='faqs' className='py-20'>
        <div className='flex flex-col p-4 items-center justify-center'>
          <div className='pb-4'>
            <p className='uppercase text-green-600 text-lg font-medium'>FAQ</p>
          </div>
          <div className='text-center max-w-4xl'>
            <h2 className='text-4xl font-black mb-4'>
              Frequently Asked Questions
            </h2>
            <p className='text-xl text-neutral-500 mb-16'>
              Everything you need to know about Harvest Box subscription
              service.
            </p>
          </div>
        </div>
        <div className='mx-auto max-w-4xl w-full px-4'>
          <Accordion type='single' collapsible className='w-full'>
            {faqData.map((faq) => (
              <AccordionItem key={faq.id} value={`item-${faq.id}`}>
                <AccordionTrigger className='text-left text-lg font-semibold text-gray-900 hover:text-green-600'>
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent className='text-neutral-600 text-base leading-relaxed'>
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
          <div className='text-center mt-12'>
            <p className='text-neutral-600 mb-4'>Have more questions?</p>
            <Link
              href='#'
              className='text-green-600 hover:text-green-700 font-medium underline'
            >
              Contact our support team
            </Link>
          </div>
        </div>
      </section>
      {/* about continued */}
      <section id='aboutus'>
        <div className='flex flex-col p-4 items-center justify-center'>
          <div className='py-10 mx-auto max-w-4xl flex flex-col space-y-8 *:text-xl p-4'>
            <p>
              We believe good food should be easy to access, and the systems
              that bring it to people should reflect the same care that goes
              into growing it. Our work bridges the gap between those who grow
              food and those who need it — with humility, efficiency, and care.
              Community Building stronger connections between farmers and
              families through food. Gratitude
            </p>
          </div>
        </div>
        <div className='mx-auto max-w-7xl w-full p-4'>
          <div className='grid md:grid-cols-3 gap-4'>
            {aboutQualities.map((quality) => (
              <div
                key={quality.id}
                className='flex flex-col items-center bg-green-100/50 p-12 rounded-xl space-y-4'
              >
                <div className='p-4 rounded-full bg-green-200/40'>
                  <quality.icon className='size-7 text-green-600' />
                </div>
                <h3 className='text-2xl font-medium text-green-600'>
                  {quality.title}
                </h3>
                <p className='text-neutral-500 text-center text-lg'>
                  {quality.message}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>
      {/* newsletter signup */}
      <section className='py-20 bg-green-600'>
        <div className='flex flex-col p-4 items-center justify-center text-center'>
          <div className='pb-4'>
            <p className='uppercase text-white text-lg font-medium'>
              Stay Updated
            </p>
          </div>
          <div className='max-w-4xl'>
            <h2 className='text-4xl font-black mb-4 text-white'>
              Join Our Newsletter
            </h2>
            <p className='text-xl text-green-100 mb-12'>
              Get seasonal recipes, produce guides, and exclusive offers
              straight to your inbox.
            </p>
          </div>
          <NewsletterSubscription />
          <div className='mt-6'>
            <p className='text-green-100 text-sm'>
              We respect your privacy. Unsubscribe at any time.
            </p>
          </div>
        </div>
      </section>
    </main>
  );
}
