'use client';

import { useState, useEffect, useCallback, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { PaymentForm } from '@/components/field-day-2025/PaymentForm';
import {
  AlertCircle,
  ArrowLeft,
  CreditCard,
  Clock,
  Shield,
} from 'lucide-react';
import Link from 'next/link';

interface PaymentData {
  clientSecret: string;
  paymentIntentId: string;
  amount: number;
  currency: string;
  order: {
    id: string;
    orderNumber: string;
    quantity: number;
    totalAmount: number;
  };
}

interface OrderFormData {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  quantity: number;
}

function PaymentPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [paymentData, setPaymentData] = useState<PaymentData | null>(null);
  const [orderData, setOrderData] = useState<OrderFormData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRemaining, setTimeRemaining] = useState(15 * 60); // 15 minutes in seconds

  // Define createPaymentIntent function first
  const createPaymentIntent = useCallback(
    async (orderFormData: OrderFormData) => {
      try {
        setIsLoading(true);
        setError(null);

        console.log('Creating payment intent for order:', orderFormData);

        // Check if we already have payment data for this session
        const sessionKey = `fieldDay2025_${JSON.stringify(orderFormData)}`;
        const existingPaymentData = sessionStorage.getItem(sessionKey);

        if (existingPaymentData) {
          try {
            const parsedData = JSON.parse(existingPaymentData);
            if (parsedData.clientSecret && parsedData.order?.orderNumber) {
              console.log('🔄 Reusing existing payment session');
              setPaymentData(parsedData);
              setIsLoading(false);
              return;
            }
          } catch {
            // Invalid data, continue with new request
            sessionStorage.removeItem(sessionKey);
          }
        }

        const response = await fetch(
          '/api/field-day-2025/create-payment-intent',
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(orderFormData),
          }
        );

        const result = await response.json();

        if (!response.ok) {
          throw new Error(
            result.error ||
              `HTTP ${response.status}: Failed to create payment intent`
          );
        }

        if (!result.success || !result.data) {
          throw new Error(result.error || 'Failed to create payment intent');
        }

        // Store in session storage to prevent duplicate requests
        sessionStorage.setItem(sessionKey, JSON.stringify(result.data));

        setPaymentData(result.data);

        console.log(
          'Payment intent created successfully:',
          result.data.paymentIntentId
        );
      } catch (error) {
        console.error('Failed to create payment intent:', error);
        setError(
          error instanceof Error
            ? error.message
            : 'Failed to create payment intent'
        );
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  // Get order data from URL params or localStorage
  useEffect(() => {
    const initializePayment = async () => {
      const orderDataParam = searchParams.get('orderData');

      if (orderDataParam) {
        try {
          const parsedOrderData = JSON.parse(
            decodeURIComponent(orderDataParam)
          );
          setOrderData(parsedOrderData);
          await createPaymentIntent(parsedOrderData);
        } catch (error) {
          console.error('Failed to parse order data from URL:', error);
          setError('Invalid order data. Please start over.');
          setIsLoading(false);
        }
      } else {
        // Try to get from localStorage as fallback
        const storedOrderData = localStorage.getItem('fieldDay2025OrderData');
        if (storedOrderData) {
          try {
            const parsedOrderData = JSON.parse(storedOrderData);
            setOrderData(parsedOrderData);
            await createPaymentIntent(parsedOrderData);
          } catch (error) {
            console.error('Failed to parse stored order data:', error);
            setError('Order session expired. Please start over.');
            setIsLoading(false);
          }
        } else {
          setError('No order data found. Please start over.');
          setIsLoading(false);
        }
      }
    };

    initializePayment();
  }, [searchParams, createPaymentIntent]);

  // Payment session timer
  useEffect(() => {
    if (timeRemaining <= 0) {
      setError('Payment session expired. Please start over.');
      return;
    }

    const timer = setInterval(() => {
      setTimeRemaining((prev) => prev - 1);
    }, 1000);

    return () => clearInterval(timer);
  }, [timeRemaining]);

  const handlePaymentSuccess = (orderNumber: string) => {
    console.log('Payment successful for order:', orderNumber);
    // Redirect will be handled by the PaymentForm component
  };

  const handlePaymentError = (error: string) => {
    console.error('Payment error:', error);
    setError(error);
  };

  // const formatTime = (seconds: number) => {
  //   const minutes = Math.floor(seconds / 60);
  //   const remainingSeconds = seconds % 60;
  //   return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  // };

  const handleGoBack = () => {
    router.push('/field-day-2025');
  };

  if (isLoading) {
    return (
      <div className='min-h-screen bg-gray-50 py-8'>
        <div className='max-w-2xl mx-auto px-4'>
          <div className='space-y-6'>
            <Skeleton className='h-8 w-64' />
            <Card>
              <CardHeader>
                <Skeleton className='h-6 w-48' />
              </CardHeader>
              <CardContent>
                <div className='space-y-4'>
                  <Skeleton className='h-4 w-full' />
                  <Skeleton className='h-4 w-3/4' />
                  <Skeleton className='h-4 w-1/2' />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <Skeleton className='h-6 w-48' />
              </CardHeader>
              <CardContent>
                <Skeleton className='h-32 w-full' />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className='min-h-screen bg-gray-50 py-8'>
        <div className='max-w-2xl mx-auto px-4'>
          <div className='space-y-6'>
            <div className='text-center'>
              <h1 className='text-2xl font-bold text-gray-900 mb-2'>
                Payment Error
              </h1>
              <p className='text-gray-600'>
                There was an issue processing your payment
              </p>
            </div>

            <Alert variant='destructive'>
              <AlertCircle className='h-4 w-4' />
              <AlertDescription>{error}</AlertDescription>
            </Alert>

            <div className='flex flex-col sm:flex-row gap-3'>
              <Button
                variant='outline'
                onClick={handleGoBack}
                className='flex-1'
              >
                <ArrowLeft className='mr-2 h-4 w-4' />
                Start Over
              </Button>

              {orderData && (
                <Button
                  onClick={() => createPaymentIntent(orderData)}
                  className='flex-1'
                >
                  Try Again
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!paymentData || !orderData) {
    return (
      <div className='min-h-screen bg-gray-50 py-8'>
        <div className='max-w-2xl mx-auto px-4'>
          <Alert variant='destructive'>
            <AlertCircle className='h-4 w-4' />
            <AlertDescription>
              Payment session not found. Please start over.
            </AlertDescription>
          </Alert>

          <div className='mt-6'>
            <Button onClick={handleGoBack} variant='outline'>
              <ArrowLeft className='mr-2 h-4 w-4' />
              Back to Order Form
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='min-h-screen bg-gray-50 py-8'>
      <div className='max-w-2xl mx-auto px-4'>
        <div className='space-y-6'>
          {/* Page Header */}
          <div className='text-center'>
            <h1 className='text-2xl md:text-3xl font-bold text-gray-900 mb-2'>
              Complete Your Payment
            </h1>
            <p className='text-gray-600'>
              Secure your Field Day 2025 produce box order
            </p>
          </div>

          {/* Payment Timer */}
          {/* <Card className='border-orange-200 bg-orange-50'>
            <CardContent className='p-4'>
              <div className='flex items-center gap-3'>
                <Clock className='h-5 w-5 text-orange-600' />
                <div>
                  <p className='font-medium text-orange-800'>
                    Complete payment within: {formatTime(timeRemaining)}
                  </p>
                  <p className='text-sm text-orange-700'>
                    Your order will be held for 15 minutes
                  </p>
                </div>
              </div>
            </CardContent>
          </Card> */}

          {/* Payment Form */}
          <PaymentForm
            clientSecret={paymentData.clientSecret}
            orderData={{
              orderNumber: paymentData.order.orderNumber,
              quantity: paymentData.order.quantity,
              totalAmount: paymentData.order.totalAmount,
              customerName: `${orderData.firstName} ${orderData.lastName}`,
              customerEmail: orderData.email,
            }}
            onSuccess={handlePaymentSuccess}
            onError={handlePaymentError}
          />

          {/* Trust Indicators */}
          <div className='grid grid-cols-1 md:grid-cols-3 gap-4 text-center'>
            <div className='flex flex-col items-center gap-2'>
              <Shield className='h-8 w-8 text-green-600' />
              <div>
                <h4 className='font-medium text-gray-900'>Secure Payment</h4>
                <p className='text-sm text-gray-600'>256-bit SSL encryption</p>
              </div>
            </div>

            <div className='flex flex-col items-center gap-2'>
              <CreditCard className='h-8 w-8 text-green-600' />
              <div>
                <h4 className='font-medium text-gray-900'>
                  Trusted Processing
                </h4>
                <p className='text-sm text-gray-600'>Powered by Stripe</p>
              </div>
            </div>

            <div className='flex flex-col items-center gap-2'>
              <Clock className='h-8 w-8 text-green-600' />
              <div>
                <h4 className='font-medium text-gray-900'>
                  Instant Confirmation
                </h4>
                <p className='text-sm text-gray-600'>Email receipt sent</p>
              </div>
            </div>
          </div>

          {/* Help Link */}
          <div className='text-center'>
            <p className='text-sm text-gray-600'>
              Need help?{' '}
              <Link
                href='/contact'
                className='text-green-600 hover:text-green-700 underline'
              >
                Contact our support team
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function FieldDay2025PaymentPage() {
  return (
    <Suspense
      fallback={
        <div className='min-h-screen bg-gray-50 py-8'>
          <div className='max-w-2xl mx-auto px-4'>
            <div className='space-y-6'>
              <Skeleton className='h-8 w-64' />
              <Card>
                <CardHeader>
                  <Skeleton className='h-6 w-48' />
                </CardHeader>
                <CardContent>
                  <div className='space-y-4'>
                    <Skeleton className='h-4 w-full' />
                    <Skeleton className='h-4 w-3/4' />
                    <Skeleton className='h-4 w-1/2' />
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      }
    >
      <PaymentPageContent />
    </Suspense>
  );
}
