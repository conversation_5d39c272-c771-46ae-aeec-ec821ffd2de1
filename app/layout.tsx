import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { cn } from '@/lib/utils';
import { Toaster } from '@/components/ui/sonner';
import AuthProvider from '@/components/auth/AuthProvider';
import { authServer } from '@/lib/utils/auth-server';

const FONT_INTER = Inter({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  metadataBase: new URL('https://www.asedafoods.org/'),
  title: 'Aseda Foods - Farm Fresh Subscriptions',
  description:
    'Get the freshest, locally-sourced organic produce delivered straight to your door. Support local farmers while enjoying the best seasonal vegetables and fruits.',
};

// Force dynamic rendering for this layout
export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const { data: initialUser } = await authServer.getCurrentUser();

  return (
    <html lang='en'>
      <body className={cn(FONT_INTER.className, '')}>
        <AuthProvider initialUser={initialUser}>
          {children}
          <Toaster />
        </AuthProvider>
      </body>
    </html>
  );
}
