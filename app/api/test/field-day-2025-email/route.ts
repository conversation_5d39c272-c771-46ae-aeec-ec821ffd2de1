import { NextRequest, NextResponse } from 'next/server';
import { EmailService } from '@/lib/services/email';
import { fieldDay2025OrderService } from '@/lib/services/field-day-2025-orders';
import {
  FIELD_DAY_2025_EVENT,
  PICKUP_LOCATIONS,
} from '@/lib/field-day-2025-config';

// Test endpoint to verify Field Day 2025 email system
export async function POST(request: NextRequest) {
  try {
    // Only allow in development
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { error: 'Test endpoint not available in production' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { orderId, type = 'confirmation' } = body;

    if (!orderId) {
      return NextResponse.json(
        { error: 'Order ID is required' },
        { status: 400 }
      );
    }

    console.log(`🧪 Testing Field Day 2025 ${type} email for order:`, orderId);

    // Get real order data from database
    const orderResult = await fieldDay2025OrderService.getOrderById(orderId);

    if (orderResult.error || !orderResult.data) {
      return NextResponse.json(
        { error: `Order not found: ${orderResult.error || 'Unknown error'}` },
        { status: 404 }
      );
    }

    const order = orderResult.data;

    // Prepare real order data for emails
    const customerName = `${order.first_name} ${order.last_name}`;
    const orderData = {
      orderNumber: order.order_number,
      orderDate: new Date(order.created_at || new Date()),
      quantity: order.quantity,
      unitPrice: order.unit_price_cents,
      totalAmount: order.total_amount_cents,
      taxAmount: order.tax_amount_cents || 0,
      productName: order.product_name,
    };

    const pickupData = {
      eventDate: FIELD_DAY_2025_EVENT.date,
      location: PICKUP_LOCATIONS[0].name,
      address: PICKUP_LOCATIONS[0].address,
      contactInfo: PICKUP_LOCATIONS[0].contactInfo,
    };

    const adminOrderData = {
      orderNumber: order.order_number,
      orderDate: new Date(order.created_at || new Date()),
      customerName,
      customerEmail: order.email,
      customerPhone: order.phone_number,
      quantity: order.quantity,
      totalAmount: order.total_amount_cents,
      productName: order.product_name,
      paymentStatus: order.payment_status,
    };

    let result;

    switch (type) {
      case 'confirmation':
        result = await EmailService.sendFieldDay2025OrderConfirmation(
          order.email,
          customerName,
          orderData,
          pickupData
        );
        break;

      case 'pickup-ready':
        result = await EmailService.sendFieldDay2025PickupReady(
          order.email,
          customerName,
          {
            orderNumber: orderData.orderNumber,
            quantity: orderData.quantity,
            productName: orderData.productName,
          },
          pickupData
        );
        break;

      case 'admin-notification':
        const adminEmail = process.env.ADMIN_EMAIL || order.email;
        result = await EmailService.sendFieldDay2025AdminNewOrder(
          adminEmail,
          adminOrderData
        );
        break;

      default:
        return NextResponse.json(
          {
            error:
              'Invalid email type. Use: confirmation, pickup-ready, or admin-notification',
          },
          { status: 400 }
        );
    }

    if (result.success) {
      console.log(`✅ Test ${type} email sent successfully:`, result.data?.id);
      const recipient =
        type === 'admin-notification'
          ? process.env.ADMIN_EMAIL || order.email
          : order.email;

      return NextResponse.json({
        success: true,
        message: `Test ${type} email sent successfully`,
        emailId: result.data?.id,
        recipient,
        orderNumber: order.order_number,
        customerName,
      });
    } else {
      console.error(`❌ Failed to send test ${type} email:`, result.error);
      return NextResponse.json(
        {
          success: false,
          error: `Failed to send test ${type} email`,
          details: result.error,
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('💥 Error in test email endpoint:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// GET endpoint to show available test options
export async function GET() {
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json(
      { error: 'Test endpoint not available in production' },
      { status: 403 }
    );
  }

  return NextResponse.json({
    message: 'Field Day 2025 Email Test Endpoint',
    usage: {
      method: 'POST',
      body: {
        orderId: 'uuid-of-existing-order',
        type: 'confirmation | pickup-ready | admin-notification',
      },
      note: 'Uses real order data from the database instead of mock data',
    },
    availableTypes: [
      {
        type: 'confirmation',
        description: 'Order confirmation email sent after successful payment',
        recipient: 'Customer email from order',
      },
      {
        type: 'pickup-ready',
        description: 'Pickup ready notification sent when order is ready',
        recipient: 'Customer email from order',
      },
      {
        type: 'admin-notification',
        description: 'Admin notification sent when new order is placed',
        recipient:
          'ADMIN_EMAIL environment variable or customer email as fallback',
      },
    ],
    example: {
      orderId: '12345678-1234-1234-1234-123456789abc',
      type: 'confirmation',
    },
  });
}
