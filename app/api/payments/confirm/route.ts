import { getNextDeliveryDate } from '@/lib/constants/subscription';
import { EmailService } from '@/lib/services/email';
import { paymentDatabaseService } from '@/lib/services/payment-database';
import { stripePaymentService } from '@/lib/services/stripe-payment';
import { SubscriptionServerService } from '@/lib/services/subscription-server';
import { createClient } from '@/lib/supabase/server';
import { PaymentConfirmationData } from '@/lib/types/payment';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Payment confirmation started');

    // Get the authenticated user using centralized auth system
    const { authServer } = await import('@/lib/utils/auth-server');
    const authResult = await authServer.getCurrentUser();

    if (authResult.error || !authResult.data) {
      console.error('❌ Authentication failed:', authResult.error);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = authResult.data;

    // Still need supabase client for database operations
    const supabase = await createClient();

    console.log('✅ User authenticated:', user.id);

    // Parse request body
    const body = await request.json();
    const { paymentIntentId, subscriptionData, subscriberId } =
      body as PaymentConfirmationData;

    console.log('📝 Request data:', {
      paymentIntentId,
      subscriberId,
      subscriptionData: {
        boxSize: subscriptionData?.boxSize,
        frequency: subscriptionData?.frequency,
        paymentPlan: subscriptionData?.paymentPlan,
        deliveryType: subscriptionData?.deliveryType,
        pickupLocation: subscriptionData?.pickupLocation,
      },
    });

    // Validate required fields
    if (!paymentIntentId || !subscriptionData || !subscriberId) {
      console.error('❌ Missing required fields');
      return NextResponse.json(
        {
          error:
            'Missing required fields: paymentIntentId, subscriptionData, and subscriberId',
        },
        { status: 400 }
      );
    }

    // Verify that the user owns the subscriber record
    console.log('🔍 Verifying subscriber ownership...');
    const { data: subscriber, error: subscriberError } = await supabase
      .from('subscribers')
      .select('id, user_id')
      .eq('id', subscriberId)
      .eq('user_id', user.id)
      .single();

    if (subscriberError || !subscriber) {
      console.error('❌ Subscriber verification failed:', subscriberError);
      return NextResponse.json(
        { error: 'Subscriber not found or access denied' },
        { status: 403 }
      );
    }

    console.log('✅ Subscriber verified:', subscriber.id);

    // Get payment intent from Stripe to verify status
    console.log('🔍 Fetching payment intent from Stripe...');
    const { data: paymentIntent, error: stripeError } =
      await stripePaymentService.getPaymentIntent(paymentIntentId);

    if (stripeError || !paymentIntent) {
      console.error('❌ Stripe payment intent fetch failed:', stripeError);
      return NextResponse.json(
        { error: 'Payment intent not found or invalid' },
        { status: 400 }
      );
    }

    console.log('✅ Payment intent fetched:', {
      id: paymentIntent.id,
      status: paymentIntent.status,
      amount: paymentIntent.amount,
    });

    // Check if payment was successful
    if (paymentIntent.status !== 'succeeded') {
      console.error('❌ Payment not succeeded:', paymentIntent.status);
      return NextResponse.json(
        {
          error: 'Payment not completed',
          paymentStatus: paymentIntent.status,
        },
        { status: 400 }
      );
    }

    // Create subscription
    console.log('🔄 Creating subscription...');
    const subscriptionService = new SubscriptionServerService();
    const { data: subscription, error: subscriptionError } =
      await subscriptionService.createSubscription(
        subscriberId,
        subscriptionData
      );

    if (subscriptionError || !subscription) {
      console.error('❌ Subscription creation failed:', subscriptionError);
      return NextResponse.json(
        { error: 'Failed to create subscription', details: subscriptionError },
        { status: 500 }
      );
    }

    console.log('✅ Subscription created:', subscription.id);

    // Create payment record in database
    console.log('🔄 Creating payment record...');
    const paymentStatus = stripePaymentService.mapStripeStatusToPaymentStatus(
      paymentIntent.status
    );
    const { data: payment, error: paymentDbError } =
      await paymentDatabaseService.createPayment(
        subscription.id,
        paymentIntentId,
        paymentIntent.amount,
        paymentStatus
      );

    if (paymentDbError) {
      console.error('❌ Payment record creation failed:', paymentDbError);
      // Note: We don't fail the request here since the subscription was created successfully
      // The payment record can be created later via webhook
    } else {
      console.log('✅ Payment record created:', payment?.id);
    }

    // Send confirmation emails
    try {
      // Get subscriber details for emails
      const { data: subscriberDetails } = await supabase
        .from('subscribers')
        .select('name, email, phone, address')
        .eq('id', subscriberId)
        .single();

      if (subscriberDetails) {
        const subscriberName = subscriberDetails.name || 'there';
        const nextDeliveryDate = getNextDeliveryDate(
          subscriptionData.frequency
        );

        // Send subscription confirmation email to customer
        await EmailService.sendSubscriptionConfirmation(
          subscriberDetails.email,
          subscriberName,
          {
            boxSize: subscription.box_size || 'Unknown',
            frequency: subscription.frequency || 'Unknown',
            paymentPlan: subscription.payment_plan || 'Unknown',
            pickupLocation: subscription.pickup_location || 'Unknown',
            nextDeliveryDate,
            totalPrice: paymentIntent.amount / 100, // Convert from cents
            deliveriesRemaining: subscription.deliveries_remaining || 0,
            specialInstructions: subscriptionData.specialInstructions,
          }
        );

        // Send admin notification
        const adminEmail = process.env.ADMIN_EMAIL;
        if (adminEmail) {
          await EmailService.sendNewSubscriptionAlert(
            adminEmail,
            {
              id: subscriberId,
              name: subscriberName,
              email: subscriberDetails.email,
              phone: subscriberDetails.phone || undefined,
              address: subscriberDetails.address || undefined,
            },
            {
              id: subscription.id,
              boxSize: subscription.box_size || 'Unknown',
              frequency: subscription.frequency || 'Unknown',
              paymentPlan: subscription.payment_plan || 'Unknown',
              pickupLocation: subscription.pickup_location || 'Unknown',
              nextDeliveryDate,
              totalPrice: paymentIntent.amount / 100,
              deliveriesRemaining: subscription.deliveries_remaining || 0,
              specialInstructions: subscriptionData.specialInstructions,
              createdAt: new Date(subscription.created_at || new Date()),
            }
          );
        }

        console.log('Confirmation emails sent successfully');
      }
    } catch (emailError) {
      // Don't fail the request if email sending fails
      console.error('Error sending confirmation emails:', emailError);
    }

    // Return success response
    console.log('🎉 Payment confirmation completed successfully');
    return NextResponse.json({
      success: true,
      data: {
        subscription: {
          id: subscription.id,
          status: subscription.status,
          boxSize: subscription.box_size,
          frequency: subscription.frequency,
          nextDeliveryDate: subscription.next_delivery_date,
          deliveriesRemaining: subscription.deliveries_remaining,
        },
        payment: {
          id: payment?.id,
          status: paymentStatus,
          amount: paymentIntent.amount,
          currency: paymentIntent.currency,
        },
      },
    });
  } catch (error) {
    console.error('💥 Error in confirm payment API:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
