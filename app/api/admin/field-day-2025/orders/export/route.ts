import { formatCurrency } from '@/lib/field-day-2025-config';
import { fieldDay2025OrderService } from '@/lib/services/field-day-2025-orders';
import { createClient } from '@/lib/supabase/server';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Validation schema for export parameters
const exportParamsSchema = z.object({
  format: z.enum(['csv', 'json']).default('csv'),
  status: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  includePersonalData: z.coerce.boolean().default(true),
});

// Helper function to check admin authentication using centralized auth system
async function checkAdminAuth() {
  try {
    // Use centralized auth system
    const { authServer } = await import('@/lib/utils/auth-server');
    const result = await authServer.getCurrentUser();

    if (result.error || !result.data) {
      return { authorized: false, error: 'Unauthorized - Please sign in' };
    }

    if (result.data.role !== 'admin') {
      return { authorized: false, error: 'Forbidden - Admin access required' };
    }

    // Still need supabase client for database operations
    const supabase = await createClient();
    return { authorized: true, user: result.data, supabase };
  } catch (error) {
    console.error('Auth check error:', error);
    return { authorized: false, error: 'Authentication check failed' };
  }
}

// Helper function to convert orders to CSV
function convertToCSV(orders: any[], includePersonalData: boolean) {
  if (orders.length === 0) {
    return 'No orders found for the specified criteria';
  }

  // Define CSV headers
  const headers = [
    'Order Number',
    'Order Date',
    'Product Name',
    'Quantity',
    'Unit Price',
    'Total Amount',
    'Order Status',
    'Payment Status',
    'Pickup Status',
    'Payment Method',
    'Payment Date',
    'Admin Notes',
    'Last Updated',
  ];

  if (includePersonalData) {
    headers.splice(3, 0, 'Customer Name', 'Email', 'Phone');
  }

  // Convert orders to CSV rows
  const rows = orders.map((order) => {
    const baseRow = [
      order.order_number,
      new Date(order.created_at).toLocaleDateString(),
      order.product_name,
      order.quantity.toString(),
      formatCurrency(order.unit_price_cents),
      formatCurrency(order.total_amount_cents),
      order.order_status,
      order.payment_status,
      order.pickup_status,
      order.payment_method || 'N/A',
      order.payment_timestamp
        ? new Date(order.payment_timestamp).toLocaleDateString()
        : 'N/A',
      order.admin_notes || '',
      new Date(order.updated_at).toLocaleDateString(),
    ];

    if (includePersonalData) {
      baseRow.splice(
        3,
        0,
        `${order.first_name} ${order.last_name}`,
        order.email,
        order.phone_number
      );
    }

    return baseRow;
  });

  // Combine headers and rows
  const csvContent = [headers, ...rows]
    .map((row) => row.map((field) => `"${field}"`).join(','))
    .join('\n');

  return csvContent;
}

export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const authResult = await checkAdminAuth();
    if (!authResult.authorized) {
      return NextResponse.json(
        {
          success: false,
          error: authResult.error,
          code: 'UNAUTHORIZED',
        },
        { status: authResult.error?.includes('Forbidden') ? 403 : 401 }
      );
    }

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url);
    const exportParams = {
      format: searchParams.get('format'),
      status: searchParams.get('status'),
      startDate: searchParams.get('startDate'),
      endDate: searchParams.get('endDate'),
      includePersonalData: searchParams.get('includePersonalData'),
    };

    const validatedParams = exportParamsSchema.parse(exportParams);

    console.log('📊 Admin exporting Field Day 2025 orders:', {
      ...validatedParams,
      adminUserId: authResult.user?.id,
    });

    // Fetch all orders matching the criteria (no pagination for export)
    const result = await fieldDay2025OrderService.getOrders({
      page: 1,
      limit: 10000, // Large limit to get all orders
      status: validatedParams.status,
      startDate: validatedParams.startDate,
      endDate: validatedParams.endDate,
    });

    if (result.error) {
      console.error('❌ Failed to fetch orders for export:', result.error);
      return NextResponse.json(
        {
          success: false,
          error: result.error,
          code: 'FETCH_FAILED',
        },
        { status: 500 }
      );
    }

    if (!result.data) {
      return NextResponse.json(
        {
          success: false,
          error: 'No data returned',
          code: 'NO_DATA',
        },
        { status: 500 }
      );
    }

    const orders = result.data.orders;

    console.log('✅ Successfully fetched orders for export:', {
      count: orders.length,
      format: validatedParams.format,
      includePersonalData: validatedParams.includePersonalData,
    });

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    const filename = `field-day-2025-orders-${timestamp}.${validatedParams.format}`;

    if (validatedParams.format === 'csv') {
      // Generate CSV content
      const csvContent = convertToCSV(
        orders,
        validatedParams.includePersonalData
      );

      return new NextResponse(csvContent, {
        status: 200,
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="${filename}"`,
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          Pragma: 'no-cache',
          Expires: '0',
        },
      });
    } else {
      // Generate JSON content
      const exportData = {
        exportInfo: {
          timestamp: new Date().toISOString(),
          exportedBy: authResult.user?.id,
          criteria: validatedParams,
          totalOrders: orders.length,
        },
        orders: validatedParams.includePersonalData
          ? orders
          : orders.map((order) => {
              // Remove personal data for privacy
              const {
                // phone_number,
                // first_name,
                // last_name,
                // email,
                ...sanitizedOrder
              } = order;
              return {
                ...sanitizedOrder,
                customer_info: 'Personal data excluded',
              };
            }),
      };

      return new NextResponse(JSON.stringify(exportData, null, 2), {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Content-Disposition': `attachment; filename="${filename}"`,
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          Pragma: 'no-cache',
          Expires: '0',
        },
      });
    }
  } catch (error) {
    console.error('💥 Error in admin orders export API:', error);

    // Handle validation errors
    if (error instanceof z.ZodError) {
      const firstError = error.errors[0];
      return NextResponse.json(
        {
          success: false,
          error: firstError?.message || 'Invalid export parameters',
          code: 'VALIDATION_ERROR',
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to export orders',
        code: 'INTERNAL_ERROR',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
