import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { fieldDay2025OrderService } from '@/lib/services/field-day-2025-orders';

// Helper function to check admin authentication
async function checkAdminAuth() {
  const supabase = await createClient();
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { authorized: false, error: 'Unauthorized - Please sign in' };
  }

  // Get user role from subscribers table
  const { data: subscriber, error: roleError } = await supabase
    .from('subscribers')
    .select('role')
    .eq('user_id', user.id)
    .single();

  if (roleError || !subscriber || subscriber.role !== 'admin') {
    return { authorized: false, error: 'Forbidden - Admin access required' };
  }

  return { authorized: true, user, supabase };
}

// POST - Resend confirmation email
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check admin authentication
    const authResult = await checkAdminAuth();
    if (!authResult.authorized) {
      return NextResponse.json(
        {
          success: false,
          error: authResult.error,
          code: 'UNAUTHORIZED',
        },
        { status: authResult.error?.includes('Forbidden') ? 403 : 401 }
      );
    }

    const { id: orderId } = await params;

    if (!orderId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Order ID is required',
          code: 'MISSING_ORDER_ID',
        },
        { status: 400 }
      );
    }

    console.log(
      '📧 Admin resending confirmation email for Field Day 2025 order:',
      {
        orderId,
        adminUserId: authResult.user?.id,
      }
    );

    // First, get the order details to validate it exists and get customer email
    const orderResult = await fieldDay2025OrderService.getOrderById(orderId);

    if (orderResult.error) {
      console.error(
        '❌ Failed to fetch order for email resend:',
        orderResult.error
      );
      return NextResponse.json(
        {
          success: false,
          error: orderResult.error,
          code: 'FETCH_FAILED',
        },
        { status: 500 }
      );
    }

    if (!orderResult.data) {
      return NextResponse.json(
        {
          success: false,
          error: 'Order not found',
          code: 'ORDER_NOT_FOUND',
        },
        { status: 404 }
      );
    }

    const order = orderResult.data;

    // Check if order is in a valid state for email resend
    if (order.payment_status !== 'succeeded') {
      return NextResponse.json(
        {
          success: false,
          error: 'Cannot resend confirmation email for unpaid orders',
          code: 'INVALID_ORDER_STATE',
        },
        { status: 400 }
      );
    }

    // Resend the confirmation email
    const emailResult =
      await fieldDay2025OrderService.resendConfirmationEmail(orderId);

    if (emailResult.error) {
      console.error(
        '❌ Failed to resend confirmation email:',
        emailResult.error
      );
      return NextResponse.json(
        {
          success: false,
          error: emailResult.error,
          code: 'EMAIL_SEND_FAILED',
        },
        { status: 500 }
      );
    }

    console.log('✅ Successfully resent confirmation email:', {
      orderId: order.id,
      orderNumber: order.order_number,
      customerEmail: order.email,
      adminUserId: authResult.user?.id,
    });

    return NextResponse.json({
      success: true,
      data: {
        orderId: order.id,
        orderNumber: order.order_number,
        customerEmail: order.email,
        emailSent: true,
      },
      message: `Confirmation email resent to ${order.email}`,
      meta: {
        timestamp: new Date().toISOString(),
        resentBy: authResult.user?.id,
      },
    });
  } catch (error) {
    console.error('💥 Error in resend email API:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to resend confirmation email',
        code: 'INTERNAL_ERROR',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// GET - Check email status/history (optional future enhancement)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check admin authentication
    const authResult = await checkAdminAuth();
    if (!authResult.authorized) {
      return NextResponse.json(
        {
          success: false,
          error: authResult.error,
          code: 'UNAUTHORIZED',
        },
        { status: authResult.error?.includes('Forbidden') ? 403 : 401 }
      );
    }

    const { id: orderId } = await params;

    if (!orderId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Order ID is required',
          code: 'MISSING_ORDER_ID',
        },
        { status: 400 }
      );
    }

    // Get order details
    const orderResult = await fieldDay2025OrderService.getOrderById(orderId);

    if (orderResult.error || !orderResult.data) {
      return NextResponse.json(
        {
          success: false,
          error: 'Order not found',
          code: 'ORDER_NOT_FOUND',
        },
        { status: 404 }
      );
    }

    const order = orderResult.data;

    // For now, return basic email status info
    // This could be expanded to include email delivery history, open rates, etc.
    return NextResponse.json({
      success: true,
      data: {
        orderId: order.id,
        orderNumber: order.order_number,
        customerEmail: order.email,
        paymentStatus: order.payment_status,
        canResendEmail: order.payment_status === 'succeeded',
        emailHistory: {
          // This would be populated from an email tracking system
          message: 'Email history tracking will be implemented in Phase 5',
        },
      },
      meta: {
        timestamp: new Date().toISOString(),
        checkedBy: authResult.user?.id,
      },
    });
  } catch (error) {
    console.error('💥 Error in email status API:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to check email status',
        code: 'INTERNAL_ERROR',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
