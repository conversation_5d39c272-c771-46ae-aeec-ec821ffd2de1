import { adminOrderUpdateSchema } from '@/lib/field-day-2025-config';
import { fieldDay2025OrderService } from '@/lib/services/field-day-2025-orders';
import { createClient } from '@/lib/supabase/server';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Helper function to check admin authentication using centralized auth system
async function checkAdminAuth() {
  try {
    // Use centralized auth system
    const { authServer } = await import('@/lib/utils/auth-server');
    const result = await authServer.getCurrentUser();

    if (result.error || !result.data) {
      return { authorized: false, error: 'Unauthorized - Please sign in' };
    }

    if (result.data.role !== 'admin') {
      return { authorized: false, error: 'Forbidden - Admin access required' };
    }

    // Still need supabase client for database operations
    const supabase = await createClient();
    return { authorized: true, user: result.data, supabase };
  } catch (error) {
    console.error('Auth check error:', error);
    return { authorized: false, error: 'Authentication check failed' };
  }
}

// GET - Fetch single order details
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check admin authentication
    const authResult = await checkAdminAuth();
    if (!authResult.authorized) {
      return NextResponse.json(
        {
          success: false,
          error: authResult.error,
          code: 'UNAUTHORIZED',
        },
        { status: authResult.error?.includes('Forbidden') ? 403 : 401 }
      );
    }

    const { id: orderId } = await params;

    if (!orderId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Order ID is required',
          code: 'MISSING_ORDER_ID',
        },
        { status: 400 }
      );
    }

    console.log('📋 Admin fetching Field Day 2025 order details:', {
      orderId,
      adminUserId: authResult.user?.id,
    });

    // Fetch order details
    const result = await fieldDay2025OrderService.getOrderById(orderId);

    if (result.error) {
      console.error('❌ Failed to fetch order details:', result.error);
      return NextResponse.json(
        {
          success: false,
          error: result.error,
          code: 'FETCH_FAILED',
        },
        { status: 500 }
      );
    }

    if (!result.data) {
      return NextResponse.json(
        {
          success: false,
          error: 'Order not found',
          code: 'ORDER_NOT_FOUND',
        },
        { status: 404 }
      );
    }

    console.log('✅ Successfully fetched order details:', {
      orderId: result.data.id,
      orderNumber: result.data.order_number,
      status: result.data.order_status,
    });

    return NextResponse.json({
      success: true,
      data: result.data,
      meta: {
        timestamp: new Date().toISOString(),
        fetchedBy: authResult.user?.id,
      },
    });
  } catch (error) {
    console.error('💥 Error in admin order details API:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch order details',
        code: 'INTERNAL_ERROR',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// PATCH - Update order details
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check admin authentication
    const authResult = await checkAdminAuth();
    if (!authResult.authorized) {
      return NextResponse.json(
        {
          success: false,
          error: authResult.error,
          code: 'UNAUTHORIZED',
        },
        { status: authResult.error?.includes('Forbidden') ? 403 : 401 }
      );
    }

    const { id: orderId } = await params;

    if (!orderId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Order ID is required',
          code: 'MISSING_ORDER_ID',
        },
        { status: 400 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = adminOrderUpdateSchema.parse(body);

    console.log('🔄 Admin updating Field Day 2025 order:', {
      orderId,
      updates: validatedData,
      adminUserId: authResult.user?.id,
    });

    // Update the order
    const result = await fieldDay2025OrderService.updateOrder(
      orderId,
      validatedData
    );

    if (result.error) {
      console.error('❌ Failed to update order:', result.error);
      return NextResponse.json(
        {
          success: false,
          error: result.error,
          code: 'UPDATE_FAILED',
        },
        { status: 500 }
      );
    }

    if (!result.data) {
      return NextResponse.json(
        {
          success: false,
          error: 'Order not found or update failed',
          code: 'ORDER_NOT_FOUND',
        },
        { status: 404 }
      );
    }

    console.log('✅ Successfully updated order:', {
      orderId: result.data.id,
      orderNumber: result.data.order_number,
      newStatus: result.data.order_status,
      newPickupStatus: result.data.pickup_status,
    });

    return NextResponse.json({
      success: true,
      data: result.data,
      message: 'Order updated successfully',
      meta: {
        timestamp: new Date().toISOString(),
        updatedBy: authResult.user?.id,
        changes: validatedData,
      },
    });
  } catch (error) {
    console.error('💥 Error in admin order update API:', error);

    // Handle validation errors
    if (error instanceof z.ZodError) {
      const firstError = error.errors[0];
      return NextResponse.json(
        {
          success: false,
          error: firstError?.message || 'Invalid update data',
          code: 'VALIDATION_ERROR',
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update order',
        code: 'INTERNAL_ERROR',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// DELETE - Cancel/delete order (optional)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check admin authentication
    const authResult = await checkAdminAuth();
    if (!authResult.authorized) {
      return NextResponse.json(
        {
          success: false,
          error: authResult.error,
          code: 'UNAUTHORIZED',
        },
        { status: authResult.error?.includes('Forbidden') ? 403 : 401 }
      );
    }

    const { id: orderId } = await params;

    console.log('🗑️ Admin cancelling Field Day 2025 order:', {
      orderId,
      adminUserId: authResult.user?.id,
    });

    // For now, we'll mark as cancelled rather than actually deleting
    const result = await fieldDay2025OrderService.updateOrder(orderId, {
      orderStatus: 'cancelled',
      adminNotes: `Order cancelled by admin on ${new Date().toISOString()}`,
    });

    if (result.error) {
      console.error('❌ Failed to cancel order:', result.error);
      return NextResponse.json(
        {
          success: false,
          error: result.error,
          code: 'CANCEL_FAILED',
        },
        { status: 500 }
      );
    }

    if (!result.data) {
      return NextResponse.json(
        {
          success: false,
          error: 'Order not found',
          code: 'ORDER_NOT_FOUND',
        },
        { status: 404 }
      );
    }

    console.log('✅ Successfully cancelled order:', {
      orderId: result.data.id,
      orderNumber: result.data.order_number,
    });

    return NextResponse.json({
      success: true,
      data: result.data,
      message: 'Order cancelled successfully',
      meta: {
        timestamp: new Date().toISOString(),
        cancelledBy: authResult.user?.id,
      },
    });
  } catch (error) {
    console.error('💥 Error in admin order cancellation API:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to cancel order',
        code: 'INTERNAL_ERROR',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
