import { fieldDay2025OrderService } from '@/lib/services/field-day-2025-orders';
import { createClient } from '@/lib/supabase/server';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Validation schema for query parameters
const queryParamsSchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  status: z.string().optional(),
  search: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
});

// Helper function to check admin authentication using centralized auth system
async function checkAdminAuth() {
  try {
    // Use centralized auth system
    const { authServer } = await import('@/lib/utils/auth-server');
    const result = await authServer.getCurrentUser();

    if (result.error || !result.data) {
      return { authorized: false, error: 'Unauthorized - Please sign in' };
    }

    if (result.data.role !== 'admin') {
      return { authorized: false, error: 'Forbidden - Admin access required' };
    }

    // Still need supabase client for database operations
    const supabase = await createClient();
    return { authorized: true, user: result.data, supabase };
  } catch (error) {
    console.error('Auth check error:', error);
    return { authorized: false, error: 'Authentication check failed' };
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const authResult = await checkAdminAuth();
    if (!authResult.authorized) {
      return NextResponse.json(
        {
          success: false,
          error: authResult.error,
          code: 'UNAUTHORIZED',
        },
        { status: authResult.error?.includes('Forbidden') ? 403 : 401 }
      );
    }

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = {
      page: searchParams.get('page'),
      limit: searchParams.get('limit'),
      status: searchParams.get('status'),
      search: searchParams.get('search'),
      startDate: searchParams.get('startDate'),
      endDate: searchParams.get('endDate'),
    };

    const validatedParams = queryParamsSchema.parse(queryParams);

    console.log('📋 Admin fetching Field Day 2025 orders:', {
      ...validatedParams,
      adminUserId: authResult.user?.id,
    });

    // Fetch orders using the service
    const result = await fieldDay2025OrderService.getOrders(validatedParams);

    if (result.error) {
      console.error('❌ Failed to fetch orders:', result.error);
      return NextResponse.json(
        {
          success: false,
          error: result.error,
          code: 'FETCH_FAILED',
        },
        { status: 500 }
      );
    }

    if (!result.data) {
      console.error('❌ No data returned from order service');
      return NextResponse.json(
        {
          success: false,
          error: 'No data returned',
          code: 'NO_DATA',
        },
        { status: 500 }
      );
    }

    console.log('✅ Successfully fetched Field Day 2025 orders:', {
      count: result.data.orders.length,
      total: result.data.pagination.total,
      page: result.data.pagination.page,
    });

    // Return the orders data
    return NextResponse.json({
      success: true,
      data: result.data,
      meta: {
        timestamp: new Date().toISOString(),
        requestParams: validatedParams,
      },
    });
  } catch (error) {
    console.error('💥 Error in admin Field Day 2025 orders API:', error);

    // Handle validation errors
    if (error instanceof z.ZodError) {
      const firstError = error.errors[0];
      return NextResponse.json(
        {
          success: false,
          error: firstError?.message || 'Invalid query parameters',
          code: 'VALIDATION_ERROR',
          details: error.errors,
        },
        { status: 400 }
      );
    }

    // Handle other errors
    return NextResponse.json(
      {
        success: false,
        error: 'An unexpected error occurred while fetching orders',
        code: 'INTERNAL_ERROR',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// POST method for bulk operations (future enhancement)
export async function POST(request: NextRequest) {
  try {
    // Check admin authentication
    const authResult = await checkAdminAuth();
    if (!authResult.authorized) {
      return NextResponse.json(
        {
          success: false,
          error: authResult.error,
          code: 'UNAUTHORIZED',
        },
        { status: authResult.error?.includes('Forbidden') ? 403 : 401 }
      );
    }

    const body = await request.json();
    const { action, orderIds } = body;

    // Validate bulk action request
    if (!action || !Array.isArray(orderIds) || orderIds.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error:
            'Invalid bulk action request. Action and orderIds are required.',
          code: 'INVALID_BULK_REQUEST',
        },
        { status: 400 }
      );
    }

    console.log('🔄 Admin bulk action on Field Day 2025 orders:', {
      action,
      orderCount: orderIds.length,
      adminUserId: authResult.user?.id,
    });

    // For now, return a placeholder response
    // This can be expanded to handle bulk status updates, exports, etc.
    return NextResponse.json({
      success: true,
      data: {
        action,
        processedCount: orderIds.length,
        message: `Bulk action '${action}' will be implemented in a future update`,
      },
    });
  } catch (error) {
    console.error('💥 Error in admin bulk action API:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to process bulk action',
        code: 'BULK_ACTION_ERROR',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
