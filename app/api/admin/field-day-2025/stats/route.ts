import { ORDER_STATUSES, PAYMENT_STATUSES } from '@/lib/field-day-2025-config';
import { createClient } from '@/lib/supabase/server';
import { NextResponse } from 'next/server';

// Helper function to check admin authentication using centralized auth system
async function checkAdminAuth() {
  try {
    // Use centralized auth system
    const { authServer } = await import('@/lib/utils/auth-server');
    const result = await authServer.getCurrentUser();

    if (result.error || !result.data) {
      return { authorized: false, error: 'Unauthorized - Please sign in' };
    }

    if (result.data.role !== 'admin') {
      return { authorized: false, error: 'Forbidden - Admin access required' };
    }

    // Still need supabase client for database operations
    const supabase = await createClient();
    return { authorized: true, user: result.data, supabase };
  } catch (error) {
    console.error('Auth check error:', error);
    return { authorized: false, error: 'Authentication check failed' };
  }
}

export async function GET() {
  try {
    // Check admin authentication
    const authResult = await checkAdminAuth();
    if (!authResult.authorized) {
      return NextResponse.json(
        {
          success: false,
          error: authResult.error,
          code: 'UNAUTHORIZED',
        },
        { status: authResult.error?.includes('Forbidden') ? 403 : 401 }
      );
    }

    const supabase = authResult.supabase!;

    console.log('📊 Admin fetching Field Day 2025 statistics');

    // Get all orders for statistics
    const { data: orders, error: ordersError } = await supabase
      .from('field_day_2025_orders')
      .select('*');

    if (ordersError) {
      console.error('❌ Failed to fetch orders for stats:', ordersError);
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to fetch order statistics',
          code: 'FETCH_FAILED',
        },
        { status: 500 }
      );
    }

    // Calculate statistics
    const totalOrders = orders.length;
    const totalRevenue = orders.reduce(
      (sum, order) => sum + order.total_amount_cents,
      0
    );
    const totalQuantity = orders.reduce(
      (sum, order) => sum + order.quantity,
      0
    );
    const averageOrderValue =
      totalOrders > 0 ? Math.round(totalRevenue / totalOrders) : 0;

    // Calculate orders by status
    const ordersByStatus = {
      pending: orders.filter((o) => o.order_status === ORDER_STATUSES.PENDING)
        .length,
      confirmed: orders.filter(
        (o) => o.order_status === ORDER_STATUSES.CONFIRMED
      ).length,
      ready: orders.filter((o) => o.order_status === ORDER_STATUSES.READY)
        .length,
      completed: orders.filter(
        (o) => o.order_status === ORDER_STATUSES.COMPLETED
      ).length,
      cancelled: orders.filter(
        (o) => o.order_status === ORDER_STATUSES.CANCELLED
      ).length,
    };

    // Calculate payments by status
    const paymentsByStatus = {
      pending: orders.filter(
        (o) => o.payment_status === PAYMENT_STATUSES.PENDING
      ).length,
      succeeded: orders.filter(
        (o) => o.payment_status === PAYMENT_STATUSES.SUCCEEDED
      ).length,
      failed: orders.filter((o) => o.payment_status === PAYMENT_STATUSES.FAILED)
        .length,
      cancelled: orders.filter(
        (o) => o.payment_status === PAYMENT_STATUSES.CANCELED
      ).length,
    };

    // Calculate recent orders (last 24 hours)
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const recentOrders = orders.filter(
      (o) => o.created_at && new Date(o.created_at) > yesterday
    ).length;

    // Calculate conversion rate (completed orders / total orders)
    const completedOrders = ordersByStatus.completed;
    const conversionRate =
      totalOrders > 0 ? (completedOrders / totalOrders) * 100 : 0;

    const stats = {
      totalOrders,
      totalRevenue,
      totalQuantity,
      averageOrderValue,
      ordersByStatus,
      paymentsByStatus,
      recentOrders,
      conversionRate,
    };

    console.log('✅ Successfully calculated Field Day 2025 statistics:', {
      totalOrders,
      totalRevenue: totalRevenue / 100, // Convert to dollars for logging
      recentOrders,
      conversionRate: conversionRate.toFixed(1) + '%',
    });

    return NextResponse.json({
      success: true,
      data: stats,
      meta: {
        timestamp: new Date().toISOString(),
        calculatedBy: authResult.user?.id,
      },
    });
  } catch (error) {
    console.error('💥 Error in Field Day 2025 stats API:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to calculate statistics',
        code: 'INTERNAL_ERROR',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// POST method for refreshing/recalculating stats (optional)
export async function POST() {
  try {
    // Check admin authentication
    const authResult = await checkAdminAuth();
    if (!authResult.authorized) {
      return NextResponse.json(
        {
          success: false,
          error: authResult.error,
          code: 'UNAUTHORIZED',
        },
        { status: authResult.error?.includes('Forbidden') ? 403 : 401 }
      );
    }

    console.log('🔄 Admin requesting stats refresh for Field Day 2025');

    // For now, just return the same as GET
    // In the future, this could trigger cache invalidation or background recalculation
    const getResponse = await GET();

    return NextResponse.json({
      success: true,
      message: 'Statistics refreshed successfully',
      data: (await getResponse.json()).data,
    });
  } catch (error) {
    console.error('💥 Error in stats refresh API:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to refresh statistics',
        code: 'INTERNAL_ERROR',
      },
      { status: 500 }
    );
  }
}
