import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET() {
  try {
    const supabase = await createClient();
    
    // Get box contents for the current week and upcoming weeks
    const { data, error } = await supabase
      .from('box_contents')
      .select('*')
      .gte('week_start_date', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]) // Include last week
      .order('week_start_date', { ascending: true })
      .limit(10);

    if (error) {
      console.error('Error fetching box contents:', error);
      return NextResponse.json(
        { error: 'Failed to fetch box contents' },
        { status: 500 }
      );
    }

    return NextResponse.json(data || []);
  } catch (error) {
    console.error('Error in box contents API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
