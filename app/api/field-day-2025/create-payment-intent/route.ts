import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { fieldDay2025PaymentService } from '@/lib/services/field-day-2025-payment';
import { fieldDay2025OrderService } from '@/lib/services/field-day-2025-orders';
import {
  fieldDay2025OrderSchema,
  calculateOrderTotal,
  PAYMENT_STATUSES,
} from '@/lib/field-day-2025-config';

// Rate limiting for payment intent creation
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT = {
  maxAttempts: 3,
  windowMs: 15 * 60 * 1000, // 15 minutes
};

function checkRateLimit(ip: string): { allowed: boolean; retryAfter?: number } {
  const now = Date.now();
  const userLimit = rateLimitMap.get(ip);

  if (!userLimit || now > userLimit.resetTime) {
    rateLimitMap.set(ip, { count: 1, resetTime: now + RATE_LIMIT.windowMs });
    return { allowed: true };
  }

  if (userLimit.count >= RATE_LIMIT.maxAttempts) {
    const retryAfter = Math.ceil((userLimit.resetTime - now) / 1000);
    return { allowed: false, retryAfter };
  }

  userLimit.count++;
  return { allowed: true };
}

// Validation schema for payment intent creation
const createPaymentIntentSchema = fieldDay2025OrderSchema.extend({
  // Additional fields for payment processing
  saveOrder: z.boolean().default(true),
});

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    // Get client IP from x-forwarded-for header or fallback to 'unknown'
    const ip =
      request.headers.get('x-forwarded-for')?.split(',')[0]?.trim() ||
      request.headers.get('x-real-ip') ||
      'unknown';
    const rateLimitResult = checkRateLimit(ip);

    if (!rateLimitResult.allowed) {
      const waitTime = Math.ceil((rateLimitResult.retryAfter || 0) / 60);
      return NextResponse.json(
        {
          success: false,
          error: `Too many payment attempts. Please try again in ${waitTime} minutes.`,
          code: 'RATE_LIMITED',
          retryAfter: rateLimitResult.retryAfter,
        },
        {
          status: 429,
          headers: {
            'Retry-After': rateLimitResult.retryAfter?.toString() || '900',
            'X-RateLimit-Limit': RATE_LIMIT.maxAttempts.toString(),
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': Math.ceil(
              Date.now() / 1000 + (rateLimitResult.retryAfter || 900)
            ).toString(),
          },
        }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = createPaymentIntentSchema.parse(body);

    console.log('💳 Creating payment intent for Field Day 2025 order:', {
      quantity: validatedData.quantity,
      customerEmail: validatedData.email,
      ip,
    });

    // Calculate order total
    const pricing = calculateOrderTotal(validatedData.quantity);

    // Check if an order already exists for this customer with pending payment
    // This prevents duplicate orders when payment intent creation is retried
    const existingOrdersResult = await fieldDay2025OrderService.getOrders({
      page: 1,
      limit: 1,
      search: validatedData.email,
    });

    let orderData;

    // Check if there's a recent pending order for this customer
    if (
      existingOrdersResult.data &&
      existingOrdersResult.data.orders.length > 0
    ) {
      const existingOrder = existingOrdersResult.data.orders[0];
      const orderAge =
        Date.now() - new Date(existingOrder.created_at || 0).getTime();
      const maxOrderAge = 30 * 60 * 1000; // 30 minutes

      // If there's a recent pending order with same details, reuse it
      if (
        orderAge < maxOrderAge &&
        existingOrder.payment_status === 'pending' &&
        existingOrder.quantity === validatedData.quantity &&
        existingOrder.first_name === validatedData.firstName &&
        existingOrder.last_name === validatedData.lastName &&
        existingOrder.email === validatedData.email
      ) {
        console.log(
          '🔄 Reusing existing pending order:',
          existingOrder.order_number
        );
        orderData = {
          orderId: existingOrder.id,
          orderNumber: existingOrder.order_number,
          totalAmountCents: existingOrder.total_amount_cents,
        };
      }
    }

    // Create new order only if no suitable existing order found
    if (!orderData) {
      const orderResult = await fieldDay2025OrderService.createOrder({
        ...validatedData,
      });

      if (orderResult.error || !orderResult.data) {
        console.error(
          '❌ Failed to create order for payment:',
          orderResult.error
        );

        // Handle duplicate order number error specifically
        if (
          orderResult.error?.includes(
            'duplicate key value violates unique constraint'
          )
        ) {
          return NextResponse.json(
            {
              success: false,
              error:
                'Order already exists. Please refresh the page and try again.',
              code: 'DUPLICATE_ORDER',
            },
            { status: 409 }
          );
        }

        return NextResponse.json(
          {
            success: false,
            error: orderResult.error || 'Failed to create order',
            code: 'ORDER_CREATION_FAILED',
          },
          { status: 500 }
        );
      }

      orderData = orderResult.data;
      console.log('✅ Created new order:', {
        orderId: orderData.orderId,
        orderNumber: orderData.orderNumber,
        isEmpty: orderData.orderNumber === '',
        totalAmount: orderData.totalAmountCents,
      });
    }

    const customerName = `${validatedData.firstName} ${validatedData.lastName}`;

    // Create payment intent with Stripe
    const paymentResult = await fieldDay2025PaymentService.createPaymentIntent({
      orderId: orderData.orderId,
      orderNumber: orderData.orderNumber,
      amount: pricing.totalAmountCents,
      customerEmail: validatedData.email,
      customerName,
      quantity: validatedData.quantity,
      metadata: {
        firstName: validatedData.firstName,
        lastName: validatedData.lastName,
        phoneNumber: validatedData.phoneNumber,
        eventType: 'field-day-2025',
        createdAt: new Date().toISOString(),
      },
    });

    if (paymentResult.error || !paymentResult.data) {
      console.error('❌ Failed to create payment intent:', paymentResult.error);

      // Clean up the order if payment intent creation failed
      try {
        await fieldDay2025OrderService.updateOrder(orderData.orderId, {
          orderStatus: 'cancelled',
          adminNotes: 'Payment intent creation failed - order cancelled',
        });
      } catch (cleanupError) {
        console.error(
          '❌ Failed to cleanup order after payment intent failure:',
          cleanupError
        );
      }

      return NextResponse.json(
        {
          success: false,
          error:
            paymentResult.error?.message || 'Failed to create payment intent',
          code: 'PAYMENT_INTENT_FAILED',
          details: paymentResult.error,
        },
        { status: 500 }
      );
    }

    // Update order with payment intent ID
    const updateResult = await fieldDay2025OrderService.updateOrder(
      orderData.orderId,
      {
        paymentIntentId: paymentResult.data.paymentIntentId,
        paymentStatus: PAYMENT_STATUSES.PENDING,
      }
    );

    if (updateResult.error) {
      console.error(
        '❌ Failed to update order with payment intent ID:',
        updateResult.error
      );
      // Continue anyway - the payment intent is created
    }

    console.log('✅ Payment intent created successfully:', {
      orderId: orderData.orderId,
      orderNumber: orderData.orderNumber,
      paymentIntentId: paymentResult.data.paymentIntentId,
      amount: paymentResult.data.amount,
    });

    return NextResponse.json({
      success: true,
      data: {
        clientSecret: paymentResult.data.clientSecret,
        paymentIntentId: paymentResult.data.paymentIntentId,
        amount: paymentResult.data.amount,
        currency: paymentResult.data.currency,
        order: {
          id: orderData.orderId,
          orderNumber: orderData.orderNumber,
          quantity: validatedData.quantity,
          totalAmount: orderData.totalAmountCents,
        },
      },
      meta: {
        timestamp: new Date().toISOString(),
        rateLimit: {
          limit: RATE_LIMIT.maxAttempts,
          remaining:
            RATE_LIMIT.maxAttempts - (rateLimitMap.get(ip)?.count || 0),
        },
      },
    });
  } catch (error) {
    console.error('💥 Error in create payment intent API:', error);

    // Handle validation errors
    if (error instanceof z.ZodError) {
      const firstError = error.errors[0];
      return NextResponse.json(
        {
          success: false,
          error: firstError?.message || 'Invalid order data',
          code: 'VALIDATION_ERROR',
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create payment intent',
        code: 'INTERNAL_ERROR',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// GET method to retrieve payment intent status
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const paymentIntentId = searchParams.get('payment_intent_id');

    if (!paymentIntentId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Payment intent ID is required',
          code: 'MISSING_PAYMENT_INTENT_ID',
        },
        { status: 400 }
      );
    }

    // Retrieve payment intent from Stripe
    const paymentResult =
      await fieldDay2025PaymentService.getPaymentIntent(paymentIntentId);

    if (paymentResult.error || !paymentResult.data) {
      return NextResponse.json(
        {
          success: false,
          error: 'Payment intent not found',
          code: 'PAYMENT_INTENT_NOT_FOUND',
        },
        { status: 404 }
      );
    }

    const paymentIntent = paymentResult.data;

    // Validate this is a Field Day 2025 payment intent
    if (
      !fieldDay2025PaymentService.isFieldDay2025PaymentIntent(paymentIntent)
    ) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid payment intent type',
          code: 'INVALID_PAYMENT_INTENT',
        },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        id: paymentIntent.id,
        status: paymentIntent.status,
        amount: paymentIntent.amount,
        currency: paymentIntent.currency,
        clientSecret: paymentIntent.client_secret,
        metadata: paymentIntent.metadata,
      },
    });
  } catch (error) {
    console.error('💥 Error retrieving payment intent:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to retrieve payment intent',
        code: 'INTERNAL_ERROR',
      },
      { status: 500 }
    );
  }
}
