import { NextRequest, NextResponse } from 'next/server';
import { fieldDay2025OrderSchema } from '@/lib/field-day-2025-config';
import { fieldDay2025OrderService } from '@/lib/services/field-day-2025-orders';
import { z } from 'zod';

// Rate limiting - simple in-memory store (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT_WINDOW = 15 * 60 * 1000; // 15 minutes
const RATE_LIMIT_MAX_REQUESTS = 5; // 5 orders per 15 minutes per IP

function checkRateLimit(ip: string): { allowed: boolean; resetTime?: number } {
  const now = Date.now();
  const key = `order_creation_${ip}`;
  const record = rateLimitStore.get(key);

  if (!record || now > record.resetTime) {
    // Reset or create new record
    rateLimitStore.set(key, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
    return { allowed: true };
  }

  if (record.count >= RATE_LIMIT_MAX_REQUESTS) {
    return { allowed: false, resetTime: record.resetTime };
  }

  // Increment count
  record.count++;
  rateLimitStore.set(key, record);
  return { allowed: true };
}

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const ip = request.headers.get('x-forwarded-for') || 'unknown';
    const rateLimitResult = checkRateLimit(ip);

    if (!rateLimitResult.allowed) {
      const resetTime = rateLimitResult.resetTime || Date.now();
      const waitTime = Math.ceil((resetTime - Date.now()) / 1000 / 60); // minutes

      return NextResponse.json(
        {
          error: `Too many order attempts. Please try again in ${waitTime} minutes.`,
          code: 'RATE_LIMIT_EXCEEDED',
        },
        {
          status: 429,
          headers: {
            'Retry-After': waitTime.toString(),
            'X-RateLimit-Limit': RATE_LIMIT_MAX_REQUESTS.toString(),
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': resetTime.toString(),
          },
        }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    console.log('📝 Field Day 2025 order creation request:', {
      quantity: body.quantity,
      firstName: body.firstName,
      lastName: body.lastName,
      email: body.email,
      phoneNumber: body.phoneNumber?.slice(0, 4) + '***', // Log partial phone for debugging
    });

    // Validate the order data
    const validatedData = fieldDay2025OrderSchema.parse(body);

    // Create the order using our service
    const result = await fieldDay2025OrderService.createOrder(validatedData);

    if (result.error) {
      console.error('❌ Order creation failed:', result.error);
      return NextResponse.json(
        {
          success: false,
          error: result.error,
          code: 'ORDER_CREATION_FAILED',
        },
        { status: 500 }
      );
    }

    if (!result.data) {
      console.error('❌ Order creation returned no data');
      return NextResponse.json(
        {
          success: false,
          error: 'Order creation failed - no data returned',
          code: 'NO_DATA_RETURNED',
        },
        { status: 500 }
      );
    }

    console.log('✅ Field Day 2025 order created successfully:', {
      orderId: result.data.orderId,
      orderNumber: result.data.orderNumber,
      totalAmountCents: result.data.totalAmountCents,
    });

    // Return success response
    return NextResponse.json({
      success: true,
      data: {
        orderId: result.data.orderId,
        orderNumber: result.data.orderNumber,
        totalAmountCents: result.data.totalAmountCents,
        totalAmount: result.data.totalAmountCents / 100,
        // Note: Payment intent creation will be handled separately
        // when integrating with Stripe in the next phase
      },
      message: `Order ${result.data.orderNumber} created successfully!`,
    });
  } catch (error) {
    console.error('💥 Error in Field Day 2025 order creation API:', error);

    // Handle validation errors
    if (error instanceof z.ZodError) {
      const firstError = error.errors[0];
      return NextResponse.json(
        {
          success: false,
          error:
            firstError?.message || 'Please check your input and try again.',
          code: 'VALIDATION_ERROR',
          details: error.errors,
        },
        { status: 400 }
      );
    }

    // Handle other errors
    return NextResponse.json(
      {
        success: false,
        error: 'An unexpected error occurred. Please try again.',
        code: 'INTERNAL_ERROR',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// GET method for order status/info (optional)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const orderNumber = searchParams.get('orderNumber');

    if (!orderNumber) {
      return NextResponse.json(
        {
          success: false,
          error: 'Order number is required',
          code: 'MISSING_ORDER_NUMBER',
        },
        { status: 400 }
      );
    }

    // For now, return basic info - this could be expanded for order tracking
    return NextResponse.json({
      success: true,
      data: {
        orderNumber,
        status: 'Order lookup not yet implemented',
        message: 'Order tracking will be available soon',
      },
    });
  } catch (error) {
    console.error('💥 Error in Field Day 2025 order lookup API:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to lookup order',
        code: 'LOOKUP_ERROR',
      },
      { status: 500 }
    );
  }
}
