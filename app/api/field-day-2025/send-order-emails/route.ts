import { NextRequest, NextResponse } from 'next/server';
import { fieldDay2025OrderService } from '@/lib/services/field-day-2025-orders';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { paymentIntentId, orderNumber } = body;

    if (!paymentIntentId && !orderNumber) {
      return NextResponse.json(
        { error: 'Payment intent ID or order number is required' },
        { status: 400 }
      );
    }

    console.log('📧 Sending Field Day 2025 order emails:', {
      paymentIntentId,
      orderNumber,
    });

    // Get order by payment intent ID or order number
    let orderResult;
    if (paymentIntentId) {
      orderResult =
        await fieldDay2025OrderService.getOrderByPaymentIntentId(
          paymentIntentId
        );
    } else {
      orderResult =
        await fieldDay2025OrderService.getOrderByOrderNumber(orderNumber);
    }

    if (orderResult.error || !orderResult.data) {
      console.error('❌ Order not found:', {
        error: orderResult.error,
        paymentIntentId,
        orderNumber,
      });
      return NextResponse.json(
        {
          error: `Order not found: ${orderResult.error || 'Unknown error'}`,
          details: {
            searchedBy: paymentIntentId ? 'paymentIntentId' : 'orderNumber',
            searchValue: paymentIntentId || orderNumber,
          },
        },
        { status: 404 }
      );
    }

    const order = orderResult.data;
    console.log('✅ Found order for email sending:', {
      orderId: order.id,
      orderNumber: order.order_number,
      email: order.email,
      paymentStatus: order.payment_status,
    });

    // Check payment status - if we have a paymentIntentId, verify with Stripe
    let paymentVerified = order.payment_status === 'succeeded';

    if (!paymentVerified && paymentIntentId) {
      console.log(
        '🔍 Payment status not succeeded in DB, verifying with Stripe...'
      );
      try {
        // Import the payment service to verify payment intent status
        const { fieldDay2025PaymentService } = await import(
          '@/lib/services/field-day-2025-payment'
        );
        const paymentResult =
          await fieldDay2025PaymentService.getPaymentIntent(paymentIntentId);

        if (paymentResult.data && paymentResult.data.status === 'succeeded') {
          paymentVerified = true;
          console.log('✅ Payment verified as succeeded via Stripe');

          // Update the order status in the database
          try {
            await fieldDay2025OrderService.updateOrder(order.id, {
              paymentStatus: 'succeeded',
              paymentTimestamp: new Date().toISOString(),
            });
            console.log('✅ Updated order payment status to succeeded');
          } catch (updateError) {
            console.error(
              '❌ Failed to update order payment status:',
              updateError
            );
            // Continue anyway since payment is verified
          }
        }
      } catch (stripeError) {
        console.error('❌ Failed to verify payment with Stripe:', stripeError);
      }
    }

    if (!paymentVerified) {
      console.log('⚠️ Order payment not verified, skipping email sending');
      return NextResponse.json(
        {
          error: 'Order payment not completed',
          details: {
            orderPaymentStatus: order.payment_status,
            paymentIntentId: paymentIntentId || 'not provided',
          },
        },
        { status: 400 }
      );
    }

    const emailResults = {
      customerEmail: { success: false, error: null as string | null },
      adminEmail: { success: false, error: null as string | null },
    };

    // Send customer confirmation email
    try {
      await fieldDay2025OrderService.sendOrderConfirmationEmail(order);
      emailResults.customerEmail.success = true;
      console.log('✅ Customer confirmation email sent successfully');
    } catch (emailError) {
      emailResults.customerEmail.error =
        emailError instanceof Error ? emailError.message : 'Unknown error';
      console.error(
        '❌ Failed to send customer confirmation email:',
        emailError
      );
    }

    // Send admin notification email
    try {
      await fieldDay2025OrderService.sendAdminNewOrderNotification(order);
      emailResults.adminEmail.success = true;
      console.log('✅ Admin notification email sent successfully');
    } catch (emailError) {
      emailResults.adminEmail.error =
        emailError instanceof Error ? emailError.message : 'Unknown error';
      console.error('❌ Failed to send admin notification email:', emailError);
    }

    // Return success if at least one email was sent
    const anyEmailSent =
      emailResults.customerEmail.success || emailResults.adminEmail.success;

    if (anyEmailSent) {
      return NextResponse.json({
        success: true,
        message: 'Order emails processed',
        results: emailResults,
        orderNumber: order.order_number,
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to send any emails',
          results: emailResults,
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('💥 Error in send order emails endpoint:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// GET method for testing the endpoint
export async function GET() {
  return NextResponse.json({
    success: true,
    message: 'Field Day 2025 send order emails endpoint is working',
    timestamp: new Date().toISOString(),
  });
}
