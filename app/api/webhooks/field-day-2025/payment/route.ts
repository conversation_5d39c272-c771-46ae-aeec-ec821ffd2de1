import { NextRequest, NextResponse } from 'next/server';
// import { headers } from 'next/headers';
import { stripe } from '@/lib/stripe/server-config';
import { fieldDay2025OrderService } from '@/lib/services/field-day-2025-orders';
import { fieldDay2025PaymentService } from '@/lib/services/field-day-2025-payment';
import { EmailService } from '@/lib/services/email';
import { PAYMENT_STATUSES } from '@/lib/field-day-2025-config';
import Stripe from 'stripe';

// This will be used when Stripe integration is added
// For now, we'll create a placeholder that can handle webhook events
const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

export async function POST(request: NextRequest) {
  if (!webhookSecret) {
    console.error('❌ STRIPE_WEBHOOK_SECRET is not configured');
    return NextResponse.json(
      { error: 'Webhook secret not configured' },
      { status: 500 }
    );
  }

  try {
    console.log('🔔 Field Day 2025 payment webhook received');

    // Get the raw body for signature verification
    const body = await request.text();
    const signature = request.headers.get('stripe-signature');

    if (!signature) {
      console.error('❌ Missing stripe-signature header');
      return NextResponse.json({ error: 'Missing signature' }, { status: 400 });
    }

    // Verify webhook signature
    let event: Stripe.Event;
    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
      console.log('✅ Webhook signature verified:', event.type);
    } catch (error) {
      console.error('❌ Webhook signature verification failed:', error);
      return NextResponse.json({ error: 'Invalid signature' }, { status: 400 });
    }

    // Validate this is a Field Day 2025 payment intent
    const paymentIntent = event.data.object as Stripe.PaymentIntent;
    if (
      !fieldDay2025PaymentService.isFieldDay2025PaymentIntent(paymentIntent)
    ) {
      console.log(
        'ℹ️ Ignoring non-Field Day 2025 payment intent:',
        paymentIntent.id
      );
      return NextResponse.json({ received: true });
    }

    // Handle different webhook event types
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentSucceeded(event);
        break;

      case 'payment_intent.payment_failed':
        await handlePaymentFailed(event);
        break;

      case 'payment_intent.canceled':
        await handlePaymentCanceled(event);
        break;

      case 'payment_intent.requires_action':
        await handlePaymentRequiresAction(event);
        break;

      default:
        console.log(`🤷 Unhandled webhook event type: ${event.type}`);
        break;
    }

    console.log('✅ Webhook processed successfully');
    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('💥 Error processing Field Day 2025 payment webhook:', error);
    return NextResponse.json(
      {
        error: 'Webhook processing failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

async function handlePaymentSucceeded(event: Stripe.Event) {
  try {
    const paymentIntent = event.data.object as Stripe.PaymentIntent;
    const paymentIntentId = paymentIntent.id;
    const paymentMethod = paymentIntent.payment_method_types?.[0] || 'unknown';

    // Extract order ID from payment intent metadata
    const orderId =
      fieldDay2025PaymentService.extractOrderIdFromPaymentIntent(paymentIntent);
    if (!orderId) {
      console.error(
        '❌ No order ID found in payment intent metadata:',
        paymentIntentId
      );
      throw new Error('Order ID not found in payment intent metadata');
    }

    console.log('💰 Payment succeeded for Field Day 2025 order:', {
      paymentIntentId,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
      paymentMethod,
    });

    // Update the order payment status
    const result = await fieldDay2025OrderService.updateOrder(orderId, {
      paymentStatus: PAYMENT_STATUSES.SUCCEEDED,
      paymentMethod,
      paymentTimestamp: new Date().toISOString(),
    });

    if (result.error) {
      console.error('❌ Failed to update order payment status:', result.error);
      throw new Error(`Failed to update order: ${result.error}`);
    }

    if (!result.data) {
      console.warn('⚠️ No order found for payment intent:', paymentIntentId);
      return;
    }

    const order = result.data;
    console.log('✅ Order payment status updated:', {
      orderId: order.id,
      orderNumber: order.order_number,
      newStatus: order.payment_status,
      orderStatus: order.order_status,
    });

    // Add customer to audience
    try {
      await EmailService.addToGeneralAudience(order.email);
      console.log('👥 Customer added to audience:', order.email);
    } catch (audienceError) {
      console.error('❌ Failed to add customer to audience:', audienceError);
      // Don't fail the webhook for audience errors
    }

    // Send emails as fallback (in case frontend email sending fails)
    // Check if emails were already sent by looking for a recent timestamp
    const emailSentRecently =
      order.updated_at &&
      Date.now() - new Date(order.updated_at).getTime() < 60000; // 1 minute

    if (!emailSentRecently) {
      console.log('📧 Sending fallback emails from webhook...');

      // Send confirmation email
      try {
        await fieldDay2025OrderService.sendOrderConfirmationEmail(order);
        console.log('📧 Fallback order confirmation email sent successfully');
      } catch (emailError) {
        console.error(
          '❌ Failed to send fallback confirmation email:',
          emailError
        );
      }

      // Send admin notification
      try {
        await fieldDay2025OrderService.sendAdminNewOrderNotification(order);
        console.log('📧 Fallback admin notification email sent successfully');
      } catch (emailError) {
        console.error(
          '❌ Failed to send fallback admin notification:',
          emailError
        );
      }
    } else {
      console.log(
        '📧 Emails likely already sent from frontend, skipping webhook emails'
      );
    }
  } catch (error) {
    console.error('💥 Error handling payment succeeded:', error);
    throw error;
  }
}

async function handlePaymentFailed(event: Stripe.Event) {
  try {
    const paymentIntent = event.data.object as Stripe.PaymentIntent;
    const paymentIntentId = paymentIntent.id;
    const failureReason =
      paymentIntent.last_payment_error?.message || 'Payment failed';

    // Extract order ID from payment intent metadata
    const orderId =
      fieldDay2025PaymentService.extractOrderIdFromPaymentIntent(paymentIntent);
    if (!orderId) {
      console.error(
        '❌ No order ID found in payment intent metadata:',
        paymentIntentId
      );
      throw new Error('Order ID not found in payment intent metadata');
    }

    console.log('❌ Payment failed for Field Day 2025 order:', {
      paymentIntentId,
      failureReason,
      amount: paymentIntent.amount,
    });

    // Update the order payment status
    const result = await fieldDay2025OrderService.updateOrder(orderId, {
      paymentStatus: PAYMENT_STATUSES.FAILED,
      adminNotes: `Payment failed: ${failureReason}`,
    });

    if (result.error) {
      console.error('❌ Failed to update order payment status:', result.error);
      throw new Error(`Failed to update order: ${result.error}`);
    }

    if (result.data) {
      console.log('✅ Order payment failure recorded:', {
        orderId: result.data.id,
        orderNumber: result.data.order_number,
        newStatus: result.data.payment_status,
      });
    }

    // TODO: Send payment failure notification (will be implemented in Phase 5)
    console.log('📧 Payment failure notification will be sent in Phase 5');
  } catch (error) {
    console.error('💥 Error handling payment failed:', error);
    throw error;
  }
}

async function handlePaymentCanceled(event: Stripe.Event) {
  try {
    const paymentIntent = event.data.object as Stripe.PaymentIntent;
    const paymentIntentId = paymentIntent.id;

    // Extract order ID from payment intent metadata
    const orderId =
      fieldDay2025PaymentService.extractOrderIdFromPaymentIntent(paymentIntent);
    if (!orderId) {
      console.error(
        '❌ No order ID found in payment intent metadata:',
        paymentIntentId
      );
      throw new Error('Order ID not found in payment intent metadata');
    }

    console.log('🚫 Payment canceled for Field Day 2025 order:', {
      paymentIntentId,
      amount: paymentIntent.amount,
    });

    // Update the order payment status
    const result = await fieldDay2025OrderService.updateOrder(orderId, {
      paymentStatus: PAYMENT_STATUSES.CANCELED,
      adminNotes: 'Payment was cancelled by customer or system',
    });

    if (result.error) {
      console.error('❌ Failed to update order payment status:', result.error);
      throw new Error(`Failed to update order: ${result.error}`);
    }

    if (result.data) {
      console.log('✅ Order payment cancellation recorded:', {
        orderId: result.data.id,
        orderNumber: result.data.order_number,
        newStatus: result.data.payment_status,
      });
    }
  } catch (error) {
    console.error('💥 Error handling payment canceled:', error);
    throw error;
  }
}

async function handlePaymentRequiresAction(event: Stripe.Event) {
  try {
    const paymentIntent = event.data.object as Stripe.PaymentIntent;
    const paymentIntentId = paymentIntent.id;

    // Extract order ID from payment intent metadata
    const orderId =
      fieldDay2025PaymentService.extractOrderIdFromPaymentIntent(paymentIntent);
    if (!orderId) {
      console.error(
        '❌ No order ID found in payment intent metadata:',
        paymentIntentId
      );
      throw new Error('Order ID not found in payment intent metadata');
    }

    console.log('⚠️ Payment requires action for Field Day 2025 order:', {
      paymentIntentId,
      amount: paymentIntent.amount,
      nextAction: paymentIntent.next_action?.type,
    });

    // Update the order payment status
    const result = await fieldDay2025OrderService.updateOrder(orderId, {
      paymentStatus: PAYMENT_STATUSES.REQUIRES_ACTION,
      adminNotes: `Payment requires action: ${paymentIntent.next_action?.type || 'unknown'}`,
    });

    if (result.error) {
      console.error('❌ Failed to update order payment status:', result.error);
      throw new Error(`Failed to update order: ${result.error}`);
    }

    if (result.data) {
      console.log('✅ Order payment action requirement recorded:', {
        orderId: result.data.id,
        orderNumber: result.data.order_number,
        newStatus: result.data.payment_status,
      });
    }
  } catch (error) {
    console.error('💥 Error handling payment requires action:', error);
    throw error;
  }
}
