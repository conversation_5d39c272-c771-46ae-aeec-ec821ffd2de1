import { paymentDatabaseService } from '@/lib/services/payment-database';
import { stripePaymentService } from '@/lib/services/stripe-payment';
import { SubscriptionServerService } from '@/lib/services/subscription-server';
import { stripe } from '@/lib/stripe/server-config';
import { headers } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import Stripe from 'stripe';

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

// Health check endpoint for webhook debugging
export async function GET() {
  const healthCheck = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    webhook: {
      hasSecret: !!webhookSecret,
      secretLength: webhookSecret ? webhookSecret.length : 0,
      environment: process.env.NODE_ENV,
    },
    services: {
      stripe: !!stripe,
      paymentDatabase: !!paymentDatabaseService,
      stripePayment: !!stripePaymentService,
    },
  };

  console.log('🏥 Webhook health check requested:', healthCheck);

  return NextResponse.json(healthCheck);
}

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  const requestId = Math.random().toString(36).substring(7);

  console.log(
    `🔔 [${requestId}] Stripe webhook received at ${new Date().toISOString()}`
  );

  // Check webhook secret configuration
  if (!webhookSecret) {
    console.error(`❌ [${requestId}] STRIPE_WEBHOOK_SECRET is not configured`);
    return NextResponse.json(
      { error: 'Webhook secret not configured', requestId },
      { status: 500 }
    );
  }

  let body: string;
  let signature: string | null;
  let event: Stripe.Event;

  try {
    // Get request body and signature
    body = await request.text();
    const headersList = await headers();
    signature = headersList.get('stripe-signature');

    console.log(
      `📝 [${requestId}] Body length: ${body.length}, Has signature: ${!!signature}`
    );

    if (!signature) {
      console.error(`❌ [${requestId}] Missing stripe-signature header`);
      return NextResponse.json(
        { error: 'Missing signature', requestId },
        { status: 400 }
      );
    }

    // Verify webhook signature
    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
      console.log(
        `✅ [${requestId}] Webhook signature verified for event: ${event.type} (${event.id})`
      );
    } catch (error) {
      console.error(
        `❌ [${requestId}] Webhook signature verification failed:`,
        error
      );
      return NextResponse.json(
        { error: 'Invalid signature', requestId },
        { status: 400 }
      );
    }

    // Handle the event with proper error handling
    let handlerResult: { success: boolean; error?: string } = { success: true };

    try {
      switch (event.type) {
        case 'payment_intent.succeeded':
          handlerResult = await handlePaymentSucceeded(
            event.data.object as Stripe.PaymentIntent,
            requestId
          );
          break;

        case 'payment_intent.payment_failed':
          handlerResult = await handlePaymentFailed(
            event.data.object as Stripe.PaymentIntent,
            requestId
          );
          break;

        case 'payment_intent.canceled':
          handlerResult = await handlePaymentCanceled(
            event.data.object as Stripe.PaymentIntent,
            requestId
          );
          break;

        case 'payment_intent.requires_action':
          handlerResult = await handlePaymentRequiresAction(
            event.data.object as Stripe.PaymentIntent,
            requestId
          );
          break;

        default:
          console.log(`ℹ️ [${requestId}] Unhandled event type: ${event.type}`);
          handlerResult = { success: true }; // Not an error, just unhandled
      }
    } catch (handlerError) {
      console.error(`❌ [${requestId}] Event handler failed:`, handlerError);
      handlerResult = {
        success: false,
        error:
          handlerError instanceof Error
            ? handlerError.message
            : 'Handler failed',
      };
    }

    const processingTime = Date.now() - startTime;

    if (handlerResult.success) {
      console.log(
        `✅ [${requestId}] Webhook processed successfully in ${processingTime}ms`
      );
      return NextResponse.json({
        received: true,
        requestId,
        eventType: event.type,
        processingTime,
      });
    } else {
      console.error(
        `❌ [${requestId}] Webhook handler failed: ${handlerResult.error}`
      );
      return NextResponse.json(
        {
          error: 'Event handler failed',
          details: handlerResult.error,
          requestId,
          eventType: event.type,
        },
        { status: 500 }
      );
    }
  } catch (error) {
    const processingTime = Date.now() - startTime;
    console.error(
      `💥 [${requestId}] Critical webhook error after ${processingTime}ms:`,
      error
    );

    return NextResponse.json(
      {
        error: 'Webhook processing failed',
        details: error instanceof Error ? error.message : 'Unknown error',
        requestId,
        processingTime,
      },
      { status: 500 }
    );
  }
}

async function handlePaymentSucceeded(
  paymentIntent: Stripe.PaymentIntent,
  requestId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    console.log(
      `💰 [${requestId}] Processing payment succeeded: ${paymentIntent.id}`
    );

    // Update payment status in database
    const paymentStatus = stripePaymentService.mapStripeStatusToPaymentStatus(
      paymentIntent.status
    );

    const updateResult = await paymentDatabaseService.updatePaymentStatus(
      paymentIntent.id,
      paymentStatus
    );

    if (updateResult.error) {
      console.error(
        `❌ [${requestId}] Failed to update payment status:`,
        updateResult.error
      );
      return {
        success: false,
        error: 'Failed to update payment status in database',
      };
    }

    console.log(
      `✅ [${requestId}] Payment status updated to: ${paymentStatus}`
    );

    // If this is the first successful payment, ensure subscription is active
    const { data: payment, error: paymentFetchError } =
      await paymentDatabaseService.getPaymentByPaymentId(paymentIntent.id);

    if (paymentFetchError) {
      console.error(
        `❌ [${requestId}] Failed to fetch payment record:`,
        paymentFetchError
      );
      return { success: false, error: 'Failed to fetch payment record' };
    }

    if (payment?.subscription_id) {
      console.log(
        `🔄 [${requestId}] Activating subscription: ${payment.subscription_id}`
      );

      const subscriptionService = new SubscriptionServerService();
      const subscriptionUpdateResult =
        await subscriptionService.updateSubscription(payment.subscription_id, {
          status: 'active',
          is_active: true,
        });

      if (subscriptionUpdateResult.error) {
        console.error(
          `❌ [${requestId}] Failed to activate subscription:`,
          subscriptionUpdateResult.error
        );
        return { success: false, error: 'Failed to activate subscription' };
      }

      console.log(`✅ [${requestId}] Subscription activated successfully`);
    } else {
      console.log(`ℹ️ [${requestId}] No subscription associated with payment`);
    }

    console.log(`✅ [${requestId}] Payment success handling completed`);
    return { success: true };
  } catch (error) {
    console.error(`💥 [${requestId}] Error handling payment succeeded:`, error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Unknown error in payment success handler',
    };
  }
}

async function handlePaymentFailed(
  paymentIntent: Stripe.PaymentIntent,
  requestId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    console.log(
      `❌ [${requestId}] Processing payment failed: ${paymentIntent.id}`
    );

    // Update payment status in database
    const paymentStatus = stripePaymentService.mapStripeStatusToPaymentStatus(
      paymentIntent.status
    );

    const updateResult = await paymentDatabaseService.updatePaymentStatus(
      paymentIntent.id,
      paymentStatus
    );

    if (updateResult.error) {
      console.error(
        `❌ [${requestId}] Failed to update payment status:`,
        updateResult.error
      );
      return {
        success: false,
        error: 'Failed to update payment status in database',
      };
    }

    console.log(
      `✅ [${requestId}] Payment status updated to: ${paymentStatus}`
    );

    // Update subscription status to indicate payment failure
    const { data: payment, error: paymentFetchError } =
      await paymentDatabaseService.getPaymentByPaymentId(paymentIntent.id);

    if (paymentFetchError) {
      console.error(
        `❌ [${requestId}] Failed to fetch payment record:`,
        paymentFetchError
      );
      return { success: false, error: 'Failed to fetch payment record' };
    }

    if (payment?.subscription_id) {
      console.log(
        `🔄 [${requestId}] Marking subscription as payment failed: ${payment.subscription_id}`
      );

      const subscriptionService = new SubscriptionServerService();
      const subscriptionUpdateResult =
        await subscriptionService.updateSubscription(payment.subscription_id, {
          status: 'payment_failed',
          is_active: false,
        });

      if (subscriptionUpdateResult.error) {
        console.error(
          `❌ [${requestId}] Failed to update subscription status:`,
          subscriptionUpdateResult.error
        );
        return {
          success: false,
          error: 'Failed to update subscription status',
        };
      }

      console.log(`✅ [${requestId}] Subscription marked as payment failed`);
    } else {
      console.log(`ℹ️ [${requestId}] No subscription associated with payment`);
    }

    console.log(`✅ [${requestId}] Payment failure handling completed`);
    return { success: true };
  } catch (error) {
    console.error(`💥 [${requestId}] Error handling payment failed:`, error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Unknown error in payment failure handler',
    };
  }
}

async function handlePaymentCanceled(
  paymentIntent: Stripe.PaymentIntent,
  requestId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    console.log(
      `🚫 [${requestId}] Processing payment canceled: ${paymentIntent.id}`
    );

    // Update payment status in database
    const paymentStatus = stripePaymentService.mapStripeStatusToPaymentStatus(
      paymentIntent.status
    );

    const updateResult = await paymentDatabaseService.updatePaymentStatus(
      paymentIntent.id,
      paymentStatus
    );

    if (updateResult.error) {
      console.error(
        `❌ [${requestId}] Failed to update payment status:`,
        updateResult.error
      );
      return {
        success: false,
        error: 'Failed to update payment status in database',
      };
    }

    console.log(
      `✅ [${requestId}] Payment status updated to: ${paymentStatus}`
    );

    // Update subscription status
    const { data: payment, error: paymentFetchError } =
      await paymentDatabaseService.getPaymentByPaymentId(paymentIntent.id);

    if (paymentFetchError) {
      console.error(
        `❌ [${requestId}] Failed to fetch payment record:`,
        paymentFetchError
      );
      return { success: false, error: 'Failed to fetch payment record' };
    }

    if (payment?.subscription_id) {
      console.log(
        `🔄 [${requestId}] Canceling subscription: ${payment.subscription_id}`
      );

      const subscriptionService = new SubscriptionServerService();
      const subscriptionUpdateResult =
        await subscriptionService.updateSubscription(payment.subscription_id, {
          status: 'canceled',
          is_active: false,
        });

      if (subscriptionUpdateResult.error) {
        console.error(
          `❌ [${requestId}] Failed to cancel subscription:`,
          subscriptionUpdateResult.error
        );
        return { success: false, error: 'Failed to cancel subscription' };
      }

      console.log(`✅ [${requestId}] Subscription canceled successfully`);
    } else {
      console.log(`ℹ️ [${requestId}] No subscription associated with payment`);
    }

    console.log(`✅ [${requestId}] Payment cancellation handling completed`);
    return { success: true };
  } catch (error) {
    console.error(`💥 [${requestId}] Error handling payment canceled:`, error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Unknown error in payment cancellation handler',
    };
  }
}

async function handlePaymentRequiresAction(
  paymentIntent: Stripe.PaymentIntent,
  requestId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    console.log(
      `⚠️ [${requestId}] Processing payment requires action: ${paymentIntent.id}`
    );

    // Update payment status in database
    const paymentStatus = stripePaymentService.mapStripeStatusToPaymentStatus(
      paymentIntent.status
    );

    const updateResult = await paymentDatabaseService.updatePaymentStatus(
      paymentIntent.id,
      paymentStatus
    );

    if (updateResult.error) {
      console.error(
        `❌ [${requestId}] Failed to update payment status:`,
        updateResult.error
      );
      return {
        success: false,
        error: 'Failed to update payment status in database',
      };
    }

    console.log(
      `✅ [${requestId}] Payment status updated to: ${paymentStatus}`
    );
    console.log(`✅ [${requestId}] Payment requires action handling completed`);
    return { success: true };
  } catch (error) {
    console.error(
      `💥 [${requestId}] Error handling payment requires action:`,
      error
    );
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Unknown error in payment requires action handler',
    };
  }
}
