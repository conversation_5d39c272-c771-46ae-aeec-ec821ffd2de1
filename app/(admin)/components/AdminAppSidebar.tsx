'use client';

import * as React from 'react';
import {
  IconUsers,
  IconPackage,
  IconMessage,
  IconApple,
  IconCalendar,
  IconHome,
  IconTruck,
} from '@tabler/icons-react';

import { NavMain } from '@/components/navigation/nav-main';
import { AdminNavUser } from './AdminNavUser';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';
import Link from 'next/link';

const data = {
  navMain: [
    {
      title: 'Dashboard',
      url: '/admin',
      icon: IconHome,
    },
    {
      title: 'Subscribers',
      url: '/admin/subscribers',
      icon: IconUsers,
    },
    {
      title: 'Subscriptions',
      url: '/admin/subscriptions',
      icon: IconPackage,
    },
    {
      title: 'Deliveries',
      url: '/admin/deliveries',
      icon: IconTruck,
    },
    {
      title: 'Box Contents',
      url: '/admin/box-contents',
      icon: IconApple,
    },
    {
      title: 'Contact Forms',
      url: '/admin/contact-forms',
      icon: IconMessage,
    },
    {
      title: 'Field Day 2025',
      url: '/admin/field-day-2025',
      icon: IconCalendar,
    },
  ],
};

export function AdminAppSidebar({
  ...props
}: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible='offcanvas' {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className='data-[slot=sidebar-menu-button]:!p-1.5'
            >
              <Link href='/'>
                <span className='text-base font-bold'>
                  <span className='text-green-600'>Aseda</span>
                  <span className='text-green-900'>Foods</span>
                </span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
      </SidebarContent>
      <SidebarFooter>
        <AdminNavUser />
      </SidebarFooter>
    </Sidebar>
  );
}
