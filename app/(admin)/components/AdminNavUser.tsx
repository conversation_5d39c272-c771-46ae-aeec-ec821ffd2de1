'use client';

import { useAuth } from '@/components/auth/AuthProvider';
import { Button } from '@/components/ui/button';
import { SidebarMenu, SidebarMenuItem } from '@/components/ui/sidebar';
import { IconLogout } from '@tabler/icons-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

export function AdminNavUser() {
  const router = useRouter();
  const { user, signOut } = useAuth();
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const handleLogout = async () => {
    if (isLoggingOut) return;

    setIsLoggingOut(true);

    try {
      await signOut();
      router.push('/login');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setIsLoggingOut(false);
    }
  };

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <div className='flex flex-col gap-3 p-2'>
          <div className='flex-1 text-left text-sm leading-tight'>
            <div className='truncate font-medium'>{user?.name}</div>
            <div className='text-muted-foreground truncate text-xs'>
              {user?.role || 'Admin'} • {user?.email}
            </div>
          </div>
          <Button
            variant='outline'
            size='sm'
            onClick={handleLogout}
            disabled={isLoggingOut}
            className='w-full hover:bg-destructive hover:text-destructive-foreground disabled:opacity-50'
            title={isLoggingOut ? 'Logging out...' : 'Logout'}
          >
            <IconLogout className='h-4 w-4' />
            <span className=''>
              {isLoggingOut ? 'Logging out...' : 'Logout'}
            </span>
            <span className='sr-only'>
              {isLoggingOut ? 'Logging out...' : 'Logout'}
            </span>
          </Button>
        </div>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
