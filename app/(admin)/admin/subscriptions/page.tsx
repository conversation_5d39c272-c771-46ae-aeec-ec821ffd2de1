import { redirect } from 'next/navigation';
import { authServer } from '@/lib/utils/auth-server';
import { SubscriptionsTable } from '@/components/admin';

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic';

export default async function SubscriptionsPage() {
  const { data: user } = await authServer.getCurrentUser();

  if (!user) {
    redirect('/login');
  }

  if (user.role !== 'admin') {
    redirect('/dashboard');
  }

  return (
    <div className='space-y-4 md:space-y-6'>
      <div className='flex justify-between items-center'>
        <div>
          <h1 className='text-3xl font-bold text-gray-900'>Subscriptions</h1>
          <p className='text-gray-600 mt-2'>
            Manage customer subscriptions and delivery schedules
          </p>
        </div>
      </div>

      <SubscriptionsTable />
    </div>
  );
}
