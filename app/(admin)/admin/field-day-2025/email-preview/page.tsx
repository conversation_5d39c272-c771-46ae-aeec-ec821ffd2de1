import { redirect } from 'next/navigation';
import { authServer } from '@/lib/utils/auth-server';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Mail,
  Eye,
  Send,
  ArrowLeft,
  User,
  UserCheck,
  Settings,
} from 'lucide-react';
import { FieldDay2025OrderConfirmation } from '@/lib/emails/templates/field-day-2025/order-confirmation';
import { FieldDay2025PickupReady } from '@/lib/emails/templates/field-day-2025/pickup-ready';
import { FieldDay2025AdminNewOrder } from '@/lib/emails/templates/field-day-2025/admin-new-order';
import Link from 'next/link';

// Admin authentication check
async function AdminAuthCheck() {
  const { data: user } = await authServer.getCurrentUser();

  if (!user) {
    redirect('/login');
  }

  if (user.role !== 'admin') {
    redirect('/dashboard');
  }

  return null;
}

// Mock data for email previews (using real Field Day 2025 configuration)
const mockOrderData = {
  orderNumber: 'FD2025-001234',
  orderDate: new Date('2024-12-21T10:30:00Z'),
  quantity: 2,
  unitPrice: 3599, // $35.99 in cents (actual Field Day 2025 price)
  totalAmount: 7198, // $71.98 in cents (2 * $35.99)
  taxAmount: 0, // No tax for Field Day 2025
  productName: 'Field Day 2025 - Medium Box', // Actual product name
};

const mockPickupData = {
  eventDate: '2025-06-15', // Actual Field Day 2025 date from config
  location: 'Elite Bodies Fitness', // Actual pickup location
  address: '1234 Fitness Way, Community Center, GA 30309',
  hours: '10:00 AM - 4:00 PM',
  contactInfo: '(*************',
};

const mockCustomerData = {
  name: 'John Doe',
  email: '<EMAIL>',
  phone: '(*************',
};

const mockAdminOrderData = {
  ...mockOrderData,
  customerName: mockCustomerData.name,
  customerEmail: mockCustomerData.email,
  customerPhone: mockCustomerData.phone,
  paymentStatus: 'succeeded',
};

// Email template components
function EmailPreviewCard({
  title,
  description,
  icon: Icon,
  children,
}: {
  title: string;
  description: string;
  icon: any;
  children: React.ReactNode;
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          <Icon className='h-5 w-5' />
          {title}
        </CardTitle>
        <p className='text-sm text-gray-600'>{description}</p>
      </CardHeader>
      <CardContent>
        <div className='border rounded-lg overflow-hidden'>
          <div className='bg-gray-50 p-3 border-b'>
            <div className='flex items-center justify-between'>
              <span className='text-sm font-medium'>Email Preview</span>
              <Badge variant='secondary'>Template</Badge>
            </div>
          </div>
          <div className='max-h-96 overflow-y-auto'>{children}</div>
        </div>
      </CardContent>
    </Card>
  );
}

export default function FieldDay2025EmailPreviewPage() {
  return (
    <>
      <AdminAuthCheck />

      <div className='space-y-6'>
        {/* Page Header */}
        <div className='flex items-center gap-4'>
          <Button variant='ghost' size='sm' asChild>
            <Link href='/admin/field-day-2025'>
              <ArrowLeft className='mr-2 h-4 w-4' />
              Back to Dashboard
            </Link>
          </Button>

          <div>
            <h1 className='text-2xl md:text-3xl font-bold text-gray-900'>
              Email Template Previews
            </h1>
            <p className='text-gray-600 mt-1'>
              Preview Field Day 2025 email templates with sample data
            </p>
          </div>
        </div>

        {/* Email Templates Grid */}
        <div className='grid gap-8'>
          {/* Order Confirmation Email */}
          <EmailPreviewCard
            title='Order Confirmation Email'
            description='Sent to customers after successful payment'
            icon={User}
          >
            <FieldDay2025OrderConfirmation
              customerName={mockCustomerData.name}
              orderData={mockOrderData}
              pickupData={mockPickupData}
            />
          </EmailPreviewCard>

          {/* Pickup Ready Email */}
          <EmailPreviewCard
            title='Pickup Ready Notification'
            description='Sent when order is ready for pickup'
            icon={UserCheck}
          >
            <FieldDay2025PickupReady
              customerName={mockCustomerData.name}
              orderData={{
                orderNumber: mockOrderData.orderNumber,
                quantity: mockOrderData.quantity,
                productName: mockOrderData.productName,
              }}
              pickupData={mockPickupData}
            />
          </EmailPreviewCard>

          {/* Admin New Order Email */}
          <EmailPreviewCard
            title='Admin New Order Notification'
            description='Sent to admin when new order is placed'
            icon={Settings}
          >
            <FieldDay2025AdminNewOrder orderData={mockAdminOrderData} />
          </EmailPreviewCard>
        </div>

        {/* Email Testing Section */}
        <Card className='bg-blue-50 border-blue-200'>
          <CardHeader>
            <CardTitle className='flex items-center gap-2 text-blue-800'>
              <Send className='h-5 w-5' />
              Email Testing
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              <p className='text-blue-700'>
                These are preview versions of the Field Day 2025 email
                templates. In production, emails are automatically sent when:
              </p>

              <ul className='space-y-2 text-blue-700'>
                <li className='flex items-start gap-2'>
                  <span className='w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0'></span>
                  <span>
                    <strong>Order Confirmation:</strong> Payment is successfully
                    processed
                  </span>
                </li>
                <li className='flex items-start gap-2'>
                  <span className='w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0'></span>
                  <span>
                    <strong>Pickup Ready:</strong> Admin marks order pickup
                    status as &apos;ready&apos;
                  </span>
                </li>
                <li className='flex items-start gap-2'>
                  <span className='w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0'></span>
                  <span>
                    <strong>Admin Notification:</strong> New order is placed
                    (regardless of payment status)
                  </span>
                </li>
              </ul>

              <div className='flex gap-2 pt-4'>
                <Button variant='outline' size='sm' asChild>
                  <Link href='/admin/field-day-2025/orders'>
                    <Eye className='mr-2 h-4 w-4' />
                    View Orders
                  </Link>
                </Button>

                <Button variant='outline' size='sm' asChild>
                  <Link href='/admin/field-day-2025'>
                    <Mail className='mr-2 h-4 w-4' />
                    Admin Dashboard
                  </Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Template Information */}
        <Card>
          <CardHeader>
            <CardTitle>Template Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className='grid md:grid-cols-3 gap-6'>
              <div>
                <h4 className='font-medium text-gray-900 mb-2'>
                  Order Confirmation
                </h4>
                <ul className='text-sm text-gray-600 space-y-1'>
                  <li>• Order details and pricing</li>
                  <li>• Event information</li>
                  <li>• Pickup instructions</li>
                  <li>• What&apos;s included in the box</li>
                  <li>• Contact information</li>
                </ul>
              </div>

              <div>
                <h4 className='font-medium text-gray-900 mb-2'>Pickup Ready</h4>
                <ul className='text-sm text-gray-600 space-y-1'>
                  <li>• Ready for pickup notification</li>
                  <li>• Pickup instructions</li>
                  <li>• What to bring</li>
                  <li>• Event highlights</li>
                  <li>• Contact for issues</li>
                </ul>
              </div>

              <div>
                <h4 className='font-medium text-gray-900 mb-2'>
                  Admin Notification
                </h4>
                <ul className='text-sm text-gray-600 space-y-1'>
                  <li>• Order summary</li>
                  <li>• Customer information</li>
                  <li>• Payment status</li>
                  <li>• Next steps</li>
                  <li>• Quick action links</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
}
