import { Suspense } from 'react';
import { redirect } from 'next/navigation';
import { authServer } from '@/lib/utils/auth-server';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Calendar,
  Package,
  Users,
  TrendingUp,
  Download,
  Plus,
} from 'lucide-react';
import { FieldDay2025OrdersTable } from '@/components/admin/field-day-2025/FieldDay2025OrdersTable';
import { FieldDay2025Stats } from '@/components/admin/field-day-2025/FieldDay2025Stats';
import { FieldDay2025OrdersSkeleton } from '@/components/admin/field-day-2025/FieldDay2025OrdersSkeleton';
import {
  FieldDay2025StatsErrorFallback,
  FieldDay2025OrdersErrorFallback,
  FieldDay2025RefreshButton,
} from '@/components/admin/field-day-2025/FieldDay2025ErrorFallbacks';
import { PPRErrorBoundary } from '@/components/ui/ppr-error-boundary';
import Link from 'next/link';

// Admin authentication check
async function AdminAuthCheck() {
  const { data: user } = await authServer.getCurrentUser();

  if (!user) {
    redirect('/login');
  }

  if (user.role !== 'admin') {
    redirect('/dashboard');
  }

  return null;
}

// Page header component
function PageHeader() {
  return (
    <div className='flex flex-col gap-4 md:flex-row md:items-center md:justify-between'>
      <div>
        <h1 className='text-2xl md:text-3xl font-bold text-gray-900'>
          Field Day 2025 Orders
        </h1>
        <p className='text-gray-600 mt-2'>
          Manage orders for the Field Day 2025 special event
        </p>
      </div>

      <div className='flex flex-col gap-2 sm:flex-row'>
        <FieldDay2025RefreshButton />

        <Button variant='outline' size='sm' asChild>
          <Link href='/admin/field-day-2025/export'>
            <Download className='mr-2 h-4 w-4' />
            Export Data
          </Link>
        </Button>

        <Button size='sm' asChild>
          <Link href='/field-day-2025'>
            <Plus className='mr-2 h-4 w-4' />
            View Product Page
          </Link>
        </Button>
      </div>
    </div>
  );
}

// Event information component
function EventInfo() {
  return (
    <Card className='bg-green-50 border-green-200'>
      <CardHeader>
        <CardTitle className='flex items-center gap-2 text-green-800'>
          <Calendar className='h-5 w-5' />
          Field Day 2025 Event Information
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className='grid md:grid-cols-3 gap-4'>
          <div>
            <h4 className='font-medium text-green-900 mb-1'>Event Date</h4>
            <p className='text-green-700'>TBD - Coming Soon</p>
          </div>
          <div>
            <h4 className='font-medium text-green-900 mb-1'>Pickup Location</h4>
            <p className='text-green-700'>Elite Bodies Fitness</p>
          </div>
          <div>
            <h4 className='font-medium text-green-900 mb-1'>Product</h4>
            <p className='text-green-700'>Medium Produce Box - $35.99</p>
          </div>
        </div>

        <div className='mt-4 flex flex-wrap gap-2'>
          <Badge variant='secondary' className='bg-green-100 text-green-800'>
            Special Event
          </Badge>
          <Badge variant='outline'>Pickup Only</Badge>
          <Badge variant='outline'>12-15 Items per Box</Badge>
        </div>
      </CardContent>
    </Card>
  );
}

// Quick actions component
function QuickActions() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Quick Actions</CardTitle>
      </CardHeader>
      <CardContent>
        <div className='grid grid-cols-2 md:grid-cols-4 gap-3'>
          <Button
            variant='outline'
            size='sm'
            className='h-auto p-3 flex-col gap-2'
            asChild
          >
            <Link href='/admin/field-day-2025/orders'>
              <Package className='h-5 w-5' />
              <span className='text-xs'>All Orders</span>
            </Link>
          </Button>

          <Button
            variant='outline'
            size='sm'
            className='h-auto p-3 flex-col gap-2'
            asChild
          >
            <Link href='/admin/field-day-2025/orders?status=pending'>
              <Users className='h-5 w-5' />
              <span className='text-xs'>Pending Orders</span>
            </Link>
          </Button>

          <Button
            variant='outline'
            size='sm'
            className='h-auto p-3 flex-col gap-2'
            asChild
          >
            <Link href='/admin/field-day-2025/orders?status=confirmed'>
              <TrendingUp className='h-5 w-5' />
              <span className='text-xs'>Confirmed Orders</span>
            </Link>
          </Button>

          <Button
            variant='outline'
            size='sm'
            className='h-auto p-3 flex-col gap-2'
            asChild
          >
            <Link href='/admin/field-day-2025/export'>
              <Download className='h-5 w-5' />
              <span className='text-xs'>Export Data</span>
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

export default function FieldDay2025AdminPage() {
  return (
    <>
      <AdminAuthCheck />

      <div className='space-y-6'>
        <PageHeader />

        <EventInfo />

        {/* Statistics Cards */}
        <PPRErrorBoundary fallback={<FieldDay2025StatsErrorFallback />}>
          <Suspense fallback={<FieldDay2025OrdersSkeleton />}>
            <FieldDay2025Stats />
          </Suspense>
        </PPRErrorBoundary>

        <QuickActions />

        {/* Orders Table */}
        <PPRErrorBoundary fallback={<FieldDay2025OrdersErrorFallback />}>
          <Suspense fallback={<FieldDay2025OrdersSkeleton />}>
            <FieldDay2025OrdersTable />
          </Suspense>
        </PPRErrorBoundary>
      </div>
    </>
  );
}
