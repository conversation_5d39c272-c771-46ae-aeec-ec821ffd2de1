import { Suspense } from 'react';
import { redirect } from 'next/navigation';
import { authServer } from '@/lib/utils/auth-server';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Package, Download, Filter, Plus, ArrowLeft } from 'lucide-react';
import { FieldDay2025OrdersTable } from '@/components/admin/field-day-2025/FieldDay2025OrdersTable';
import { FieldDay2025OrdersFilters } from '@/components/admin/field-day-2025/FieldDay2025OrdersFilters';
import { FieldDay2025TableSkeleton } from '@/components/admin/field-day-2025/FieldDay2025OrdersSkeleton';
import {
  FieldDay2025OrdersErrorFallback,
  FieldDay2025RefreshButton,
} from '@/components/admin/field-day-2025/FieldDay2025ErrorFallbacks';
import { PPRErrorBoundary } from '@/components/ui/ppr-error-boundary';
import Link from 'next/link';

// Admin authentication check
async function AdminAuthCheck() {
  const { data: user } = await authServer.getCurrentUser();

  if (!user) {
    redirect('/login');
  }

  if (user.role !== 'admin') {
    redirect('/dashboard');
  }

  return null;
}

// Page header component
function PageHeader() {
  return (
    <div className='flex flex-col gap-4 md:flex-row md:items-center md:justify-between'>
      <div className='flex items-center gap-4'>
        <Button variant='ghost' size='sm' asChild>
          <Link href='/admin/field-day-2025'>
            <ArrowLeft className='mr-2 h-4 w-4' />
            Back to Overview
          </Link>
        </Button>

        <div>
          <h1 className='text-2xl md:text-3xl font-bold text-gray-900'>
            Field Day 2025 Orders
          </h1>
          <p className='text-gray-600 mt-1'>
            Detailed order management and tracking
          </p>
        </div>
      </div>

      <div className='flex flex-col gap-2 sm:flex-row'>
        <FieldDay2025RefreshButton />

        <Button variant='outline' size='sm' asChild>
          <Link href='/admin/field-day-2025/export'>
            <Download className='mr-2 h-4 w-4' />
            Export
          </Link>
        </Button>

        <Button size='sm' asChild>
          <Link href='/field-day-2025'>
            <Plus className='mr-2 h-4 w-4' />
            New Order
          </Link>
        </Button>
      </div>
    </div>
  );
}

// Quick stats component
function QuickStats() {
  return (
    <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
      <Card>
        <CardContent className='p-4'>
          <div className='flex items-center justify-between'>
            <div>
              <p className='text-sm font-medium text-gray-600'>Total Orders</p>
              <p className='text-2xl font-bold'>0</p>
            </div>
            <Package className='h-8 w-8 text-gray-400' />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className='p-4'>
          <div className='flex items-center justify-between'>
            <div>
              <p className='text-sm font-medium text-gray-600'>Pending</p>
              <p className='text-2xl font-bold text-yellow-600'>0</p>
            </div>
            <Badge
              variant='secondary'
              className='bg-yellow-100 text-yellow-800'
            >
              Pending
            </Badge>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className='p-4'>
          <div className='flex items-center justify-between'>
            <div>
              <p className='text-sm font-medium text-gray-600'>Confirmed</p>
              <p className='text-2xl font-bold text-blue-600'>0</p>
            </div>
            <Badge variant='secondary' className='bg-blue-100 text-blue-800'>
              Confirmed
            </Badge>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className='p-4'>
          <div className='flex items-center justify-between'>
            <div>
              <p className='text-sm font-medium text-gray-600'>Ready</p>
              <p className='text-2xl font-bold text-green-600'>0</p>
            </div>
            <Badge variant='secondary' className='bg-green-100 text-green-800'>
              Ready
            </Badge>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Filters and search component
function OrdersFiltersSection() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          <Filter className='h-5 w-5' />
          Filters & Search
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Suspense
          fallback={
            <div className='space-y-4'>
              <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
                {[1, 2, 3, 4].map((i) => (
                  <div
                    key={i}
                    className='h-10 bg-gray-200 rounded animate-pulse'
                  ></div>
                ))}
              </div>
            </div>
          }
        >
          <FieldDay2025OrdersFilters />
        </Suspense>
      </CardContent>
    </Card>
  );
}

export default function FieldDay2025OrdersPage() {
  return (
    <>
      <AdminAuthCheck />

      <div className='space-y-6'>
        <PageHeader />

        <QuickStats />

        <OrdersFiltersSection />

        {/* Orders Table */}
        <PPRErrorBoundary fallback={<FieldDay2025OrdersErrorFallback />}>
          <Suspense fallback={<FieldDay2025TableSkeleton />}>
            <FieldDay2025OrdersTable />
          </Suspense>
        </PPRErrorBoundary>
      </div>
    </>
  );
}
