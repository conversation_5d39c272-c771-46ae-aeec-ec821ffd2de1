import { redirect } from 'next/navigation';
import { authServer } from '@/lib/utils/auth-server';
import { SubscribersTable } from '@/components/admin';

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic';

export default async function SubscribersPage() {
  const { data: user } = await authServer.getCurrentUser();

  if (!user) {
    redirect('/login');
  }

  if (user.role !== 'admin') {
    redirect('/dashboard');
  }

  return <SubscribersTable />;
}
