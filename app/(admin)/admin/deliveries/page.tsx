import { DeliveriesTable } from '@/components/admin/deliveries/DeliveriesTable';
import { DeliveryStats } from '@/components/admin/deliveries/DeliveryStats';
import { Metadata } from 'next';
import { redirect } from 'next/navigation';

export const metadata: Metadata = {
  title: 'Deliveries Management | AsedaFoods Admin',
  description: 'Manage delivery schedules and track delivery status',
};

export default async function DeliveriesPage() {
  // Check authentication and admin access using centralized auth system
  const { authServer } = await import('@/lib/utils/auth-server');
  const authResult = await authServer.getCurrentUser();

  if (authResult.error || !authResult.data) {
    redirect('/auth/signin');
  }

  if (authResult.data.role !== 'admin') {
    redirect('/dashboard');
  }

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div>
        <h1 className='text-2xl md:text-3xl font-bold text-gray-900'>
          Deliveries Management
        </h1>
        <p className='text-gray-600 mt-2 text-sm md:text-base'>
          Track and manage delivery schedules, update delivery status, and
          monitor delivery metrics.
        </p>
      </div>

      {/* Delivery Statistics */}
      <DeliveryStats />

      {/* Deliveries Table */}
      <div className='bg-white rounded-lg shadow'>
        <div className='px-4 py-5 sm:p-6'>
          <h2 className='text-lg font-medium text-gray-900 mb-4'>
            All Deliveries
          </h2>
          <DeliveriesTable />
        </div>
      </div>
    </div>
  );
}
