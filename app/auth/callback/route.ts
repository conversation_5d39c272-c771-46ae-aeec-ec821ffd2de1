import { createClient } from '@/lib/supabase/server';
import { NextRequest, NextResponse } from 'next/server';
import { EmailService } from '@/lib/services/email';

export async function GET(request: NextRequest) {
  const { searchParams, origin } = new URL(request.url);
  const code = searchParams.get('code');

  if (code) {
    const supabase = await createClient();

    try {
      const { data, error } = await supabase.auth.exchangeCodeForSession(code);

      if (error) {
        console.error('Error exchanging code for session:', error);
        return NextResponse.redirect(
          `${origin}/auth/error?message=${encodeURIComponent(error.message)}`
        );
      }

      if (data.user) {
        console.log('User verified successfully:', data.user.id);

        // Check if user is admin by looking up their role in subscribers table
        try {
          const { data: subscriber } = await supabase
            .from('subscribers')
            .select('role, name')
            .eq('user_id', data.user.id)
            .single();

          const userRole = subscriber?.role || 'user';
          const userName =
            subscriber?.name || data.user.user_metadata?.name || 'there';
          const redirectPath = userRole === 'admin' ? '/admin' : '/dashboard';

          // Send welcome email for new users (only if this is their first verification)
          try {
            // Check if this is a new user by looking at email_confirmed_at
            if (data.user.email_confirmed_at && data.user.email) {
              await EmailService.sendWelcomeEmail(data.user.email, userName);
              console.log('Welcome email sent to:', data.user.email);
            }
          } catch (emailError) {
            // Don't fail the verification if email sending fails
            console.error('Error sending welcome email:', emailError);
          }

          // Add user to general audience for email marketing
          try {
            if (data.user.email) {
              const audienceResult = await EmailService.addToGeneralAudience(
                data.user.email
              );
              if (audienceResult.success) {
                console.log('User added to general audience successfully:', {
                  email: data.user.email,
                  contactId: audienceResult.data?.contactId,
                });
              } else {
                console.error('Failed to add user to audience:', {
                  email: data.user.email,
                  error: audienceResult.error,
                });
              }
            }
          } catch (audienceError) {
            // Don't fail the verification if audience addition fails
            console.error('Error adding user to audience:', audienceError);
          }

          // Redirect to success page with role-based redirect info
          return NextResponse.redirect(
            `${origin}/auth/verified?email=${encodeURIComponent(data.user.email || '')}&redirect=${encodeURIComponent(redirectPath)}`
          );
        } catch (roleError) {
          console.error('Error fetching user role:', roleError);
          // Fallback to regular dashboard redirect
          return NextResponse.redirect(
            `${origin}/auth/verified?email=${encodeURIComponent(data.user.email || '')}`
          );
        }
      }
    } catch (error) {
      console.error('Unexpected error during code exchange:', error);
      return NextResponse.redirect(
        `${origin}/auth/error?message=${encodeURIComponent('An unexpected error occurred')}`
      );
    }
  }

  // If no code or verification failed, redirect to error page
  return NextResponse.redirect(
    `${origin}/auth/error?message=${encodeURIComponent('Invalid verification link')}`
  );
}
