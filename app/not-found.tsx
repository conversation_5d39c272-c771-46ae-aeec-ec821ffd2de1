import Link from 'next/link';
import { Home, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';

export default function NotFound() {
  return (
    <div className='min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8'>
      <div className='max-w-md w-full space-y-8 text-center'>
        <div className='space-y-6'>
          {/* 404 Illustration */}
          <div className='mx-auto flex items-center justify-center h-32 w-32 rounded-full bg-green-100'>
            <span className='text-6xl font-bold text-green-600'>404</span>
          </div>

          {/* Header */}
          <div className='space-y-2'>
            <h1 className='text-3xl font-bold text-gray-900'>Page Not Found</h1>
            <p className='text-lg text-gray-600'>
              Oops! The page you&apos;re looking for doesn&apos;t exist.
            </p>
          </div>

          {/* Description */}
          <div className='bg-white p-6 rounded-lg border border-gray-200 shadow-sm'>
            <p className='text-gray-700 mb-4'>
              The page you requested might have been moved, deleted, or you
              might have entered an incorrect URL.
            </p>
            <div className='space-y-2 text-sm text-gray-600'>
              <p>• Check the URL for typos</p>
              <p>
                • Use the navigation menu to find what you&apos;re looking for
              </p>
              <p>• Go back to the homepage and start fresh</p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className='space-y-3'>
            <Link href='/' className='block'>
              <Button className='w-full bg-green-600 hover:bg-green-700 text-white'>
                <Home className='mr-2 h-4 w-4' />
                Go to Homepage
              </Button>
            </Link>

            <Link href='/login' className='block'>
              <Button
                variant='outline'
                className='w-full border-green-300 text-green-700 hover:bg-green-50'
              >
                <ArrowLeft className='mr-2 h-4 w-4' />
                Sign In
              </Button>
            </Link>
          </div>

          {/* Quick Links */}
          <div className='bg-green-50 p-4 rounded-lg border border-green-200'>
            <h3 className='text-sm font-medium text-green-900 mb-3'>
              Popular Pages
            </h3>
            <div className='space-y-2'>
              <Link
                href='/#subscriptionplans'
                className='block text-sm text-green-700 hover:text-green-900 hover:underline'
              >
                View Subscription Plans
              </Link>
              <Link
                href='/#aboutus'
                className='block text-sm text-green-700 hover:text-green-900 hover:underline'
              >
                About Aseda Foods
              </Link>
              <Link
                href='/#faqs'
                className='block text-sm text-green-700 hover:text-green-900 hover:underline'
              >
                Frequently Asked Questions
              </Link>
              <Link
                href='/signup'
                className='block text-sm text-green-700 hover:text-green-900 hover:underline'
              >
                Create Account
              </Link>
            </div>
          </div>

          {/* Contact Info */}
          <div className='text-center'>
            <p className='text-sm text-gray-500'>
              Still can&apos;t find what you&apos;re looking for?
            </p>
            <p className='text-sm text-gray-600 mt-1'>
              Contact our support team for assistance.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
