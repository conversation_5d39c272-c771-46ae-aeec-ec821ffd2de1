import LogoutButton from '@/components/auth/LogoutButton';
import { SubscriptionHistoryServer } from '@/components/dashboard/SubscriptionHistoryServer';
import SubscriptionManagement from '@/components/dashboard/SubscriptionManagement';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { PPRErrorBoundary } from '@/components/ui/ppr-error-boundary';
import {
  AccountInfoSkeleton,
  SubscriptionHistorySkeleton,
  SubscriptionStatusSkeleton,
  UserHeaderSkeleton,
} from '@/components/ui/skeletons';
import {
  BOX_SIZES,
  FREQUENCIES,
  PICKUP_LOCATIONS,
} from '@/lib/constants/subscription';
import { getCurrentUserSubscriptionSummary } from '@/lib/services/server/subscription-server';
import { authServer } from '@/lib/utils/auth-server';
import Link from 'next/link';
import { redirect } from 'next/navigation';
import { Suspense } from 'react';

// Enable Partial Prerendering for this page (when Next.js supports it)
// export const experimental_ppr = true;

// Dynamic components for PPR
async function UserHeader() {
  const { data: user } = await authServer.getCurrentUser();

  if (!user) {
    redirect('/login');
  }

  return (
    <div className='flex justify-between items-center'>
      <h1 className='text-2xl font-bold text-gray-900'>
        Welcome, {user.name}!
      </h1>
      <LogoutButton variant='outline' />
    </div>
  );
}

async function AccountInfo() {
  const { data: user } = await authServer.getCurrentUser();

  if (!user) {
    redirect('/login');
  }

  return (
    <div className='text-sm space-y-2'>
      <p>
        <strong>Email:</strong> {user.email}
      </p>
    </div>
  );
}

async function SubscriptionStatus() {
  const { data: user } = await authServer.getCurrentUser();

  if (!user) {
    redirect('/login');
  }

  try {
    const subscriptionSummary = await getCurrentUserSubscriptionSummary();
    const { activeSubscription, pausedSubscriptions } = subscriptionSummary;

    return (
      <SubscriptionStatusContent
        activeSubscription={activeSubscription}
        pausedSubscriptions={pausedSubscriptions}
      />
    );
  } catch {
    return <NoActiveSubscriptionDisplay />;
  }
}

function SubscriptionStatusContent({
  activeSubscription,
  pausedSubscriptions,
}: {
  activeSubscription: any;
  pausedSubscriptions: any[];
}) {
  if (activeSubscription) {
    return (
      <div className='border rounded-lg p-4 bg-green-50 border-green-200'>
        <div className='flex justify-between items-start mb-3'>
          <div>
            <h3 className='font-semibold text-lg text-green-800'>
              Active Subscription
            </h3>
            <p className='text-sm text-green-700'>
              {BOX_SIZES[activeSubscription.box_size as keyof typeof BOX_SIZES]
                ?.name || 'Fresh Produce Share'}{' '}
              •{' '}
              {FREQUENCIES[
                activeSubscription.frequency as keyof typeof FREQUENCIES
              ]?.name || activeSubscription.frequency}{' '}
              •{' '}
              {PICKUP_LOCATIONS[
                activeSubscription.pickup_location as keyof typeof PICKUP_LOCATIONS
              ]?.name || activeSubscription.pickup_location}
            </p>
          </div>
          <Badge className='bg-green-100 text-green-800'>Active</Badge>
        </div>

        <div className='grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-4'>
          <div>
            <span className='text-green-600'>Pickups Remaining:</span>
            <p className='font-medium text-green-800'>
              {activeSubscription.deliveries_remaining ?? 'N/A'}
            </p>
          </div>
          <div>
            <span className='text-green-600'>Next Pickup:</span>
            <p className='font-medium text-green-800'>
              {activeSubscription.next_delivery_date
                ? new Date(
                    activeSubscription.next_delivery_date
                  ).toLocaleDateString()
                : 'N/A'}
            </p>
          </div>
          <div>
            <span className='text-green-600'>Auto-Renew:</span>
            <p className='font-medium text-green-800'>
              {activeSubscription.auto_renew ? 'Yes' : 'No'}
            </p>
          </div>
          <div>
            <span className='text-green-600'>Discount:</span>
            <p className='font-medium text-green-800'>
              {activeSubscription.discount_percentage || 0}%
            </p>
          </div>
        </div>

        {activeSubscription && (
          <SubscriptionManagement subscription={activeSubscription} />
        )}
      </div>
    );
  }

  if (pausedSubscriptions.length > 0) {
    return (
      <div className='space-y-4'>
        <div className='text-center py-4'>
          <h3 className='font-semibold text-lg text-yellow-800 mb-2'>
            No Active Subscription
          </h3>
          <p className='text-yellow-700 mb-2'>
            You have paused subscriptions that can be resumed.
          </p>
          <div className='bg-yellow-100 border border-yellow-300 rounded-lg p-3 mt-4'>
            <p className='text-yellow-800 text-sm'>
              <strong>Note:</strong> You cannot start a new subscription while
              you have paused subscriptions. You must either resume or cancel
              your paused subscription first.
            </p>
          </div>
        </div>
        {pausedSubscriptions.map((subscription: any) => (
          <div
            key={subscription.id}
            className='border rounded-lg p-4 bg-yellow-50 border-yellow-200'
          >
            <div className='flex justify-between items-start mb-3'>
              <div>
                <h3 className='font-semibold text-lg text-yellow-800'>
                  Paused Subscription
                </h3>
                <p className='text-sm text-yellow-700'>
                  {BOX_SIZES[subscription.box_size as keyof typeof BOX_SIZES]
                    ?.name || subscription.box_size}{' '}
                  •{' '}
                  {FREQUENCIES[
                    subscription.frequency as keyof typeof FREQUENCIES
                  ]?.name || subscription.frequency}{' '}
                  •{' '}
                  {PICKUP_LOCATIONS[
                    subscription.pickup_location as keyof typeof PICKUP_LOCATIONS
                  ]?.name || subscription.pickup_location}
                </p>
              </div>
              <Badge className='bg-yellow-100 text-yellow-800'>Paused</Badge>
            </div>

            <div className='grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-4'>
              <div>
                <span className='text-yellow-600'>Pickups Remaining:</span>
                <p className='font-medium text-yellow-800'>
                  {subscription.deliveries_remaining ?? 'N/A'}
                </p>
              </div>
              <div>
                <span className='text-yellow-600'>Auto-Renew:</span>
                <p className='font-medium text-yellow-800'>
                  {subscription.auto_renew ? 'Yes' : 'No'}
                </p>
              </div>
              <div>
                <span className='text-yellow-600'>Discount:</span>
                <p className='font-medium text-yellow-800'>
                  {subscription.discount_percentage || 0}%
                </p>
              </div>
            </div>

            {subscription && (
              <SubscriptionManagement subscription={subscription} />
            )}
          </div>
        ))}
      </div>
    );
  }

  return <NoActiveSubscriptionDisplay />;
}

function NoActiveSubscriptionDisplay() {
  return (
    <div className='text-center py-8'>
      <h3 className='font-semibold text-lg text-gray-800 mb-2'>
        No Active Subscription
      </h3>
      <p className='text-gray-500 mb-4'>
        You don&apos;t have any active subscriptions yet.
      </p>
      <Link href='/get-a-box'>
        <Button>Get Your First Box</Button>
      </Link>
    </div>
  );
}

export default function DashboardPage() {
  return (
    <div className='min-h-screen bg-gray-50'>
      <div className='max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8'>
        <div className='space-y-6'>
          {/* Static header structure with dynamic content */}
          <div className='bg-white shadow-sm rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <PPRErrorBoundary>
                <Suspense fallback={<UserHeaderSkeleton />}>
                  <UserHeader />
                </Suspense>
              </PPRErrorBoundary>
            </div>
          </div>

          {/* Static account info structure */}
          <Card>
            <CardHeader>
              <CardTitle>Your Account</CardTitle>
            </CardHeader>
            <CardContent>
              <PPRErrorBoundary>
                <Suspense fallback={<AccountInfoSkeleton />}>
                  <AccountInfo />
                </Suspense>
              </PPRErrorBoundary>
            </CardContent>
          </Card>

          {/* Static subscription status structure */}
          <Card>
            <CardHeader>
              <CardTitle>Subscription Status</CardTitle>
            </CardHeader>
            <CardContent>
              <PPRErrorBoundary>
                <Suspense fallback={<SubscriptionStatusSkeleton />}>
                  <SubscriptionStatus />
                </Suspense>
              </PPRErrorBoundary>
            </CardContent>
          </Card>

          {/* Dynamic subscription history */}
          <PPRErrorBoundary>
            <Suspense fallback={<SubscriptionHistorySkeleton />}>
              <SubscriptionHistoryServer />
            </Suspense>
          </PPRErrorBoundary>

          {/* Static actions section */}
          <AdminLinkSection />
        </div>
      </div>
    </div>
  );
}

async function AdminLinkSection() {
  const { data: user } = await authServer.getCurrentUser();

  if (!user || user.role !== 'admin') {
    return null;
  }

  return (
    <div className='flex space-x-4'>
      <Link href='/admin'>
        <Button variant='outline'>Admin Dashboard</Button>
      </Link>
    </div>
  );
}
