import Link from 'next/link';
import { SignUpForm } from '@/components/forms/auth';

export default function SignUpPage() {
  return (
    <div className='min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8'>
      <div className='max-w-md w-full space-y-8'>
        <div>
          <h2 className='mt-6 text-center text-3xl font-extrabold text-gray-900'>
            Create your account
          </h2>
          <p className='mt-2 text-center text-sm text-gray-600'>
            Or{' '}
            <Link
              href='/login'
              className='font-medium text-green-600 hover:text-green-500'
            >
              sign in to your existing account
            </Link>
          </p>
        </div>

        <SignUpForm className='mt-8' />

        <div className='text-center'>
          <Link href='/' className='text-sm text-gray-600 hover:text-gray-500'>
            ← Back to home
          </Link>
        </div>
      </div>
    </div>
  );
}
