'use client';

import { buttonVariants } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import { useAuth } from '@/components/auth/AuthProvider';

// interface NavbarAuthClientProps {
//   pathname: string;
// }

/**
 * Client component for navbar authentication state
 * This component uses client-side auth context and renders appropriate buttons
 */
// export function NavbarAuthClient({ pathname }: NavbarAuthClientProps) {
export function NavbarAuthClient() {
  const { user, loading } = useAuth();

  // Show loading state
  if (loading) {
    return (
      <div className="flex items-center space-x-4">
        <div className="h-9 w-24 bg-gray-200 rounded animate-pulse"></div>
        <div className="h-9 w-16 bg-gray-200 rounded animate-pulse hidden md:block"></div>
      </div>
    );
  }

  // Determine dashboard URL based on user role
  const getDashboardUrl = () => {
    if (user?.role === 'admin') {
      return '/admin';
    }
    return '/dashboard';
  };

  if (user) {
    // Authenticated user - show dashboard button
    return (
      <Link
        href={getDashboardUrl()}
        className={cn(buttonVariants({ variant: 'default' }), '')}
      >
        {user.role === 'admin' ? 'Admin Dashboard' : 'Dashboard'}
      </Link>
    );
  }

  // Unauthenticated user - show subscription and login buttons
  // Hide Start Subscription button on /field-day-2025 page
  // const isFieldDayPage = pathname === '/field-day-2025';
  
  return (
    <>
      {/* Start Subscription button - hidden on field-day-2025 page */}
      {/* {!isFieldDayPage && (
        <Link
          href={'/get-a-box'}
          className={cn(buttonVariants({ variant: 'default' }), '')}
        >
          Start a Subscription
        </Link>
      )} */}
      {/* Login button - always visible on desktop */}
      <Link
        href={'/login'}
        className={cn(
          buttonVariants({ variant: 'secondary' }),
          'hidden md:inline-flex'
        )}
      >
        Login
      </Link>
    </>
  );
}
