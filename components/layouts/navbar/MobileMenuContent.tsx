'use client';

import { useAuth } from '@/components/auth/AuthProvider';
import { buttonVariants } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import { useMobileMenu } from './MobileMenuContext';

const navLink = [
  { id: 1, title: 'About Us', url: '#aboutus' },
  { id: 3, title: 'How It works', url: '#howitworks' },
  { id: 4, title: 'Plans', url: '#subscriptionplans' },
  { id: 5, title: 'Seasonal Produce', url: '#freshselection' },
  { id: 6, title: 'FAQs', url: '#faqs' },
];

interface MobileMenuContentProps {
  pathname: string;
}

export function MobileMenuContent({ pathname }: MobileMenuContentProps) {
  const { isOpen, close } = useMobileMenu();
  const { user, loading } = useAuth();

  // Determine dashboard URL based on user role
  const getDashboardUrl = () => {
    if (user?.role === 'admin') {
      return '/admin';
    }
    return '/dashboard';
  };

  if (!isOpen) return null;

  return (
    <div className='md:hidden flex flex-col space-y-4 p-4 border-b border-green-50 bg-white'>
      {navLink.map((link) => (
        <Link
          key={link.id}
          href={link.url}
          className='font-medium text-neutral-500 hover:text-green-600 hover:underline'
          onClick={close}
        >
          {link.title}
        </Link>
      ))}

      {/* Mobile authentication buttons */}
      <div className='pt-4 border-t border-green-100'>
        {loading ? (
          // Loading state
          <div className='h-10 w-full bg-gray-200 rounded animate-pulse'></div>
        ) : user ? (
          // Authenticated user - show dashboard button
          <Link
            href={getDashboardUrl()}
            className={cn(
              buttonVariants({ variant: 'default' }),
              'w-full justify-center'
            )}
            onClick={close}
          >
            {user.role === 'admin' ? 'Admin Dashboard' : 'Dashboard'}
          </Link>
        ) : (
          // Unauthenticated user - show subscription button only on mobile
          // Hide Start Subscription button on /field-day-2025 page

          pathname !== '/field-day-2025' && (
            <Link
              href={'/get-a-box'}
              className={cn(
                buttonVariants({ variant: 'default' }),
                'w-full justify-center'
              )}
              onClick={close}
            >
              Start a Subscription
            </Link>
          )
        )}
      </div>
      <Link
        href={'/login'}
        className={cn(
          buttonVariants({ variant: 'secondary' }),
          ''
        )}
      >
        Login
      </Link>
    </div>
  );
}
