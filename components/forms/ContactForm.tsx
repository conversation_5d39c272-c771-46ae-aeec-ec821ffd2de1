'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  submitContactFormAction,
  type ContactFormData,
} from '@/lib/actions/contact';
import { toast } from 'sonner';

// Validation schema
const contactFormSchema = z.object({
  name: z
    .string()
    .min(1, 'Name is required')
    .max(100, 'Name must be less than 100 characters'),
  email: z.string().email('Please enter a valid email address'),
  message: z
    .string()
    .min(10, 'Message must be at least 10 characters')
    .max(2000, 'Message must be less than 2000 characters'),
});

interface ContactFormProps {
  className?: string;
}

export default function ContactForm({ className }: ContactFormProps) {
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<ContactFormData>({
    resolver: zodResolver(contactFormSchema),
    defaultValues: {
      name: '',
      email: '',
      message: '',
    },
  });

  const onSubmit = async (data: ContactFormData) => {
    setIsLoading(true);

    try {
      const result = await submitContactFormAction(data);

      if (result.success) {
        toast.success(result.message);
        form.reset(); // Clear the form on success
      } else {
        toast.error(result.error);
      }
    } catch (error) {
      console.error('Contact form submission error:', error);
      toast.error('An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form
      onSubmit={form.handleSubmit(onSubmit)}
      className={`space-y-6 ${className}`}
    >
      <div className='space-y-2'>
        <Label htmlFor='name'>Name *</Label>
        <Input
          id='name'
          type='text'
          placeholder='Your full name'
          {...form.register('name')}
          disabled={isLoading}
          className={form.formState.errors.name ? 'border-red-500' : ''}
        />
        {form.formState.errors.name && (
          <p className='text-red-500 text-sm'>
            {form.formState.errors.name.message}
          </p>
        )}
      </div>

      <div className='space-y-2'>
        <Label htmlFor='email'>Email *</Label>
        <Input
          id='email'
          type='email'
          placeholder='<EMAIL>'
          {...form.register('email')}
          disabled={isLoading}
          className={form.formState.errors.email ? 'border-red-500' : ''}
        />
        {form.formState.errors.email && (
          <p className='text-red-500 text-sm'>
            {form.formState.errors.email.message}
          </p>
        )}
      </div>

      <div className='space-y-2'>
        <Label htmlFor='message'>Message *</Label>
        <Textarea
          id='message'
          placeholder='Tell us how we can help you...'
          rows={6}
          {...form.register('message')}
          disabled={isLoading}
          className={form.formState.errors.message ? 'border-red-500' : ''}
        />
        {form.formState.errors.message && (
          <p className='text-red-500 text-sm'>
            {form.formState.errors.message.message}
          </p>
        )}
        <p className='text-gray-500 text-sm'>
          {form.watch('message')?.length || 0}/2000 characters
        </p>
      </div>

      <Button
        type='submit'
        disabled={isLoading}
        className='w-full bg-green-600 hover:bg-green-700 text-white'
      >
        {isLoading ? 'Sending...' : 'Send Message'}
      </Button>

      <p className='text-gray-500 text-sm text-center'>
        We&apos;ll get back to you within 24 hours during business days.
      </p>
    </form>
  );
}
