'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import { subscribeToNewsletterAction } from '@/lib/actions/newsletter';
import { toast } from 'sonner';

// Validation schema
const newsletterSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
});

type NewsletterFormData = z.infer<typeof newsletterSchema>;

interface NewsletterSubscriptionProps {
  className?: string;
  inputClassName?: string;
  buttonClassName?: string;
  placeholder?: string;
  buttonText?: string;
}

export default function NewsletterSubscription({
  className,
  inputClassName,
  buttonClassName,
  placeholder = 'Enter your email',
  buttonText = 'Subscribe',
}: NewsletterSubscriptionProps) {
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<NewsletterFormData>({
    resolver: zodResolver(newsletterSchema),
    defaultValues: {
      email: '',
    },
  });

  const onSubmit = async (data: NewsletterFormData) => {
    setIsLoading(true);

    try {
      const result = await subscribeToNewsletterAction(data.email);

      if (result.success) {
        toast.success(result.message);
        form.reset(); // Clear the form on success
      } else {
        toast.error(result.error);
      }
    } catch (error) {
      console.error('Newsletter subscription error:', error);
      toast.error('An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form 
      onSubmit={form.handleSubmit(onSubmit)} 
      className={cn('flex flex-col sm:flex-row gap-4 w-full max-w-md', className)}
    >
      <Input
        type="email"
        placeholder={placeholder}
        className={cn(
          'flex-1 bg-white border-white text-gray-900 placeholder:text-gray-500 focus:ring-white focus:border-white h-12',
          inputClassName
        )}
        {...form.register('email')}
        disabled={isLoading}
      />
      <Button
        type="submit"
        disabled={isLoading}
        className={cn(
          'bg-white text-green-600 hover:bg-green-50 h-12 px-8 font-semibold',
          buttonClassName
        )}
      >
        {isLoading ? 'Subscribing...' : buttonText}
      </Button>
      {form.formState.errors.email && (
        <p className="text-red-200 text-sm mt-1">
          {form.formState.errors.email.message}
        </p>
      )}
    </form>
  );
}
