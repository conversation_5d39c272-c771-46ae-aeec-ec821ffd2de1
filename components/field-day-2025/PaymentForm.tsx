'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { loadStripe } from '@stripe/stripe-js';
import {
  Elements,
  PaymentElement,
  useStripe,
  useElements,
} from '@stripe/react-stripe-js';
import { stripeConfig } from '@/lib/stripe/client-config';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import {
  CreditCard,
  Lock,
  AlertCircle,
  CheckCircle,
  ArrowLeft,
} from 'lucide-react';
import { formatCurrency } from '@/lib/field-day-2025-config';

// Initialize Stripe
const stripePromise = loadStripe(stripeConfig.publicKey);

interface PaymentFormProps {
  clientSecret: string;
  orderData: {
    orderNumber: string;
    quantity: number;
    totalAmount: number;
    customerName: string;
    customerEmail: string;
  };
  onSuccess?: (orderNumber: string) => void;
  onError?: (error: string) => void;
}

// Payment form component that uses Stripe Elements
function PaymentFormContent({
  orderData,
  onSuccess,
  onError,
}: Omit<PaymentFormProps, 'clientSecret'>) {
  const stripe = useStripe();
  const elements = useElements();
  const router = useRouter();

  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentError, setPaymentError] = useState<string | null>(null);
  const [paymentSuccess, setPaymentSuccess] = useState(false);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      setPaymentError(
        'Payment system not loaded. Please refresh and try again.'
      );
      return;
    }

    setIsProcessing(true);
    setPaymentError(null);

    try {
      // Confirm payment with Stripe
      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/field-day-2025/order-success?orderNumber=${orderData.orderNumber}`,
        },
        redirect: 'if_required',
      });

      if (error) {
        console.error('Payment confirmation error:', error);
        setPaymentError(error.message || 'Payment failed. Please try again.');
        onError?.(error.message || 'Payment failed');
      } else if (paymentIntent && paymentIntent.status === 'succeeded') {
        console.log('Payment succeeded:', paymentIntent.id);
        setPaymentSuccess(true);

        // Get order number from payment intent metadata or use fallback
        const orderNumber =
          (paymentIntent as any).metadata?.orderNumber || orderData.orderNumber;

        console.log('🎯 Payment success - Order number details:', {
          fromMetadata: (paymentIntent as any).metadata?.orderNumber,
          fromOrderData: orderData.orderNumber,
          finalOrderNumber: orderNumber,
          paymentIntentId: paymentIntent.id,
        });

        onSuccess?.(orderNumber);

        // Send confirmation emails
        try {
          console.log('📧 Sending order confirmation emails...');
          const emailResponse = await fetch(
            '/api/field-day-2025/send-order-emails',
            {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                paymentIntentId: paymentIntent.id,
                orderNumber: orderNumber,
              }),
            }
          );

          if (emailResponse.ok) {
            const result = await emailResponse.json();
            console.log(
              '✅ Order confirmation emails sent successfully:',
              result
            );
          } else {
            const errorResult = await emailResponse
              .json()
              .catch(() => ({ error: 'Unknown error' }));
            console.error('❌ Failed to send order confirmation emails:', {
              status: emailResponse.status,
              statusText: emailResponse.statusText,
              error: errorResult,
            });

            // Handle different error types
            if (emailResponse.status === 404) {
              console.log(
                '⚠️ Email API not available - emails will be sent via webhook'
              );
            } else if (emailResponse.status === 400) {
              console.log(
                '⚠️ Payment verification in progress - emails will be sent via webhook'
              );
            } else {
              console.log(
                '⚠️ Email sending failed - emails will be sent via webhook'
              );
            }
          }
        } catch (emailError) {
          console.error(
            '❌ Error sending order confirmation emails:',
            emailError
          );
          console.log(
            '⚠️ Email sending failed - emails will be sent via webhook'
          );
          // Don't fail the payment flow for email errors
        }

        // Redirect to success page with order number
        setTimeout(() => {
          if (orderNumber && orderNumber.trim() !== '') {
            console.log('✅ Redirecting with order number:', orderNumber);
            router.push(
              `/field-day-2025/order-success?orderNumber=${orderNumber}`
            );
          } else {
            console.log('⚠️ No order number available, redirecting without it');
            // Fallback: redirect without order number and let success page handle it
            router.push('/field-day-2025/order-success');
          }
        }, 2000);
      }
    } catch (error) {
      console.error('Payment processing error:', error);
      setPaymentError('An unexpected error occurred. Please try again.');
      onError?.('Payment processing failed');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleGoBack = () => {
    router.back();
  };

  if (paymentSuccess) {
    return (
      <Card className='border-green-200 bg-green-50'>
        <CardContent className='p-6 text-center'>
          <CheckCircle className='h-12 w-12 text-green-600 mx-auto mb-4' />
          <h3 className='text-lg font-semibold text-green-800 mb-2'>
            Payment Successful! 🎉
          </h3>
          <p className='text-green-700 mb-4'>
            Your Field Day 2025 order has been confirmed. You&apos;ll receive an
            email confirmation shortly.
          </p>
          <p className='text-sm text-green-600'>
            Redirecting to order confirmation...
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <form onSubmit={handleSubmit} className='space-y-6'>
      {/* Order Summary */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <CreditCard className='h-5 w-5' />
            Order Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='space-y-3'>
            <div className='flex justify-between'>
              <span className='text-gray-600'>Order Number:</span>
              <span className='font-mono text-sm'>{orderData.orderNumber}</span>
            </div>
            <div className='flex justify-between'>
              <span className='text-gray-600'>Customer:</span>
              <span>{orderData.customerName}</span>
            </div>
            <div className='flex justify-between'>
              <span className='text-gray-600'>Email:</span>
              <span>{orderData.customerEmail}</span>
            </div>
            <div className='flex justify-between'>
              <span className='text-gray-600'>Quantity:</span>
              <span>
                {orderData.quantity} box{orderData.quantity > 1 ? 'es' : ''}
              </span>
            </div>
            <Separator />
            <div className='flex justify-between font-semibold text-lg'>
              <span>Total:</span>
              <span className='text-green-600'>
                {formatCurrency(orderData.totalAmount)}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payment Element */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Lock className='h-5 w-5' />
            Payment Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='space-y-4'>
            <PaymentElement
              options={{
                layout: 'tabs',
                paymentMethodOrder: ['card', 'apple_pay', 'google_pay'],
              }}
            />

            {paymentError && (
              <Alert variant='destructive'>
                <AlertCircle className='h-4 w-4' />
                <AlertDescription>{paymentError}</AlertDescription>
              </Alert>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Security Notice */}
      <div className='bg-gray-50 rounded-lg p-4'>
        <div className='flex items-start gap-3'>
          <Lock className='h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0' />
          <div>
            <h4 className='font-medium text-gray-900 mb-1'>Secure Payment</h4>
            <p className='text-sm text-gray-600'>
              Your payment information is encrypted and secure. We use Stripe to
              process payments safely.
            </p>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className='flex flex-col sm:flex-row gap-3'>
        <Button
          type='button'
          variant='outline'
          onClick={handleGoBack}
          disabled={isProcessing}
          className='flex-1'
        >
          <ArrowLeft className='mr-2 h-4 w-4' />
          Back to Order
        </Button>

        <Button
          type='submit'
          disabled={!stripe || !elements || isProcessing}
          className='flex-1 bg-green-600 hover:bg-green-700'
        >
          {isProcessing ? (
            <>
              <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2'></div>
              Processing Payment...
            </>
          ) : (
            <>
              <Lock className='mr-2 h-4 w-4' />
              Pay {formatCurrency(orderData.totalAmount)}
            </>
          )}
        </Button>
      </div>

      <p className='text-xs text-gray-500 text-center'>
        By completing this payment, you agree to our terms of service and
        confirm that you will pick up your order at the specified location
        during Field Day 2025.
      </p>
    </form>
  );
}

// Main payment form wrapper with Stripe Elements provider
export function PaymentForm({ clientSecret, ...props }: PaymentFormProps) {
  const [stripeError, setStripeError] = useState<string | null>(null);

  useEffect(() => {
    try {
      // Test if we can access the Stripe config
      const publicKey = stripeConfig.publicKey;
      if (!publicKey) {
        setStripeError(
          'Payment system configuration error. Please contact support.'
        );
      }
    } catch {
      setStripeError(
        'Payment system configuration error. Please contact support.'
      );
    }
  }, []);

  if (stripeError) {
    return (
      <Alert variant='destructive'>
        <AlertCircle className='h-4 w-4' />
        <AlertDescription>{stripeError}</AlertDescription>
      </Alert>
    );
  }

  if (!clientSecret) {
    return (
      <Alert variant='destructive'>
        <AlertCircle className='h-4 w-4' />
        <AlertDescription>
          Payment session not found. Please try creating your order again.
        </AlertDescription>
      </Alert>
    );
  }

  const options = {
    clientSecret,
    appearance: {
      theme: 'stripe' as const,
      variables: {
        colorPrimary: '#059669',
        colorBackground: '#ffffff',
        colorText: '#1f2937',
        colorDanger: '#dc2626',
        fontFamily: 'system-ui, sans-serif',
        spacingUnit: '4px',
        borderRadius: '6px',
      },
    },
  };

  return (
    <Elements stripe={stripePromise} options={options}>
      <PaymentFormContent {...props} />
    </Elements>
  );
}
