'use client';

import { useState } from 'react';
import Image from 'next/image';
import { cn } from '@/lib/utils/cn';
import { ProductImage } from '@/lib/field-day-2025-config';
import { ChevronLeft, ChevronRight, ZoomIn } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogTrigger,
  DialogTitle,
} from '@/components/ui/dialog';
import { VisuallyHidden } from '@/components/ui/visually-hidden';

interface ProductImageGalleryProps {
  images: ProductImage[];
  selectedIndex: number;
  onImageSelect: (index: number) => void;
}

export function ProductImageGallery({
  images,
  selectedIndex,
  onImageSelect,
}: ProductImageGalleryProps) {
  const [isZoomOpen, setIsZoomOpen] = useState(false);

  const handlePrevious = () => {
    const newIndex = selectedIndex > 0 ? selectedIndex - 1 : images.length - 1;
    onImageSelect(newIndex);
  };

  const handleNext = () => {
    const newIndex = selectedIndex < images.length - 1 ? selectedIndex + 1 : 0;
    onImageSelect(newIndex);
  };

  const selectedImage = images[selectedIndex];

  if (!selectedImage) {
    return (
      <div className='aspect-square bg-gray-200 rounded-xl flex items-center justify-center'>
        <span className='text-gray-400'>No image available</span>
      </div>
    );
  }

  return (
    <div className='space-y-4'>
      {/* Main Image Display */}
      <div className='relative aspect-square rounded-xl overflow-hidden bg-gray-100 group'>
        <Image
          src={selectedImage.url}
          alt={selectedImage.alt}
          fill
          className='object-cover transition-transform duration-300 group-hover:scale-105'
          sizes='(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw'
          priority={selectedIndex === 0}
        />

        {/* Navigation Arrows */}
        {images.length > 1 && (
          <>
            <Button
              variant='ghost'
              size='icon'
              className='absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white/90 shadow-lg opacity-0 group-hover:opacity-100 transition-opacity'
              onClick={handlePrevious}
              aria-label='Previous image'
            >
              <ChevronLeft className='h-4 w-4' />
            </Button>

            <Button
              variant='ghost'
              size='icon'
              className='absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white/90 shadow-lg opacity-0 group-hover:opacity-100 transition-opacity'
              onClick={handleNext}
              aria-label='Next image'
            >
              <ChevronRight className='h-4 w-4' />
            </Button>
          </>
        )}

        {/* Zoom Button */}
        <Dialog open={isZoomOpen} onOpenChange={setIsZoomOpen}>
          <DialogTrigger asChild>
            <Button
              variant='ghost'
              size='icon'
              className='absolute top-2 right-2 bg-white/80 hover:bg-white/90 shadow-lg opacity-0 group-hover:opacity-100 transition-opacity'
              aria-label='Zoom image'
            >
              <ZoomIn className='h-4 w-4' />
            </Button>
          </DialogTrigger>
          <DialogContent className='max-w-4xl w-full p-0'>
            <VisuallyHidden>
              <DialogTitle>Product Image Zoom View</DialogTitle>
            </VisuallyHidden>
            <div className='relative aspect-square w-full'>
              <Image
                src={selectedImage.url}
                alt={selectedImage.alt}
                fill
                className='object-contain'
                sizes='90vw'
              />
            </div>
          </DialogContent>
        </Dialog>

        {/* Image Counter */}
        {images.length > 1 && (
          <div className='absolute bottom-2 left-2 bg-black/60 text-white px-2 py-1 rounded text-sm'>
            {selectedIndex + 1} / {images.length}
          </div>
        )}
      </div>

      {/* Thumbnail Images */}
      {images.length > 1 && (
        <div className='grid grid-cols-3 gap-3'>
          {images.map((image, index) => (
            <button
              key={index}
              onClick={() => onImageSelect(index)}
              className={cn(
                'relative aspect-square rounded-lg overflow-hidden border-2 transition-all duration-200',
                selectedIndex === index
                  ? 'border-green-500 ring-2 ring-green-200'
                  : 'border-gray-200 hover:border-gray-300'
              )}
              aria-label={`View ${image.alt}`}
            >
              <Image
                src={image.url}
                alt={image.alt}
                fill
                className='object-cover'
                sizes='(max-width: 768px) 33vw, (max-width: 1200px) 16vw, 12vw'
              />

              {/* Overlay for non-selected images */}
              {selectedIndex !== index && (
                <div className='absolute inset-0 bg-white/20 hover:bg-white/10 transition-colors' />
              )}
            </button>
          ))}
        </div>
      )}

      {/* Image Information */}
      <div className='text-center'>
        <p className='text-sm text-gray-600'>{selectedImage.alt}</p>
        {selectedImage.isPrimary && (
          <span className='inline-block mt-1 text-xs bg-green-100 text-green-800 px-2 py-1 rounded'>
            Main Image
          </span>
        )}
      </div>
    </div>
  );
}

// Keyboard navigation hook for accessibility
export function useImageGalleryKeyboard(
  images: ProductImage[],
  selectedIndex: number,
  onImageSelect: (index: number) => void
) {
  const handleKeyDown = (event: KeyboardEvent) => {
    switch (event.key) {
      case 'ArrowLeft':
        event.preventDefault();
        const prevIndex =
          selectedIndex > 0 ? selectedIndex - 1 : images.length - 1;
        onImageSelect(prevIndex);
        break;
      case 'ArrowRight':
        event.preventDefault();
        const nextIndex =
          selectedIndex < images.length - 1 ? selectedIndex + 1 : 0;
        onImageSelect(nextIndex);
        break;
      case 'Home':
        event.preventDefault();
        onImageSelect(0);
        break;
      case 'End':
        event.preventDefault();
        onImageSelect(images.length - 1);
        break;
    }
  };

  return { handleKeyDown };
}
