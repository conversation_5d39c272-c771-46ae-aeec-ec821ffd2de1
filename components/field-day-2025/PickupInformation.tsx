import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { MapPin, Phone, AlertCircle } from 'lucide-react';
import { PickupLocation } from '@/lib/field-day-2025-config';

interface PickupInformationProps {
  locations: PickupLocation[];
}

export function PickupInformation({ locations }: PickupInformationProps) {
  return (
    <section>
      <h2 className='text-2xl font-bold text-center text-gray-900 mb-8'>
        Pickup Information
      </h2>

      <div className='grid gap-6'>
        {locations.map((location) => (
          <Card key={location.id}>
            <CardHeader>
              <div className='flex items-center justify-between'>
                <CardTitle className='flex items-center gap-2'>
                  <MapPin className='h-5 w-5 text-green-600' />
                  {location.name}
                </CardTitle>
                <Badge variant='secondary'>Pickup Location</Badge>
              </div>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='grid md:grid-cols-2 gap-4'>
                <div className='space-y-3'>
                  <div className='flex items-start gap-3'>
                    <MapPin className='h-4 w-4 text-gray-500 mt-1 flex-shrink-0' />
                    <div>
                      <p className='font-medium text-gray-900'>Address</p>
                      <p className='text-gray-600'>{location.address}</p>
                    </div>
                  </div>
                  {location.contactInfo && (
                    <div className='flex items-start gap-3'>
                      <Phone className='h-4 w-4 text-gray-500 mt-1 flex-shrink-0' />
                      <div>
                        <p className='font-medium text-gray-900'>Contact</p>
                        <p className='text-gray-600'>{location.contactInfo}</p>
                      </div>
                    </div>
                  )}
                </div>

                <div className='bg-gray-50 rounded-lg p-4'>
                  <h4 className='font-medium text-gray-900 mb-3'>
                    Pickup Instructions
                  </h4>
                  <ul className='space-y-2 text-sm text-gray-600'>
                    <li className='flex items-start gap-2'>
                      <span className='w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0'></span>
                      <span>
                        Bring your order confirmation email or order number
                      </span>
                    </li>
             
                    <li className='flex items-start gap-2'>
                      <span className='w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0'></span>
                      <span>
                        Please arrive during the specified pickup hours
                      </span>
                    </li>
                    <li className='flex items-start gap-2'>
                      <span className='w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0'></span>
                      <span>Boxes must be picked up on the scheduled date</span>
                    </li>
                  </ul>
                </div>
              </div>

              {/* Map placeholder - could be replaced with actual map integration */}
              <div className='bg-gray-100 rounded-lg h-48 flex items-center justify-center overflow-hidden'>
                <iframe
                  src='https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3099.57588085533!2d-77.0156576!3d39.0249873!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89b7cf5c28f37b11%3A0x37e3629296cc6a59!2sNorth%20Four%20Corners%20Local%20Park!5e0!3m2!1sen!2sgh!4v1749167570192!5m2!1sen!2sgh'
                  width='100%'
                  height='100%'
                  loading='lazy'
                ></iframe>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Important Notice */}
      <Card className='mt-6 border-orange-200 bg-orange-50'>
        <CardContent className='p-6'>
          <div className='flex items-start gap-3'>
            <AlertCircle className='h-5 w-5 text-orange-600 mt-0.5 flex-shrink-0' />
            <div>
              <h3 className='font-semibold text-orange-900 mb-2'>
                Important Pickup Information
              </h3>
              <ul className='space-y-1 text-sm text-orange-800'>
                <li>
                  • Orders must be picked up on the scheduled Field Day 2025
                  event date
                </li>
                <li>
                  • Late pickups may result in produce quality degradation
                </li>
                <li>
                  • Contact us immediately if you cannot pick up your order as
                  scheduled
                </li>
                <li>
                  • Refunds are not available for missed pickups due to the
                  perishable nature of produce
                </li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </section>
  );
}

// Compact version for smaller spaces
export function PickupInformationCompact({
  locations,
}: PickupInformationProps) {
  return (
    <div className='space-y-4'>
      <h3 className='font-semibold text-gray-900'>Pickup Location</h3>
      {locations.map((location) => (
        <div key={location.id} className='bg-gray-50 rounded-lg p-4'>
          <h4 className='font-medium text-gray-900 mb-2'>{location.name}</h4>
          <div className='space-y-1 text-sm text-gray-600'>
            <p className='flex items-center gap-2'>
              <MapPin className='h-3 w-3' />
              {location.address}
            </p>
            {location.contactInfo && (
              <p className='flex items-center gap-2'>
                <Phone className='h-3 w-3' />
                {location.contactInfo}
              </p>
            )}
          </div>
        </div>
      ))}
    </div>
  );
}
