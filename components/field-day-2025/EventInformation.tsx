import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock, MapPin, Users, Star } from 'lucide-react';
import { FIELD_DAY_2025_EVENT } from '@/lib/field-day-2025-config';

interface EventInformationProps {
  event: typeof FIELD_DAY_2025_EVENT;
}

export function EventInformation({ event }: EventInformationProps) {
  return (
    <section>
      <h2 className='text-2xl font-bold text-center text-gray-900 mb-8'>
        About Field Day 2025
      </h2>

      <Card className='bg-gradient-to-br from-green-50 to-green-100 border-green-200'>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <CardTitle className='flex items-center gap-2 text-green-800'>
              <Calendar className='h-5 w-5' />
              {event.name}
            </CardTitle>
            <Badge className='bg-green-600 text-white'>Special Event</Badge>
          </div>
        </CardHeader>
        <CardContent className='space-y-6'>
          <p className='text-lg text-green-800 leading-relaxed'>
            {event.description}
          </p>

          <div className='grid md:grid-cols-2 gap-6'>
            <div className='space-y-4'>
              <h3 className='font-semibold text-green-900'>Event Details</h3>

              <div className='space-y-3'>
                <div className='flex items-center gap-3'>
                  <Calendar className='h-4 w-4 text-green-600' />
                  <div>
                    <p className='font-medium text-green-900'>Date</p>
                    <p className='text-green-700'>{event.date}</p>
                  </div>
                </div>

                <div className='flex items-center gap-3'>
                  <Clock className='h-4 w-4 text-green-600' />
                  <div>
                    <p className='font-medium text-green-900'>Time</p>
                    <p className='text-green-700'>All Day Event</p>
                  </div>
                </div>

                <div className='flex items-center gap-3'>
                  <MapPin className='h-4 w-4 text-green-600' />
                  <div>
                    <p className='font-medium text-green-900'>Location</p>
                    <p className='text-green-700'>Community Center</p>
                  </div>
                </div>

                <div className='flex items-center gap-3'>
                  <Users className='h-4 w-4 text-green-600' />
                  <div>
                    <p className='font-medium text-green-900'>
                      Expected Attendance
                    </p>
                    <p className='text-green-700'>500+ Community Members</p>
                  </div>
                </div>
              </div>
            </div>

            <div className='space-y-4'>
              <h3 className='font-semibold text-green-900'>Event Highlights</h3>

              <ul className='space-y-2'>
                {[
                  'Fresh, locally-sourced produce',
                  'Community activities and games',
                  'Live music and entertainment',
                  'Food vendors and local businesses',
                  'Family-friendly environment',
                ].map((highlight, index) => (
                  <li key={index} className='flex items-start gap-2'>
                    <Star className='h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0' />
                    <span className='text-green-700'>{highlight}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Call to Action */}
          <div className='bg-white rounded-lg p-6 border border-green-200'>
            <h3 className='font-semibold text-green-900 mb-3'>
              Why Order a Produce Box for Field Day 2025?
            </h3>
            <div className='grid md:grid-cols-3 gap-4'>
              <div className='text-center'>
                <div className='w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2'>
                  <span className='text-xl'>🥗</span>
                </div>
                <h4 className='font-medium text-green-900 mb-1'>
                  Fresh & Healthy
                </h4>
                <p className='text-sm text-green-700'>
                  Perfect for sharing healthy snacks during the event
                </p>
              </div>

              <div className='text-center'>
                <div className='w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2'>
                  <span className='text-xl'>👨‍👩‍👧‍👦</span>
                </div>
                <h4 className='font-medium text-green-900 mb-1'>
                  Family Friendly
                </h4>
                <p className='text-sm text-green-700'>
                  Great for families attending the event together
                </p>
              </div>

              <div className='text-center'>
                <div className='w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2'>
                  <span className='text-xl'>🌱</span>
                </div>
                <h4 className='font-medium text-green-900 mb-1'>
                  Support Local
                </h4>
                <p className='text-sm text-green-700'>
                  Support local farmers and sustainable agriculture
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </section>
  );
}

// Compact version for smaller displays
export function EventInformationCompact({ event }: EventInformationProps) {
  return (
    <Card className='bg-green-50 border-green-200'>
      <CardContent className='p-4'>
        <div className='flex items-center gap-2 mb-3'>
          <Calendar className='h-4 w-4 text-green-600' />
          <h3 className='font-semibold text-green-900'>{event.name}</h3>
        </div>

        <div className='grid grid-cols-2 gap-3 text-sm'>
          <div className='flex items-center gap-2'>
            <Calendar className='h-3 w-3 text-green-600' />
            <span className='text-green-700'>{event.date}</span>
          </div>
          <div className='flex items-center gap-2'>
            <Clock className='h-3 w-3 text-green-600' />
            <span className='text-green-700'>All Day Event</span>
          </div>
          <div className='flex items-center gap-2 col-span-2'>
            <MapPin className='h-3 w-3 text-green-600' />
            <span className='text-green-700'>Community Center</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
