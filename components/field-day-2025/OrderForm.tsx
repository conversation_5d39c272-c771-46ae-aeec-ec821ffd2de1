'use client';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import {
  FieldDay2025OrderData,
  FieldDay2025Product,
  PickupLocation,
  calculateOrderTotal,
  fieldDay2025OrderSchema,
  formatCurrency,
} from '@/lib/field-day-2025-config';
import { zodResolver } from '@hookform/resolvers/zod';
import { AlertCircle, Minus, Plus, ShoppingCart } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
// import { fieldDay2025OrderActions } from '@/lib/services/field-day-2025-orders'; // Unused

interface OrderFormProps {
  product: FieldDay2025Product;
  pickupLocations: PickupLocation[];
}

export function OrderForm({ product, pickupLocations }: OrderFormProps) {
  const router = useRouter();
  const [quantity, setQuantity] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  const form = useForm<FieldDay2025OrderData>({
    resolver: zodResolver(fieldDay2025OrderSchema),
    defaultValues: {
      quantity: 1,
      firstName: '',
      lastName: '',
      email: '',
      phoneNumber: '',
    },
  });

  // Calculate pricing
  const pricing = calculateOrderTotal(quantity);

  const handleQuantityChange = (newQuantity: number) => {
    const clampedQuantity = Math.max(
      product.minQuantity,
      Math.min(product.maxQuantity, newQuantity)
    );
    setQuantity(clampedQuantity);
    form.setValue('quantity', clampedQuantity);
  };

  const onSubmit = async (data: FieldDay2025OrderData) => {
    try {
      setIsSubmitting(true);
      setSubmitError(null);

      console.log('Proceeding to payment for Field Day 2025 order:', data);

      // Prepare order data with quantity
      const orderData = {
        ...data,
        quantity,
      };

      // Store order data for payment page
      localStorage.setItem('fieldDay2025OrderData', JSON.stringify(orderData));

      // Redirect to payment page with order data
      const orderDataParam = encodeURIComponent(JSON.stringify(orderData));
      router.push(`/field-day-2025/payment?orderData=${orderDataParam}`);
    } catch (error) {
      console.error('Order submission error:', error);
      setSubmitError(
        error instanceof Error
          ? error.message
          : 'Failed to proceed to payment. Please try again.'
      );
      setIsSubmitting(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          <ShoppingCart className='h-5 w-5' />
          Order Your Field Day 2025 Box
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
          {/* Quantity Selector */}
          <div className='space-y-2'>
            <Label htmlFor='quantity'>
              Quantity{' '}
              <span className='text-sm text-gray-500'>
                (max {product.maxQuantity})
              </span>
            </Label>
            <div className='flex items-center gap-4'>
              <Button
                type='button'
                variant='outline'
                size='icon'
                onClick={() => handleQuantityChange(quantity - 1)}
                disabled={quantity <= product.minQuantity}
                aria-label='Decrease quantity'
              >
                <Minus className='h-4 w-4' />
              </Button>

              <div className='flex items-center gap-2'>
                <Input
                  id='quantity'
                  type='number'
                  value={quantity}
                  onChange={(e) =>
                    handleQuantityChange(parseInt(e.target.value) || 1)
                  }
                  min={product.minQuantity}
                  max={product.maxQuantity}
                  className='w-20 text-center [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none'
                />
              </div>

              <Button
                type='button'
                variant='outline'
                size='icon'
                onClick={() => handleQuantityChange(quantity + 1)}
                disabled={quantity >= product.maxQuantity}
                aria-label='Increase quantity'
              >
                <Plus className='h-4 w-4' />
              </Button>
            </div>
            {form.formState.errors.quantity && (
              <p className='text-sm text-red-600'>
                {form.formState.errors.quantity.message}
              </p>
            )}
          </div>

          <Separator />

          {/* Customer Information */}
          <div className='space-y-4'>
            <h3 className='font-semibold text-gray-900'>Contact Information</h3>

            <div className='grid grid-cols-2 gap-4'>
              <div className='space-y-2'>
                <Label htmlFor='firstName'>First Name *</Label>
                <Input
                  id='firstName'
                  placeholder='John'
                  {...form.register('firstName')}
                  className={
                    form.formState.errors.firstName ? 'border-red-500' : ''
                  }
                />
                {form.formState.errors.firstName && (
                  <p className='text-sm text-red-600'>
                    {form.formState.errors.firstName.message}
                  </p>
                )}
              </div>

              <div className='space-y-2'>
                <Label htmlFor='lastName'>Last Name *</Label>
                <Input
                  id='lastName'
                  placeholder='Doe'
                  {...form.register('lastName')}
                  className={
                    form.formState.errors.lastName ? 'border-red-500' : ''
                  }
                />
                {form.formState.errors.lastName && (
                  <p className='text-sm text-red-600'>
                    {form.formState.errors.lastName.message}
                  </p>
                )}
              </div>
            </div>

            <div className='space-y-2'>
              <Label htmlFor='email'>Email Address *</Label>
              <Input
                id='email'
                type='email'
                placeholder='<EMAIL>'
                {...form.register('email')}
                className={form.formState.errors.email ? 'border-red-500' : ''}
              />
              {form.formState.errors.email && (
                <p className='text-sm text-red-600'>
                  {form.formState.errors.email.message}
                </p>
              )}
            </div>

            <div className='space-y-2'>
              <Label htmlFor='phoneNumber'>Phone Number *</Label>
              <Input
                id='phoneNumber'
                type='tel'
                placeholder='(*************'
                {...form.register('phoneNumber')}
                className={
                  form.formState.errors.phoneNumber ? 'border-red-500' : ''
                }
              />
              {form.formState.errors.phoneNumber && (
                <p className='text-sm text-red-600'>
                  {form.formState.errors.phoneNumber.message}
                </p>
              )}
            </div>
          </div>

          <Separator />

          {/* Pickup Information */}
          <div className='space-y-4'>
            <h3 className='font-semibold text-gray-900'>Pickup Information</h3>
            {pickupLocations.map((location) => (
              <div key={location.id} className='bg-gray-50 rounded-lg p-4'>
                <h4 className='font-medium text-gray-900'>{location.name}</h4>
                <p className='text-sm text-gray-600 mt-1'>{location.address}</p>
                {location.contactInfo && (
                  <p className='text-sm text-gray-600'>
                    {location.contactInfo}
                  </p>
                )}
              </div>
            ))}
          </div>

          <Separator />

          {/* Order Summary */}
          <div className='space-y-3'>
            <h3 className='font-semibold text-gray-900'>Order Summary</h3>

            <div className='space-y-2'>
              <div className='flex justify-between text-sm'>
                <span>
                  {product.name} × {quantity}
                </span>
                <span>{formatCurrency(pricing.subtotalCents)}</span>
              </div>

              {pricing.taxAmountCents > 0 && (
                <div className='flex justify-between text-sm'>
                  <span>Tax</span>
                  <span>{formatCurrency(pricing.taxAmountCents)}</span>
                </div>
              )}

              <Separator />

              <div className='flex justify-between font-semibold'>
                <span>Total</span>
                <span className='text-green-600'>
                  {formatCurrency(pricing.totalAmountCents)}
                </span>
              </div>
            </div>
          </div>

          {/* Error Display */}
          {submitError && (
            <Alert variant='destructive'>
              <AlertCircle className='h-4 w-4' />
              <AlertDescription>{submitError}</AlertDescription>
            </Alert>
          )}

          {/* Submit Button */}
          <Button
            type='submit'
            className='w-full bg-green-600 hover:bg-green-700 text-white py-3'
            size='lg'
            disabled={isSubmitting || !form.formState.isValid}
          >
            {isSubmitting ? (
              <>
                <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2'></div>
                Proceeding to Payment...
              </>
            ) : (
              <>
                <ShoppingCart className='mr-2 h-4 w-4' />
                Continue to Payment - {formatCurrency(pricing.totalAmountCents)}
              </>
            )}
          </Button>

          <p className='text-xs text-gray-500 text-center'>
            By placing this order, you agree to pickup your produce box at the
            specified location. You will receive an email confirmation with
            pickup details.
          </p>
        </form>
      </CardContent>
    </Card>
  );
}
