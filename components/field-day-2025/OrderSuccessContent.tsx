'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  FIELD_DAY_2025_EVENT,
  PICKUP_LOCATIONS,
} from '@/lib/field-day-2025-config';
import {
  ArrowRight,
  Calendar,
  CheckCircle,
  Download,
  Home,
  Mail,
  MapPin,
  Phone,
} from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

export function OrderSuccessContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [orderNumber, setOrderNumber] = useState<string | null>(null);

  useEffect(() => {
    const orderNumberParam = searchParams.get('orderNumber');
    setOrderNumber(orderNumberParam);
  }, [searchParams]);

  const handleGoHome = () => {
    router.push('/');
  };

  const handleViewProducts = () => {
    router.push('/field-day-2025');
  };

  if (!orderNumber) {
    return (
      <div className='max-w-2xl mx-auto text-center'>
        <div className='bg-yellow-50 border border-yellow-200 rounded-lg p-8'>
          <h2 className='text-2xl font-bold text-yellow-800 mb-4'>
            Order Information Missing
          </h2>
          <p className='text-yellow-700 mb-6'>
            We couldn&apos;t find your order information. Please check your
            email for the confirmation details.
          </p>
          <div className='flex justify-center gap-4'>
            <Button onClick={handleGoHome} variant='outline'>
              <Home className='mr-2 h-4 w-4' />
              Go Home
            </Button>
            <Button
              onClick={handleViewProducts}
              className='bg-green-600 hover:bg-green-700'
            >
              <ArrowRight className='mr-2 h-4 w-4' />
              View Products
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='max-w-4xl mx-auto'>
      {/* Success Header */}
      <div className='text-center mb-8'>
        <div className='w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4'>
          <CheckCircle className='h-8 w-8 text-green-600' />
        </div>
        <h1 className='text-3xl font-bold text-gray-900 mb-2'>
          Order Confirmed!
        </h1>
        <p className='text-lg text-gray-600'>
          Thank you for your Field Day 2025 produce box order
        </p>
      </div>

      {/* Order Details Card */}
      <Card className='mb-8'>
        <CardHeader>
          <CardTitle className='flex items-center justify-between'>
            <span>Order Details</span>
            <Badge className='bg-green-600 text-white'>Confirmed</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='grid md:grid-cols-2 gap-6'>
            <div>
              <h3 className='font-semibold text-gray-900 mb-3'>
                Order Information
              </h3>
              <div className='space-y-2 text-sm'>
                <div className='flex justify-between'>
                  <span className='text-gray-600'>Order Number:</span>
                  <span className='font-mono font-medium'>{orderNumber}</span>
                </div>
                <div className='flex justify-between'>
                  <span className='text-gray-600'>Order Date:</span>
                  <span>{new Date().toLocaleDateString()}</span>
                </div>
                <div className='flex justify-between'>
                  <span className='text-gray-600'>Status:</span>
                  <Badge variant='secondary'>Processing</Badge>
                </div>
              </div>
            </div>

            <div>
              <h3 className='font-semibold text-gray-900 mb-3'>
                What&apos;s Next?
              </h3>
              <ul className='space-y-2 text-sm text-gray-600'>
                <li className='flex items-start gap-2'>
                  <span className='w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0'></span>
                  <span>You&apos;ll receive an email confirmation shortly</span>
                </li>
                <li className='flex items-start gap-2'>
                  <span className='w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0'></span>
                  <span>We&apos;ll prepare your fresh produce box</span>
                </li>
                <li className='flex items-start gap-2'>
                  <span className='w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0'></span>
                  <span>Pickup details will be sent before the event</span>
                </li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Event Information */}
      <Card className='mb-8 bg-green-50 border-green-200'>
        <CardHeader>
          <CardTitle className='flex items-center gap-2 text-green-800'>
            <Calendar className='h-5 w-5' />
            {FIELD_DAY_2025_EVENT.name}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='grid md:grid-cols-2 gap-6'>
            <div className='space-y-3'>
              <div className='flex items-center gap-3'>
                <Calendar className='h-4 w-4 text-green-600' />
                <div>
                  <p className='font-medium text-green-900'>Event Date</p>
                  <p className='text-green-700'>{FIELD_DAY_2025_EVENT.date}</p>
                </div>
              </div>

              <div className='flex items-center gap-3'>
                <MapPin className='h-4 w-4 text-green-600' />
                <div>
                  <p className='font-medium text-green-900'>Location</p>
                  <p className='text-green-700'>Community Center</p>
                </div>
              </div>
            </div>

            <div className='bg-white rounded-lg p-4 border border-green-200'>
              <h4 className='font-medium text-green-900 mb-2'>
                Pickup Reminder
              </h4>
              <p className='text-sm text-green-700'>
                Your produce box will be ready for pickup during the Field Day
                2025 event. Please bring your order confirmation.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Pickup Location */}
      <Card className='mb-8'>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <MapPin className='h-5 w-5' />
            Pickup Location
          </CardTitle>
        </CardHeader>
        <CardContent>
          {PICKUP_LOCATIONS.map((location) => (
            <div key={location.id} className='space-y-3'>
              <div>
                <h3 className='font-semibold text-gray-900'>{location.name}</h3>
                <p className='text-gray-600'>{location.address}</p>
              </div>

              <div className='grid md:grid-cols-2 gap-4'>
                {location.contactInfo && (
                  <div className='flex items-center gap-2'>
                    <Phone className='h-4 w-4 text-gray-500' />
                    <span className='text-sm text-gray-600'>
                      {location.contactInfo}
                    </span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Contact Information */}
      <Card className='mb-8'>
        <CardHeader>
          <CardTitle>Need Help?</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='grid md:grid-cols-2 gap-6'>
            <div>
              <h3 className='font-semibold text-gray-900 mb-3'>Contact Us</h3>
              <div className='space-y-2'>
                <div className='flex items-center gap-2'>
                  <Mail className='h-4 w-4 text-gray-500' />
                  <a
                    href='mailto:<EMAIL>'
                    className='text-green-600 hover:text-green-700 underline'
                  >
                    <EMAIL>
                  </a>
                </div>
                <div className='flex items-center gap-2'>
                  <Phone className='h-4 w-4 text-gray-500' />
                  <a
                    href='tel:+1234567890'
                    className='text-green-600 hover:text-green-700 underline'
                  >
                    (*************
                  </a>
                </div>
              </div>
            </div>

            <div>
              <h3 className='font-semibold text-gray-900 mb-3'>
                Common Questions
              </h3>
              <ul className='space-y-1 text-sm text-gray-600'>
                <li>• Can I modify my order? Contact us ASAP</li>
                <li>• What if I can&apos;t pick up? Let us know immediately</li>
                <li>• Forgot your order number? Check your email</li>
                <li>• Need directions? We&apos;ll send detailed pickup info</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className='flex flex-col sm:flex-row gap-4 justify-center'>
        <Button onClick={handleGoHome} variant='outline' size='lg'>
          <Home className='mr-2 h-4 w-4' />
          Return Home
        </Button>

        <Button onClick={() => window.print()} variant='outline' size='lg'>
          <Download className='mr-2 h-4 w-4' />
          Print Confirmation
        </Button>

        <Button
          onClick={handleViewProducts}
          className='bg-green-600 hover:bg-green-700'
          size='lg'
        >
          Order Another Box
          <ArrowRight className='ml-2 h-4 w-4' />
        </Button>
      </div>

      {/* Footer Note */}
      <div className='text-center mt-8 p-4 bg-gray-100 rounded-lg'>
        <p className='text-sm text-gray-600'>
          <strong>Important:</strong> Please save this confirmation page and
          check your email for additional details. If you don&apos;t receive a
          confirmation email within 30 minutes, please contact us.
        </p>
      </div>
    </div>
  );
}
