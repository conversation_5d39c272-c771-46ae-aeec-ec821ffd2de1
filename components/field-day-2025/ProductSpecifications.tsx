import { Check } from 'lucide-react';
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from '@/components/ui/card';

interface ProductSpecificationsProps {
  specifications: string[];
}

export function ProductSpecifications({
  specifications,
}: ProductSpecificationsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className='text-lg'>What&apos;s Included</CardTitle>
      </CardHeader>
      <CardContent>
        <ul className='space-y-3'>
          {specifications.map((spec, index) => (
            <li key={index} className='flex items-start gap-3'>
              <div className='flex-shrink-0 w-5 h-5 bg-green-100 rounded-full flex items-center justify-center mt-0.5'>
                <Check className='w-3 h-3 text-green-600' />
              </div>
              <span className='text-gray-700 leading-relaxed'>{spec}</span>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  );
}

// Alternative compact version for smaller spaces
export function ProductSpecificationsCompact({
  specifications,
}: ProductSpecificationsProps) {
  return (
    <div className='space-y-2'>
      <h3 className='font-semibold text-gray-900'>What&apos;s Included:</h3>
      <ul className='space-y-1'>
        {specifications.map((spec, index) => (
          <li
            key={index}
            className='flex items-center gap-2 text-sm text-gray-600'
          >
            <Check className='w-3 h-3 text-green-600 flex-shrink-0' />
            <span>{spec}</span>
          </li>
        ))}
      </ul>
    </div>
  );
}

// Grid version for better layout in some contexts
export function ProductSpecificationsGrid({
  specifications,
}: ProductSpecificationsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className='text-lg'>Product Features</CardTitle>
      </CardHeader>
      <CardContent>
        <div className='grid grid-cols-1 md:grid-cols-2 gap-3'>
          {specifications.map((spec, index) => (
            <div key={index} className='flex items-start gap-3'>
              <div className='flex-shrink-0 w-5 h-5 bg-green-100 rounded-full flex items-center justify-center mt-0.5'>
                <Check className='w-3 h-3 text-green-600' />
              </div>
              <span className='text-gray-700 text-sm leading-relaxed'>
                {spec}
              </span>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
