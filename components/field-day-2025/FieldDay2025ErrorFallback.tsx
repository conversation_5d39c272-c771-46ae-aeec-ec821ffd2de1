'use client';

interface FieldDay2025ErrorFallbackProps {
  onRetry?: () => void;
}

export function FieldDay2025ErrorFallback({ onRetry }: FieldDay2025ErrorFallbackProps) {
  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    } else {
      window.location.reload();
    }
  };

  return (
    <div className='text-center py-12'>
      <h2 className='text-xl font-semibold text-gray-900 mb-2'>
        Something went wrong
      </h2>
      <p className='text-gray-600 mb-4'>
        We&apos;re having trouble loading the Field Day 2025 order form.
      </p>
      <button
        onClick={handleRetry}
        className='bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors'
      >
        Try Again
      </button>
    </div>
  );
}
