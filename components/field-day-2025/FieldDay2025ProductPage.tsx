'use client';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  FIELD_DAY_2025_EVENT,
  FIELD_DAY_2025_PRODUCT,
  formatCurrency,
  PICKUP_LOCATIONS,
} from '@/lib/field-day-2025-config';
import { useState } from 'react';
import { EventInformation } from './EventInformation';
import { OrderForm } from './OrderForm';
import { PickupInformation } from './PickupInformation';
import { ProductImageGallery } from './ProductImageGallery';
import { ProductSpecifications } from './ProductSpecifications';

export function FieldDay2025ProductPage() {
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  if (!FIELD_DAY_2025_PRODUCT.isAvailable) {
    return (
      <div className='max-w-4xl mx-auto text-center py-16'>
        <div className='bg-yellow-50 border border-yellow-200 rounded-lg p-8'>
          <h2 className='text-2xl font-bold text-yellow-800 mb-4'>
            Currently Unavailable
          </h2>
          <p className='text-yellow-700 mb-6'>
            Field Day 2025 produce boxes are currently not available for
            ordering. Please check back later or contact us for more
            information.
          </p>
          <div className='flex justify-center'>
            <a
              href='mailto:<EMAIL>'
              className='bg-yellow-600 text-white px-6 py-2 rounded-lg hover:bg-yellow-700 transition-colors'
            >
              Contact Us
            </a>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='max-w-6xl mx-auto'>
      <div className='grid lg:grid-cols-2 gap-12'>
        {/* Product Images */}
        <div className='space-y-4'>
          <ProductImageGallery
            images={FIELD_DAY_2025_PRODUCT.images}
            selectedIndex={selectedImageIndex}
            onImageSelect={setSelectedImageIndex}
          />
        </div>

        {/* Product Details and Order Form */}
        <div className='space-y-6'>
          {/* Product Header */}
          <div className='space-y-4'>
            <div className='flex items-center gap-3'>
              <Badge
                variant='secondary'
                className='bg-green-100 text-green-800'
              >
                Special Event
              </Badge>
              <Badge variant='outline'>Pickup Only</Badge>
            </div>

            <h1 className='text-3xl font-bold text-gray-900'>
              {FIELD_DAY_2025_PRODUCT.name}
            </h1>

            <div className='flex items-baseline gap-2'>
              <span className='text-3xl font-bold text-green-600'>
                {formatCurrency(FIELD_DAY_2025_PRODUCT.unitPriceCents)}
              </span>
              <span className='text-gray-500'>per box</span>
            </div>

            <p className='text-lg text-gray-600 leading-relaxed'>
              {FIELD_DAY_2025_PRODUCT.description}
            </p>
          </div>

          {/* Product Specifications */}
          <ProductSpecifications
            specifications={FIELD_DAY_2025_PRODUCT.specifications}
          />

          <Separator />

          {/* Order Form */}
          <OrderForm
            product={FIELD_DAY_2025_PRODUCT}
            pickupLocations={PICKUP_LOCATIONS}
          />
        </div>
      </div>

      {/* Additional Information Sections */}
      <div className='mt-16 space-y-12'>
        {/* Event Information */}
        <EventInformation event={FIELD_DAY_2025_EVENT} />

        {/* Pickup Information */}
        <PickupInformation locations={PICKUP_LOCATIONS} />

        {/* Product Details */}
        <section>
          <h2 className='text-2xl font-bold text-center text-gray-900 mb-8'>
            What&apos;s Inside Your Box
          </h2>
          <div className='grid md:grid-cols-2 gap-8'>
            <Card>
              <CardContent className='p-6'>
                <h3 className='text-lg font-semibold text-gray-900 mb-4'>
                  Fresh Vegetables
                </h3>
                <ul className='space-y-2 text-gray-600'>
                  <li className='flex items-center gap-2'>
                    <span className='w-2 h-2 bg-green-500 rounded-full'></span>
                    Seasonal leafy greens (lettuce, spinach, kale)
                  </li>
                  <li className='flex items-center gap-2'>
                    <span className='w-2 h-2 bg-green-500 rounded-full'></span>
                    Root vegetables (carrots, beets, radishes)
                  </li>
                  <li className='flex items-center gap-2'>
                    <span className='w-2 h-2 bg-green-500 rounded-full'></span>
                    Fresh herbs (basil, cilantro, parsley)
                  </li>
                  <li className='flex items-center gap-2'>
                    <span className='w-2 h-2 bg-green-500 rounded-full'></span>
                    Seasonal specialties (tomatoes, peppers, squash)
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardContent className='p-6'>
                <h3 className='text-lg font-semibold text-gray-900 mb-4'>
                  Fresh Fruits
                </h3>
                <ul className='space-y-2 text-gray-600'>
                  <li className='flex items-center gap-2'>
                    <span className='w-2 h-2 bg-orange-500 rounded-full'></span>
                    Seasonal stone fruits (peaches, plums, apricots)
                  </li>
                  <li className='flex items-center gap-2'>
                    <span className='w-2 h-2 bg-orange-500 rounded-full'></span>
                    Fresh berries (strawberries, blueberries)
                  </li>
                  <li className='flex items-center gap-2'>
                    <span className='w-2 h-2 bg-orange-500 rounded-full'></span>
                    Citrus fruits (oranges, lemons, limes)
                  </li>
                  <li className='flex items-center gap-2'>
                    <span className='w-2 h-2 bg-orange-500 rounded-full'></span>
                    Seasonal apples and pears
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>

          <div className='mt-8 text-center'>
            <p className='text-gray-600 max-w-2xl mx-auto'>
              <strong>Note:</strong> Exact contents may vary based on seasonal
              availability and freshness. We guarantee high-quality items in
              every box, sourced from our trusted local farm partners.
            </p>
          </div>
        </section>

        {/* Sustainability Information */}
        <section className='bg-green-50 rounded-xl p-8'>
          <h2 className='text-2xl font-bold text-center text-gray-900 mb-8'>
            Our Commitment to Sustainability
          </h2>
          <div className='grid md:grid-cols-3 gap-6'>
            <div className='text-center'>
              <div className='w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4'>
                <span className='text-2xl'>🌱</span>
              </div>
              <h3 className='font-semibold text-gray-900 mb-2'>
                Organic Farming
              </h3>
              <p className='text-gray-600 text-sm'>
                All our produce is grown using sustainable, organic farming
                practices
              </p>
            </div>
            <div className='text-center'>
              <div className='w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4'>
                <span className='text-2xl'>🚚</span>
              </div>
              <h3 className='font-semibold text-gray-900 mb-2'>
                Local Sourcing
              </h3>
              <p className='text-gray-600 text-sm'>
                Sourced within 50 miles to reduce carbon footprint and support
                local farmers
              </p>
            </div>
            <div className='text-center'>
              <div className='w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4'>
                <span className='text-2xl'>♻️</span>
              </div>
              <h3 className='font-semibold text-gray-900 mb-2'>
                Eco Packaging
              </h3>
              <p className='text-gray-600 text-sm'>
                Reusable and recyclable packaging materials to minimize waste
              </p>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
