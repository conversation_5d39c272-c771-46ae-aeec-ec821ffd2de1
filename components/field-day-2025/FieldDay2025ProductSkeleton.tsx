import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

export function FieldDay2025ProductSkeleton() {
  return (
    <div className='max-w-6xl mx-auto'>
      <div className='grid lg:grid-cols-2 gap-12'>
        {/* Product Images Skeleton */}
        <div className='space-y-4'>
          {/* Main Image */}
          <div className='aspect-square rounded-xl overflow-hidden bg-gray-200'>
            <Skeleton className='w-full h-full' />
          </div>

          {/* Thumbnail Images */}
          <div className='grid grid-cols-3 gap-4'>
            {[1, 2, 3].map((i) => (
              <div
                key={i}
                className='aspect-square rounded-lg overflow-hidden bg-gray-200'
              >
                <Skeleton className='w-full h-full' />
              </div>
            ))}
          </div>
        </div>

        {/* Product Details Skeleton */}
        <div className='space-y-6'>
          {/* Product Title and Price */}
          <div className='space-y-4'>
            <Skeleton className='h-8 w-3/4' />
            <Skeleton className='h-10 w-1/3' />
            <Skeleton className='h-4 w-full' />
            <Skeleton className='h-4 w-5/6' />
          </div>

          {/* Specifications */}
          <div className='space-y-3'>
            <Skeleton className='h-6 w-1/2' />
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className='flex items-center space-x-2'>
                <Skeleton className='h-4 w-4 rounded-full' />
                <Skeleton className='h-4 w-3/4' />
              </div>
            ))}
          </div>

          {/* Order Form Skeleton */}
          <Card>
            <CardHeader>
              <Skeleton className='h-6 w-1/3' />
            </CardHeader>
            <CardContent className='space-y-6'>
              {/* Quantity Selector */}
              <div className='space-y-2'>
                <Skeleton className='h-4 w-1/4' />
                <div className='flex items-center space-x-4'>
                  <Skeleton className='h-10 w-10 rounded' />
                  <Skeleton className='h-10 w-16' />
                  <Skeleton className='h-10 w-10 rounded' />
                </div>
              </div>

              {/* Customer Information Form */}
              <div className='grid grid-cols-2 gap-4'>
                <div className='space-y-2'>
                  <Skeleton className='h-4 w-1/2' />
                  <Skeleton className='h-10 w-full' />
                </div>
                <div className='space-y-2'>
                  <Skeleton className='h-4 w-1/2' />
                  <Skeleton className='h-10 w-full' />
                </div>
              </div>

              <div className='space-y-2'>
                <Skeleton className='h-4 w-1/3' />
                <Skeleton className='h-10 w-full' />
              </div>

              <div className='space-y-2'>
                <Skeleton className='h-4 w-1/4' />
                <Skeleton className='h-10 w-full' />
              </div>

              {/* Order Summary */}
              <div className='border-t pt-4 space-y-2'>
                <div className='flex justify-between'>
                  <Skeleton className='h-4 w-1/4' />
                  <Skeleton className='h-4 w-1/6' />
                </div>
                <div className='flex justify-between'>
                  <Skeleton className='h-4 w-1/6' />
                  <Skeleton className='h-4 w-1/8' />
                </div>
                <div className='flex justify-between font-semibold'>
                  <Skeleton className='h-5 w-1/5' />
                  <Skeleton className='h-5 w-1/6' />
                </div>
              </div>

              {/* Submit Button */}
              <Skeleton className='h-12 w-full rounded-lg' />
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Additional Content Skeletons */}
      <div className='mt-16 space-y-12'>
        {/* Product Details Section */}
        <div className='space-y-6'>
          <Skeleton className='h-8 w-1/3 mx-auto' />
          <div className='grid md:grid-cols-2 gap-8'>
            <div className='space-y-4'>
              <Skeleton className='h-6 w-1/2' />
              <Skeleton className='h-4 w-full' />
              <Skeleton className='h-4 w-5/6' />
              <Skeleton className='h-4 w-4/5' />
            </div>
            <div className='space-y-4'>
              <Skeleton className='h-6 w-1/2' />
              <Skeleton className='h-4 w-full' />
              <Skeleton className='h-4 w-5/6' />
              <Skeleton className='h-4 w-4/5' />
            </div>
          </div>
        </div>

        {/* Pickup Information Section */}
        <div className='space-y-6'>
          <Skeleton className='h-8 w-1/4 mx-auto' />
          <Card>
            <CardContent className='p-6'>
              <div className='space-y-4'>
                <Skeleton className='h-6 w-1/3' />
                <Skeleton className='h-4 w-full' />
                <Skeleton className='h-4 w-3/4' />
                <Skeleton className='h-4 w-1/2' />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

// Alternative compact skeleton for smaller spaces
export function FieldDay2025ProductCompactSkeleton() {
  return (
    <Card>
      <CardContent className='p-6'>
        <div className='flex items-center space-x-4'>
          <Skeleton className='h-16 w-16 rounded-lg' />
          <div className='flex-1 space-y-2'>
            <Skeleton className='h-5 w-3/4' />
            <Skeleton className='h-4 w-1/2' />
            <Skeleton className='h-6 w-1/3' />
          </div>
          <Skeleton className='h-10 w-24' />
        </div>
      </CardContent>
    </Card>
  );
}
