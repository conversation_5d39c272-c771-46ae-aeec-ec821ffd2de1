// AlignUI Divider v0.0.0

import * as React from 'react';
import { cn } from '@/lib/utils';

const Divider = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    variant?: 'line' | 'line-spacing';
  }
>(({ className, variant = 'line', ...rest }, forwardedRef) => {
  return (
    <div
      ref={forwardedRef}
      className={cn(
        'border-t border-gray-200',
        variant === 'line-spacing' && 'my-2',
        className
      )}
      {...rest}
    />
  );
});
Divider.displayName = 'Divider';

export {
  Divider as Root,
};
