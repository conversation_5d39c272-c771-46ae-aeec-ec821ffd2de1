/**
 * Skeleton components for PPR loading states
 * These replace traditional loading spinners in Suspense boundaries
 */

import { Card, CardContent, CardHeader } from '@/components/ui/card';

export function UserHeaderSkeleton() {
  return (
    <div className='flex justify-between items-center animate-pulse'>
      <div>
        <div className='h-8 bg-gray-200 rounded w-48 mb-2'></div>
        <div className='h-4 bg-gray-200 rounded w-64'></div>
      </div>
      <div className='h-10 bg-gray-200 rounded w-24'></div>
    </div>
  );
}

export function SubscriptionStatusSkeleton() {
  return (
    <div className='space-y-4 animate-pulse'>
      <div className='flex items-center justify-between'>
        <div className='h-6 bg-gray-200 rounded w-32'></div>
        <div className='h-6 bg-gray-200 rounded w-20'></div>
      </div>
      <div className='space-y-2'>
        <div className='h-4 bg-gray-200 rounded w-full'></div>
        <div className='h-4 bg-gray-200 rounded w-3/4'></div>
        <div className='h-4 bg-gray-200 rounded w-1/2'></div>
      </div>
      <div className='flex space-x-2'>
        <div className='h-10 bg-gray-200 rounded w-24'></div>
        <div className='h-10 bg-gray-200 rounded w-24'></div>
      </div>
    </div>
  );
}

export function DeliveryTrackingSkeleton() {
  return (
    <Card>
      <CardHeader>
        <div className='h-6 bg-gray-200 rounded w-48 animate-pulse'></div>
      </CardHeader>
      <CardContent>
        <div className='space-y-4 animate-pulse'>
          {[1, 2, 3].map((i) => (
            <div
              key={i}
              className='flex items-center space-x-4 p-4 border rounded-lg'
            >
              <div className='h-12 w-12 bg-gray-200 rounded'></div>
              <div className='flex-1 space-y-2'>
                <div className='h-4 bg-gray-200 rounded w-3/4'></div>
                <div className='h-3 bg-gray-200 rounded w-1/2'></div>
              </div>
              <div className='h-6 bg-gray-200 rounded w-20'></div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

export function AccountInfoSkeleton() {
  return (
    <div className='space-y-4 animate-pulse'>
      <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
        <div>
          <div className='h-4 bg-gray-200 rounded w-16 mb-2'></div>
          <div className='h-6 bg-gray-200 rounded w-full'></div>
        </div>
        <div>
          <div className='h-4 bg-gray-200 rounded w-16 mb-2'></div>
          <div className='h-6 bg-gray-200 rounded w-full'></div>
        </div>
      </div>
      <div className='flex space-x-2'>
        <div className='h-10 bg-gray-200 rounded w-32'></div>
        <div className='h-10 bg-gray-200 rounded w-32'></div>
      </div>
    </div>
  );
}

export function SubscriptionHistorySkeleton() {
  return (
    <Card>
      <CardHeader>
        <div className='h-6 bg-gray-200 rounded w-40 animate-pulse'></div>
      </CardHeader>
      <CardContent>
        <div className='space-y-3 animate-pulse'>
          {[1, 2, 3, 4, 5].map((i) => (
            <div
              key={i}
              className='flex items-center justify-between p-3 border rounded'
            >
              <div className='flex items-center space-x-3'>
                <div className='h-8 w-8 bg-gray-200 rounded'></div>
                <div>
                  <div className='h-4 bg-gray-200 rounded w-24 mb-1'></div>
                  <div className='h-3 bg-gray-200 rounded w-16'></div>
                </div>
              </div>
              <div className='h-6 bg-gray-200 rounded w-16'></div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

export function PersonalizationSkeleton() {
  return (
    <section className='py-12 bg-green-50'>
      <div className='max-w-7xl mx-auto px-4'>
        <div className='animate-pulse'>
          <div className='h-8 bg-gray-200 rounded w-1/2 mb-6'></div>
          <div className='grid md:grid-cols-3 gap-6'>
            {[1, 2, 3].map((i) => (
              <div key={i} className='bg-white p-6 rounded-lg shadow'>
                <div className='h-4 bg-gray-200 rounded w-3/4 mb-2'></div>
                <div className='h-4 bg-gray-200 rounded w-1/2 mb-4'></div>
                <div className='h-10 bg-gray-200 rounded w-full'></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}

export function InventorySkeleton() {
  return (
    <section className='py-8 bg-gray-50 border-l-4 border-gray-200'>
      <div className='max-w-7xl mx-auto px-4'>
        <div className='flex items-center space-x-4 animate-pulse'>
          <div className='flex-shrink-0'>
            <div className='h-8 w-8 bg-gray-200 rounded'></div>
          </div>
          <div className='flex-1'>
            <div className='h-5 bg-gray-200 rounded w-1/3 mb-2'></div>
            <div className='h-4 bg-gray-200 rounded w-1/2'></div>
          </div>
        </div>
      </div>
    </section>
  );
}

export function AdminStatsSkeleton() {
  return (
    <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
      {[1, 2, 3, 4].map((i) => (
        <Card key={i}>
          <CardContent className='p-6'>
            <div className='animate-pulse'>
              <div className='flex items-center justify-between mb-2'>
                <div className='h-4 bg-gray-200 rounded w-20'></div>
                <div className='h-4 w-4 bg-gray-200 rounded'></div>
              </div>
              <div className='h-8 bg-gray-200 rounded w-16 mb-1'></div>
              <div className='h-3 bg-gray-200 rounded w-24'></div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

export function DeliveryTableSkeleton() {
  return (
    <div className='space-y-4'>
      <div className='rounded-md border'>
        <div className='p-4'>
          <div className='space-y-3 animate-pulse'>
            {/* Table header */}
            <div className='grid grid-cols-6 gap-4 pb-3 border-b'>
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <div key={i} className='h-4 bg-gray-200 rounded'></div>
              ))}
            </div>
            {/* Table rows */}
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className='grid grid-cols-6 gap-4 py-3'>
                {[1, 2, 3, 4, 5, 6].map((j) => (
                  <div key={j} className='h-4 bg-gray-200 rounded'></div>
                ))}
              </div>
            ))}
          </div>
        </div>
      </div>
      {/* Pagination skeleton */}
      <div className='flex items-center justify-between'>
        <div className='h-4 bg-gray-200 rounded w-48 animate-pulse'></div>
        <div className='flex space-x-2'>
          <div className='h-8 bg-gray-200 rounded w-20 animate-pulse'></div>
          <div className='h-8 bg-gray-200 rounded w-16 animate-pulse'></div>
        </div>
      </div>
    </div>
  );
}

export function ChartSkeleton() {
  return (
    <Card>
      <CardHeader>
        <div className='h-6 bg-gray-200 rounded w-32 animate-pulse'></div>
      </CardHeader>
      <CardContent>
        <div className='h-64 bg-gray-200 rounded animate-pulse'></div>
      </CardContent>
    </Card>
  );
}

export function NavbarAuthSkeleton() {
  return (
    <div className='flex items-center space-x-4'>
      {/* Desktop skeleton */}
      <div className='hidden md:flex items-center space-x-4'>
        <div className='h-10 bg-gray-200 rounded w-32 animate-pulse'></div>
        <div className='h-10 bg-gray-200 rounded w-20 animate-pulse'></div>
      </div>
      {/* Mobile skeleton */}
      <div className='md:hidden'>
        <div className='h-10 bg-gray-200 rounded w-32 animate-pulse'></div>
      </div>
    </div>
  );
}

export function MobileNavbarAuthSkeleton() {
  return (
    <div className='pt-4 border-t border-green-100'>
      <div className='h-10 bg-gray-200 rounded w-full animate-pulse'></div>
    </div>
  );
}
