'use client';

import * as React from 'react';
import { Eye, EyeOff } from 'lucide-react';
import { cn } from '@/lib/utils/cn';
import { Input } from './input';
import { Root as Checkbox } from './checkbox';

export interface PasswordInputProps
  extends Omit<React.ComponentProps<'input'>, 'type'> {
  showToggle?: boolean;
  togglePosition?: 'checkbox' | 'icon';
  toggleLabel?: string;
}

const PasswordInput = React.forwardRef<HTMLInputElement, PasswordInputProps>(
  (
    {
      className,
      showToggle = true,
      togglePosition = 'checkbox',
      toggleLabel = 'Show password',
      ...props
    },
    ref
  ) => {
    const [showPassword, setShowPassword] = React.useState(false);

    const togglePasswordVisibility = () => {
      setShowPassword(!showPassword);
    };

    if (togglePosition === 'icon') {
      return (
        <div className='relative'>
          <Input
            type={showPassword ? 'text' : 'password'}
            className={cn('pr-10', className)}
            ref={ref}
            {...props}
          />
          {showToggle && (
            <button
              type='button'
              onClick={togglePasswordVisibility}
              className='absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600 focus:outline-none'
              tabIndex={-1}
            >
              {showPassword ? (
                <EyeOff className='h-4 w-4' />
              ) : (
                <Eye className='h-4 w-4' />
              )}
            </button>
          )}
        </div>
      );
    }

    // Default checkbox toggle
    return (
      <div className='space-y-2'>
        <Input
          type={showPassword ? 'text' : 'password'}
          className={className}
          ref={ref}
          {...props}
        />
        {showToggle && (
          <div className='flex items-center space-x-2'>
            <Checkbox
              id={`show-password-${props.id || 'default'}`}
              checked={showPassword}
              onCheckedChange={(checked) => setShowPassword(checked === true)}
            />
            <label
              htmlFor={`show-password-${props.id || 'default'}`}
              className='text-sm text-gray-600 cursor-pointer select-none'
            >
              {toggleLabel}
            </label>
          </div>
        )}
      </div>
    );
  }
);

PasswordInput.displayName = 'PasswordInput';

export { PasswordInput };
