'use client';

import { Component, ReactNode, Suspense } from 'react';
import { AlertTriangle, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  showRetry?: boolean;
  onRetry?: () => void;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class PPRErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('PPR Error Boundary caught an error:', error, errorInfo);

    // You can also log the error to an error reporting service here
    // Example: errorReportingService.captureException(error, { extra: errorInfo });
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined });
    if (this.props.onRetry) {
      this.props.onRetry();
    }
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <Alert variant='destructive' className='my-4'>
          <AlertTriangle className='h-4 w-4' />
          <AlertDescription className='flex items-center justify-between'>
            <div>
              <p className='font-medium'>
                Something went wrong loading this content.
              </p>
              {this.state.error && (
                <p className='text-sm text-muted-foreground mt-1'>
                  {this.state.error.message}
                </p>
              )}
            </div>
            {this.props.showRetry !== false && (
              <Button
                variant='outline'
                size='sm'
                onClick={this.handleRetry}
                className='ml-4'
              >
                <RefreshCw className='h-3 w-3 mr-1' />
                Retry
              </Button>
            )}
          </AlertDescription>
        </Alert>
      );
    }

    return this.props.children;
  }
}

// Convenience wrapper for common use cases
export function PPRSuspenseWrapper({
  children,
  fallback,
  errorFallback,
  showRetry = true,
  onRetry,
}: {
  children: ReactNode;
  fallback: ReactNode;
  errorFallback?: ReactNode;
  showRetry?: boolean;
  onRetry?: () => void;
}) {
  return (
    <PPRErrorBoundary
      fallback={errorFallback}
      showRetry={showRetry}
      onRetry={onRetry}
    >
      <Suspense fallback={fallback}>{children}</Suspense>
    </PPRErrorBoundary>
  );
}

// Specific error fallbacks for different content types
export function DataLoadingError({
  message = 'Failed to load data',
  onRetry,
}: {
  message?: string;
  onRetry?: () => void;
}) {
  return (
    <div className='p-4 bg-red-50 border border-red-200 rounded-lg text-center'>
      <AlertTriangle className='h-8 w-8 text-red-600 mx-auto mb-2' />
      <p className='text-red-800 font-medium'>{message}</p>
      {onRetry && (
        <Button variant='outline' size='sm' onClick={onRetry} className='mt-2'>
          <RefreshCw className='h-3 w-3 mr-1' />
          Try Again
        </Button>
      )}
    </div>
  );
}

export function UserDataError({ onRetry }: { onRetry?: () => void }) {
  return (
    <DataLoadingError
      message='Failed to load your account information'
      onRetry={onRetry}
    />
  );
}

export function SubscriptionDataError({ onRetry }: { onRetry?: () => void }) {
  return (
    <DataLoadingError
      message='Failed to load subscription data'
      onRetry={onRetry}
    />
  );
}

export function DeliveryDataError({ onRetry }: { onRetry?: () => void }) {
  return (
    <DataLoadingError
      message='Failed to load delivery information'
      onRetry={onRetry}
    />
  );
}

export function AdminDataError({ onRetry }: { onRetry?: () => void }) {
  return (
    <DataLoadingError message='Failed to load admin data' onRetry={onRetry} />
  );
}
