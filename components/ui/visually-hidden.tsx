import * as React from 'react';
import { cn } from '@/lib/utils/cn';

interface VisuallyHiddenProps extends React.HTMLAttributes<HTMLSpanElement> {
  children: React.ReactNode;
}

/**
 * VisuallyHidden component that hides content visually but keeps it accessible to screen readers.
 * This is useful for providing context to assistive technologies without affecting the visual design.
 */
export function VisuallyHidden({ children, className, ...props }: VisuallyHiddenProps) {
  return (
    <span
      className={cn(
        'absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0',
        'clip-path-[inset(50%)]',
        className
      )}
      style={{
        clipPath: 'inset(50%)',
        clip: 'rect(0 0 0 0)',
      }}
      {...props}
    >
      {children}
    </span>
  );
}
