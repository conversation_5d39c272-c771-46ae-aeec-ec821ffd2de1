'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import {
  cancelSubscriptionAction,
  getSubscriptionPaymentHistoryAction,
  pauseSubscriptionAction,
  resumeSubscriptionAction,
  toggleAutoRenewAction,
} from '@/lib/actions/user/subscription-management';
import { Tables } from '@/lib/supabase/types';
import {
  AlertTriangle,
  CreditCard,
  Pause,
  Play,
  Settings,
  X,
} from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

type Subscription = Tables<'subscriptions'>;

interface SubscriptionManagementProps {
  subscription: Subscription;
}

export default function SubscriptionManagement({
  subscription,
}: SubscriptionManagementProps) {
  console.time('SubscriptionManagement-render');

  // React hooks must be called before any early returns
  const [isLoading, setIsLoading] = useState(false);
  const [pauseUntil, setPauseUntil] = useState('');
  const [showPaymentHistory, setShowPaymentHistory] = useState(false);
  const [paymentHistory, setPaymentHistory] = useState<any[]>([]);
  const [actionType, setActionType] = useState<string | null>(null);

  // Safety check: ensure subscription object exists and has required properties
  if (!subscription || !subscription.id) {
    console.error(
      'SubscriptionManagement: Invalid subscription object',
      subscription
    );
    return (
      <div className='text-center py-4'>
        <p className='text-red-600'>
          Unable to load subscription management. Please refresh the page.
        </p>
      </div>
    );
  }

  const handleCancelSubscription = async () => {
    setIsLoading(true);
    setActionType('cancel');
    console.time('cancelSubscription');
    try {
      const result = await cancelSubscriptionAction(subscription.id);
      console.timeEnd('cancelSubscription');
      if (result.success) {
        toast.success('Subscription cancelled successfully');
      } else {
        console.error('Cancel subscription failed:', result.error);
        toast.error(result.error || 'Failed to cancel subscription');
      }
    } catch (error) {
      console.error('Cancel subscription exception:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsLoading(false);
      setActionType(null);
    }
  };

  const handlePauseSubscription = async () => {
    if (!pauseUntil) {
      toast.error('Please select a resume date');
      return;
    }

    setIsLoading(true);
    setActionType('pause');
    console.time('pauseSubscription');
    try {
      const result = await pauseSubscriptionAction(subscription.id, pauseUntil);
      console.timeEnd('pauseSubscription');
      if (result.success) {
        toast.success('Subscription paused successfully');
        setPauseUntil('');
      } else {
        console.error('Pause subscription failed:', result.error);
        toast.error(result.error || 'Failed to pause subscription');
      }
    } catch (error) {
      console.error('Pause subscription exception:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsLoading(false);
      setActionType(null);
    }
  };

  const handleResumeSubscription = async () => {
    setIsLoading(true);
    setActionType('resume');
    console.time('resumeSubscription');
    try {
      const result = await resumeSubscriptionAction(subscription.id);
      console.timeEnd('resumeSubscription');
      if (result.success) {
        toast.success('Subscription resumed successfully');
      } else {
        console.error('Resume subscription failed:', result.error);
        toast.error(result.error || 'Failed to resume subscription');
      }
    } catch (error) {
      console.error('Resume subscription exception:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsLoading(false);
      setActionType(null);
    }
  };

  const handleToggleAutoRenew = async (autoRenew: boolean) => {
    setIsLoading(true);
    setActionType('toggle-auto-renew');
    console.time('toggleAutoRenew');
    try {
      const result = await toggleAutoRenewAction(subscription.id, autoRenew);
      console.timeEnd('toggleAutoRenew');
      if (result.success) {
        toast.success(`Auto-renewal ${autoRenew ? 'enabled' : 'disabled'}`);
      } else {
        console.error('Toggle auto-renew failed:', result.error);
        toast.error(result.error || 'Failed to update auto-renewal');
      }
    } catch (error) {
      console.error('Toggle auto-renew exception:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsLoading(false);
      setActionType(null);
    }
  };

  const handleViewPaymentHistory = async () => {
    setIsLoading(true);
    setActionType('payment-history');
    console.time('viewPaymentHistory');
    try {
      const result = await getSubscriptionPaymentHistoryAction(subscription.id);
      console.timeEnd('viewPaymentHistory');
      if (result.success) {
        setPaymentHistory(result.data || []);
        setShowPaymentHistory(true);
      } else {
        console.error('View payment history failed:', result.error);
        toast.error(result.error || 'Failed to fetch payment history');
      }
    } catch (error) {
      console.error('View payment history exception:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsLoading(false);
      setActionType(null);
    }
  };

  const getMinDate = () => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    return tomorrow.toISOString().split('T')[0];
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          <Settings className='h-5 w-5' />
          Manage Subscription
        </CardTitle>
      </CardHeader>
      <CardContent className='space-y-4'>
        {/* Auto-Renewal Toggle */}
        <div className='flex items-center justify-between'>
          <div>
            <Label htmlFor='auto-renew'>Auto-Renewal</Label>
            <p className='text-sm text-gray-600'>
              Automatically renew when deliveries are complete
            </p>
          </div>
          <Switch
            id='auto-renew'
            checked={Boolean(subscription?.auto_renew)}
            onCheckedChange={handleToggleAutoRenew}
            disabled={
              isLoading ||
              (subscription?.status ? subscription.status !== 'active' : false)
            }
            aria-label={`Auto-renewal is ${Boolean(subscription?.auto_renew) ? 'enabled' : 'disabled'}`}
            data-loading={
              isLoading && actionType === 'toggle-auto-renew' ? 'true' : 'false'
            }
          />
        </div>

        {/* Action Buttons */}
        <div className='flex flex-wrap gap-2'>
          {/* Pause/Resume Button */}
          {subscription?.status === 'active' || !subscription?.status ? (
            <Dialog>
              <DialogTrigger asChild>
                <Button
                  variant='outline'
                  size='sm'
                  disabled={isLoading}
                  data-loading={
                    isLoading && actionType === 'pause' ? 'true' : 'false'
                  }
                >
                  <Pause className='h-4 w-4 mr-2' />
                  Pause Subscription
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Pause Subscription</DialogTitle>
                  <DialogDescription>
                    Choose when you&apos;d like to resume your subscription.
                    Your subscription will be paused immediately and you can
                    resume it later. Note: You cannot start a new subscription
                    while this one is paused - you would need to cancel this
                    subscription first.
                  </DialogDescription>
                </DialogHeader>
                <div className='space-y-4'>
                  <div>
                    <Label htmlFor='pause-until'>Resume Date</Label>
                    <Input
                      id='pause-until'
                      type='date'
                      value={pauseUntil}
                      onChange={(e) => setPauseUntil(e.target.value)}
                      min={getMinDate()}
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    onClick={handlePauseSubscription}
                    disabled={isLoading || !pauseUntil}
                    data-loading={
                      isLoading && actionType === 'pause' ? 'true' : 'false'
                    }
                  >
                    {isLoading && actionType === 'pause'
                      ? 'Processing...'
                      : 'Pause Subscription'}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          ) : subscription?.status === 'paused' ? (
            <div className='flex flex-wrap gap-2'>
              <Button
                variant='default'
                size='sm'
                onClick={handleResumeSubscription}
                disabled={isLoading}
                className='bg-green-600 hover:bg-green-700'
                data-loading={
                  isLoading && actionType === 'resume' ? 'true' : 'false'
                }
              >
                <Play className='h-4 w-4 mr-2' />
                {isLoading && actionType === 'resume'
                  ? 'Processing...'
                  : 'Resume Subscription'}
              </Button>
              <Dialog>
                <DialogTrigger asChild>
                  <Button
                    variant='destructive'
                    size='sm'
                    disabled={isLoading}
                    data-loading={
                      isLoading && actionType === 'cancel' ? 'true' : 'false'
                    }
                  >
                    <X className='h-4 w-4 mr-2' />
                    Stop Permanently
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle className='flex items-center gap-2'>
                      <AlertTriangle className='h-5 w-5 text-red-500' />
                      Stop Subscription Permanently
                    </DialogTitle>
                    <DialogDescription>
                      Are you sure you want to permanently stop this paused
                      subscription? This action cannot be undone and you will
                      lose access to future deliveries. After cancellation, you
                      will be able to start a new subscription if desired.
                    </DialogDescription>
                  </DialogHeader>
                  <DialogFooter>
                    <Button
                      variant='destructive'
                      onClick={handleCancelSubscription}
                      disabled={isLoading}
                      data-loading={
                        isLoading && actionType === 'cancel' ? 'true' : 'false'
                      }
                    >
                      {isLoading && actionType === 'cancel'
                        ? 'Processing...'
                        : 'Yes, Stop Permanently'}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          ) : null}

          {/* Payment History Button */}
          <Button
            variant='outline'
            size='sm'
            onClick={handleViewPaymentHistory}
            disabled={isLoading}
            data-loading={
              isLoading && actionType === 'payment-history' ? 'true' : 'false'
            }
          >
            <CreditCard className='h-4 w-4 mr-2' />
            {isLoading && actionType === 'payment-history'
              ? 'Loading...'
              : 'Payment History'}
          </Button>

          {/* Cancel Button - Only for Active Subscriptions */}
          {subscription?.status === 'active' && (
            <Dialog>
              <DialogTrigger asChild>
                <Button
                  variant='destructive'
                  size='sm'
                  disabled={isLoading}
                  data-loading={
                    isLoading && actionType === 'cancel' ? 'true' : 'false'
                  }
                >
                  <X className='h-4 w-4 mr-2' />
                  Cancel Subscription
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle className='flex items-center gap-2'>
                    <AlertTriangle className='h-5 w-5 text-red-500' />
                    Cancel Subscription
                  </DialogTitle>
                  <DialogDescription>
                    Are you sure you want to cancel your subscription? This
                    action cannot be undone. You will lose access to future
                    deliveries and will need to cancel to start any new
                    subscription. Consider pausing instead if you want to resume
                    this same subscription later.
                  </DialogDescription>
                </DialogHeader>
                <DialogFooter>
                  <Button
                    variant='destructive'
                    onClick={handleCancelSubscription}
                    disabled={isLoading}
                    data-loading={
                      isLoading && actionType === 'cancel' ? 'true' : 'false'
                    }
                  >
                    {isLoading && actionType === 'cancel'
                      ? 'Processing...'
                      : 'Yes, Cancel Permanently'}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          )}
        </div>

        {/* Payment History Dialog */}
        <Dialog open={showPaymentHistory} onOpenChange={setShowPaymentHistory}>
          <DialogContent className='max-w-2xl'>
            <DialogHeader>
              <DialogTitle>Payment History</DialogTitle>
            </DialogHeader>
            <div className='space-y-4 max-h-96 overflow-y-auto'>
              {paymentHistory.length > 0 ? (
                paymentHistory.map((payment) => (
                  <div key={payment.id} className='border rounded-lg p-4'>
                    <div className='flex justify-between items-start'>
                      <div>
                        <p className='font-medium'>${payment.amount}</p>
                        <p className='text-sm text-gray-600'>
                          {new Date(payment.created_at).toLocaleDateString()}
                        </p>
                      </div>
                      <Badge
                        variant={
                          payment.status === 'succeeded'
                            ? 'default'
                            : 'destructive'
                        }
                      >
                        {payment.status}
                      </Badge>
                    </div>
                    <p className='text-sm text-gray-600 mt-2'>
                      Payment ID: {payment.payment_id}
                    </p>
                  </div>
                ))
              ) : (
                <p className='text-center text-gray-500 py-8'>
                  No payment history found
                </p>
              )}
            </div>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
}
