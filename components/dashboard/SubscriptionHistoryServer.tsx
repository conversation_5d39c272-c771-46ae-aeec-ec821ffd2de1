import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { History, Calendar, Package, CreditCard } from 'lucide-react';
import { format } from 'date-fns';
import { BOX_SIZES, FREQUENCIES } from '@/lib/constants/subscription';
import { getSubscriptionHistoryAction } from '@/lib/actions/user/subscription-management';

function getStatusBadge(status: string) {
  switch (status) {
    case 'active':
      return <Badge variant='default'>Active</Badge>;
    case 'paused':
      return <Badge variant='secondary'>Paused</Badge>;
    case 'cancelled':
      return <Badge variant='destructive'>Cancelled</Badge>;
    case 'completed':
      return <Badge variant='outline'>Completed</Badge>;
    default:
      return <Badge variant='outline'>{status}</Badge>;
  }
}

function getPaymentStatusBadge(status: string) {
  switch (status) {
    case 'succeeded':
      return <Badge variant='default'>Paid</Badge>;
    case 'pending':
      return <Badge variant='secondary'>Pending</Badge>;
    case 'failed':
      return <Badge variant='destructive'>Failed</Badge>;
    default:
      return <Badge variant='outline'>{status}</Badge>;
  }
}

export async function SubscriptionHistoryServer() {
  const result = await getSubscriptionHistoryAction();

  if (!result.success) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <History className='h-5 w-5' />
            Subscription History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='text-center py-8'>
            <Package className='h-12 w-12 text-red-400 mx-auto mb-4' />
            <p className='text-red-500'>Failed to load subscription history</p>
            <p className='text-sm text-gray-400 mt-1'>{result.error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const subscriptions = result.data || [];

  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          <History className='h-5 w-5' />
          Subscription History
        </CardTitle>
        <p className='text-sm text-gray-600 mt-2'>
          View all your past and current subscriptions
        </p>
      </CardHeader>
      <CardContent>
        {subscriptions.length === 0 ? (
          <div className='text-center py-8'>
            <Package className='h-12 w-12 text-gray-400 mx-auto mb-4' />
            <p className='text-gray-500'>No subscription history found</p>
            <p className='text-sm text-gray-400 mt-1'>
              Your subscription history will appear here once you create a
              subscription
            </p>
          </div>
        ) : (
          <div className='space-y-4'>
            {subscriptions.map((subscription: any) => (
              <div key={subscription.id} className='border rounded-lg p-4'>
                <div className='flex items-start justify-between mb-4'>
                  <div>
                    <h3 className='font-semibold text-lg'>
                      {BOX_SIZES[
                        subscription.box_size as keyof typeof BOX_SIZES
                      ]?.name || subscription.box_size}{' '}
                      Box
                    </h3>
                    {getStatusBadge(subscription.status)}
                  </div>
                </div>

                <div className='grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-4'>
                  <div className='flex items-center gap-2'>
                    <Calendar className='h-4 w-4' />
                    <span>
                      {FREQUENCIES[
                        subscription.frequency as keyof typeof FREQUENCIES
                      ]?.name || subscription.frequency}
                    </span>
                  </div>
                  <div>
                    <span className='font-medium'>Started:</span>{' '}
                    {subscription.created_at
                      ? format(
                          new Date(subscription.created_at),
                          'MMM dd, yyyy'
                        )
                      : 'N/A'}
                  </div>
                  <div>
                    <span className='font-medium'>Price:</span> $
                    {((subscription.base_price_cents || 0) / 100).toFixed(2)}
                    /month
                  </div>
                </div>

                <Separator className='my-4' />

                {/* Subscription Details */}
                <div className='mb-4'>
                  <h4 className='font-medium mb-2'>Subscription Details</h4>
                  <div className='grid grid-cols-1 md:grid-cols-2 gap-4 text-sm'>
                    <div>
                      <span className='font-medium'>Delivery Type:</span>{' '}
                      {subscription.delivery_type === 'pickup'
                        ? 'Pickup'
                        : 'Delivery'}
                    </div>
                    <div>
                      <span className='font-medium'>Location:</span>{' '}
                      {subscription.pickup_location || 'N/A'}
                    </div>
                    <div>
                      <span className='font-medium'>Deliveries Remaining:</span>{' '}
                      {subscription.deliveries_remaining || 0}
                    </div>
                    <div>
                      <span className='font-medium'>Auto-Renewal:</span>{' '}
                      {subscription.auto_renew ? 'Enabled' : 'Disabled'}
                    </div>
                  </div>
                </div>

                {/* Payment History */}
                <div>
                  <h4 className='font-medium mb-2 flex items-center gap-2'>
                    <CreditCard className='h-4 w-4' />
                    Payment History ({subscription.payments?.length || 0}{' '}
                    payments)
                  </h4>
                  {subscription.payments && subscription.payments.length > 0 ? (
                    <div className='space-y-2'>
                      {subscription.payments.map((payment: any) => (
                        <div
                          key={payment.id}
                          className='flex items-center justify-between p-3 bg-gray-50 rounded'
                        >
                          <div>
                            <div className='font-medium'>
                              ${(payment.amount / 100).toFixed(2)}
                            </div>
                            <div className='text-sm text-gray-600'>
                              {payment.created_at
                                ? format(
                                    new Date(payment.created_at),
                                    'MMM dd, yyyy'
                                  )
                                : 'N/A'}
                            </div>
                          </div>
                          <div className='text-right'>
                            {getPaymentStatusBadge(payment.status)}
                            <div className='text-xs text-gray-500 mt-1'>
                              ID: {payment.payment_id}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className='text-sm text-gray-500'>
                      No payment history available
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
