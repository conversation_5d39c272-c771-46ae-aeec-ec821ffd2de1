'use client';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Calendar, Leaf, Loader2, Package } from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

interface BoxContent {
  id: string;
  week_start_date: string;
  contents: string;
  created_at: string;
}

interface SubscriptionBoxContentsProps {
  className?: string;
}

export default function SubscriptionBoxContents({
  className,
}: SubscriptionBoxContentsProps) {
  const [loading, setLoading] = useState(true);
  const [boxContents, setBoxContents] = useState<BoxContent[]>([]);

  useEffect(() => {
    loadBoxContents();
  }, []);

  const loadBoxContents = async () => {
    setLoading(true);
    try {
      // Fetch box contents from the API
      const response = await fetch('/api/box-contents');
      if (!response.ok) {
        throw new Error('Failed to fetch box contents');
      }

      const data = await response.json();
      setBoxContents(data);
    } catch (error) {
      console.error('Failed to load box contents:', error);
      toast.error('Failed to load box contents');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const getWeekLabel = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const diffTime = date.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < -6) {
      return 'Past Week';
    } else if (diffDays >= -6 && diffDays <= 0) {
      return 'This Week';
    } else if (diffDays > 0 && diffDays <= 7) {
      return 'Next Week';
    } else {
      return 'Upcoming';
    }
  };

  const parseContents = (contents: string) => {
    // Split by comma and clean up each item
    return contents
      .split(',')
      .map((item) => item.trim())
      .filter((item) => item.length > 0);
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className='p-6'>
          <div className='flex items-center justify-center'>
            <Loader2 className='h-6 w-6 animate-spin mr-2' />
            <span>Loading box contents...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          <Package className='h-5 w-5' />
          What You&apos;ll Get in Your Share
        </CardTitle>
        <p className='text-sm text-gray-600'>
          Fresh, seasonal produce delivered to your pickup location
        </p>
      </CardHeader>
      <CardContent className='space-y-6'>
        {boxContents.length > 0 ? (
          boxContents.map((box) => (
            <div key={box.id} className='border rounded-lg p-4'>
              <div className='flex items-center justify-between mb-3'>
                <div className='flex items-center gap-2'>
                  <Calendar className='h-4 w-4 text-gray-500' />
                  <span className='font-medium'>
                    {formatDate(box.week_start_date)}
                  </span>
                </div>
                <Badge
                  variant={
                    getWeekLabel(box.week_start_date) === 'This Week'
                      ? 'default'
                      : 'secondary'
                  }
                >
                  {getWeekLabel(box.week_start_date)}
                </Badge>
              </div>

              <div className='space-y-2'>
                <div className='flex flex-wrap gap-2'>
                  {parseContents(box.contents).map((item, index) => (
                    <div
                      key={index}
                      className='flex items-center gap-1 bg-green-50 text-green-700 px-2 py-1 rounded-md text-sm'
                    >
                      <Leaf className='h-3 w-3' />
                      {item}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className='text-center py-8'>
            <Package className='h-12 w-12 text-gray-400 mx-auto mb-4' />
            <p className='text-gray-500 mb-2'>
              No share contents available yet
            </p>
            <p className='text-sm text-gray-400'>
              Share contents will be updated weekly with fresh seasonal produce
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
