'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Package,
  Calendar,
  MapPin,
  Clock,
  CheckCircle,
  XCircle,
} from 'lucide-react';
import { customerDeliveryService } from '@/lib/services/customer-delivery';
import { Delivery } from '@/lib/types/delivery';
import { DELIVERY_STATUS } from '@/lib/types/delivery';

interface DeliveryTrackingProps {
  userId: string;
}

export function DeliveryTracking({ userId }: DeliveryTrackingProps) {
  const [nextDelivery, setNextDelivery] = useState<Delivery | null>(null);
  const [upcomingDeliveries, setUpcomingDeliveries] = useState<Delivery[]>([]);
  const [stats, setStats] = useState<{
    totalDeliveries: number;
    deliveredCount: number;
    scheduledCount: number;
    nextDeliveryDate: string | null;
  } | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDeliveryData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch next delivery
        const nextResult =
          await customerDeliveryService.getNextDelivery(userId);
        if (nextResult.error && nextResult.error.code !== 'PGRST116') {
          throw new Error(nextResult.error.message);
        }
        setNextDelivery(nextResult.data);

        // Fetch upcoming deliveries
        const upcomingResult =
          await customerDeliveryService.getUpcomingDeliveries(userId);
        if (upcomingResult.error) {
          throw new Error(upcomingResult.error.message);
        }
        setUpcomingDeliveries(upcomingResult.data || []);

        // Fetch delivery stats
        const statsResult =
          await customerDeliveryService.getUserDeliveryStats(userId);
        if (statsResult.error) {
          throw new Error(statsResult.error.message);
        }
        setStats(statsResult.data);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : 'Failed to fetch delivery data'
        );
      } finally {
        setLoading(false);
      }
    };

    fetchDeliveryData();
  }, [userId]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getStatusBadge = (status: string) => {
    const statusConfig =
      DELIVERY_STATUS[status as keyof typeof DELIVERY_STATUS];
    if (!statusConfig) return null;

    const variants = {
      scheduled: 'secondary' as const,
      delivered: 'default' as const,
      cancelled: 'destructive' as const,
    };

    const colors = {
      scheduled: 'bg-yellow-100 text-yellow-800',
      delivered: 'bg-green-100 text-green-800',
      cancelled: 'bg-red-100 text-red-800',
    };

    return (
      <Badge
        variant={variants[status as keyof typeof variants] || 'default'}
        className={colors[status as keyof typeof colors] || ''}
      >
        {statusConfig.name}
      </Badge>
    );
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'delivered':
        return <CheckCircle className='h-4 w-4 text-green-600' />;
      case 'scheduled':
        return <Clock className='h-4 w-4 text-yellow-600' />;
      case 'cancelled':
        return <XCircle className='h-4 w-4 text-red-600' />;
      default:
        return <Package className='h-4 w-4 text-gray-600' />;
    }
  };

  if (loading) {
    return (
      <div className='space-y-4'>
        <Card>
          <CardContent className='p-6'>
            <div className='animate-pulse space-y-4'>
              <div className='h-4 bg-gray-200 rounded w-1/4'></div>
              <div className='h-8 bg-gray-200 rounded w-1/2'></div>
              <div className='h-4 bg-gray-200 rounded w-3/4'></div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className='p-6'>
          <div className='text-center text-red-600'>
            <XCircle className='h-8 w-8 mx-auto mb-2' />
            <p>{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className='space-y-6'>
      {/* Next Delivery Card */}
      {nextDelivery ? (
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Package className='h-5 w-5' />
              Next Delivery
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              <div className='flex items-center justify-between'>
                <div className='flex items-center gap-2'>
                  <Calendar className='h-4 w-4 text-gray-500' />
                  <span className='font-medium'>
                    {formatDate(nextDelivery.delivery_date)}
                  </span>
                </div>
                {getStatusBadge(nextDelivery.status)}
              </div>

              <div className='flex items-center gap-2'>
                <MapPin className='h-4 w-4 text-gray-500' />
                <span className='text-sm text-gray-600'>
                  {nextDelivery.pickup_location.replace('_', ' ').toUpperCase()}
                </span>
              </div>

              {nextDelivery.box_contents && (
                <div className='bg-gray-50 p-3 rounded-lg'>
                  <p className='text-sm font-medium text-gray-700 mb-1'>
                    Box Contents:
                  </p>
                  <p className='text-sm text-gray-600'>
                    {nextDelivery.box_contents}
                  </p>
                </div>
              )}

              {nextDelivery.special_instructions && (
                <div className='bg-blue-50 p-3 rounded-lg'>
                  <p className='text-sm font-medium text-blue-700 mb-1'>
                    Special Instructions:
                  </p>
                  <p className='text-sm text-blue-600'>
                    {nextDelivery.special_instructions}
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className='p-6 text-center'>
            <Package className='h-12 w-12 text-gray-400 mx-auto mb-4' />
            <h3 className='text-lg font-medium text-gray-900 mb-2'>
              No Upcoming Deliveries
            </h3>
            <p className='text-gray-600'>
              You don&apos;t have any scheduled deliveries at the moment.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Delivery Stats */}
      {stats && (
        <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
          <Card>
            <CardContent className='p-4'>
              <div className='flex items-center gap-3'>
                <div className='p-2 bg-blue-50 rounded-lg'>
                  <Package className='h-5 w-5 text-blue-600' />
                </div>
                <div>
                  <p className='text-sm text-gray-600'>Total Deliveries</p>
                  <p className='text-xl font-bold'>{stats.totalDeliveries}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className='p-4'>
              <div className='flex items-center gap-3'>
                <div className='p-2 bg-green-50 rounded-lg'>
                  <CheckCircle className='h-5 w-5 text-green-600' />
                </div>
                <div>
                  <p className='text-sm text-gray-600'>Delivered</p>
                  <p className='text-xl font-bold'>{stats.deliveredCount}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className='p-4'>
              <div className='flex items-center gap-3'>
                <div className='p-2 bg-yellow-50 rounded-lg'>
                  <Clock className='h-5 w-5 text-yellow-600' />
                </div>
                <div>
                  <p className='text-sm text-gray-600'>Scheduled</p>
                  <p className='text-xl font-bold'>{stats.scheduledCount}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Upcoming Deliveries List */}
      {upcomingDeliveries.length > 1 && (
        <Card>
          <CardHeader>
            <CardTitle>Upcoming Deliveries</CardTitle>
          </CardHeader>
          <CardContent>
            <div className='space-y-3'>
              {upcomingDeliveries.slice(1).map((delivery) => (
                <div
                  key={delivery.id}
                  className='flex items-center justify-between p-3 bg-gray-50 rounded-lg'
                >
                  <div className='flex items-center gap-3'>
                    {getStatusIcon(delivery.status)}
                    <div>
                      <p className='font-medium'>
                        {new Date(delivery.delivery_date).toLocaleDateString()}
                      </p>
                      <p className='text-sm text-gray-600'>
                        {delivery.pickup_location
                          .replace('_', ' ')
                          .toUpperCase()}
                      </p>
                    </div>
                  </div>
                  {getStatusBadge(delivery.status)}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
