import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { authServer } from '@/lib/utils/auth-server';
import { getCurrentUserSubscriptionSummary } from '@/lib/services/server/subscription-server';
import { Leaf, Clock, MapPin, Star } from 'lucide-react';

export async function PersonalizedRecommendations() {
  const { data: user } = await authServer.getCurrentUser();

  if (!user) {
    return <GuestRecommendations />;
  }

  try {
    const subscriptionSummary = await getCurrentUserSubscriptionSummary();
    return (
      <UserRecommendations
        user={user}
        subscriptionSummary={subscriptionSummary}
      />
    );
  } catch {
    // If user has no subscription data, show new user recommendations
    return <NewUserRecommendations user={user} />;
  }
}

function GuestRecommendations() {
  return (
    <section className='py-12 bg-green-50'>
      <div className='max-w-7xl mx-auto px-4'>
        <div className='text-center mb-8'>
          <h2 className='text-3xl font-bold text-gray-900 mb-4'>
            Start Your Fresh Food Journey
          </h2>
          <p className='text-lg text-gray-600 max-w-2xl mx-auto'>
            Join thousands of families enjoying fresh, locally-sourced produce
            delivered weekly. Sign up to get personalized recommendations based
            on your preferences.
          </p>
        </div>

        <div className='grid md:grid-cols-3 gap-6 mb-8'>
          <Card className='text-center'>
            <CardHeader>
              <Leaf className='h-12 w-12 text-green-600 mx-auto mb-2' />
              <CardTitle className='text-lg'>Fresh & Local</CardTitle>
            </CardHeader>
            <CardContent>
              <p className='text-gray-600'>
                Handpicked produce from local farms, delivered fresh to your
                pickup location.
              </p>
            </CardContent>
          </Card>

          <Card className='text-center'>
            <CardHeader>
              <Clock className='h-12 w-12 text-green-600 mx-auto mb-2' />
              <CardTitle className='text-lg'>Flexible Plans</CardTitle>
            </CardHeader>
            <CardContent>
              <p className='text-gray-600'>
                Choose weekly or bi-weekly deliveries that fit your schedule and
                family size.
              </p>
            </CardContent>
          </Card>

          <Card className='text-center'>
            <CardHeader>
              <MapPin className='h-12 w-12 text-green-600 mx-auto mb-2' />
              <CardTitle className='text-lg'>Convenient Pickup</CardTitle>
            </CardHeader>
            <CardContent>
              <p className='text-gray-600'>
                Multiple pickup locations across the city for your convenience.
              </p>
            </CardContent>
          </Card>
        </div>

        <div className='text-center'>
          <div className='space-y-4 max-w-md mx-auto'>
            <Button asChild size='lg' className='w-full'>
              <Link href='/signup'>Get Started Today</Link>
            </Button>
            <p className='text-sm text-gray-500'>
              Already have an account?{' '}
              <Link
                href='/login'
                className='text-green-600 hover:text-green-700 font-medium'
              >
                Sign in
              </Link>
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}

function UserRecommendations({
  user,
  subscriptionSummary,
}: {
  user: any;
  subscriptionSummary: any;
}) {
  const { activeSubscription, subscriber } = subscriptionSummary;

  if (activeSubscription) {
    return (
      <section className='py-12 bg-green-50'>
        <div className='max-w-7xl mx-auto px-4'>
          <div className='text-center mb-8'>
            <h2 className='text-3xl font-bold text-gray-900 mb-4'>
              Welcome back, {subscriber.name}! 👋
            </h2>
            <p className='text-lg text-gray-600'>
              Your fresh produce journey continues. Here&apos;s what&apos;s
              happening with your subscription.
            </p>
          </div>

          <div className='grid md:grid-cols-2 gap-6 mb-8'>
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <Leaf className='h-5 w-5 text-green-600' />
                  Your Current Plan
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className='space-y-2'>
                  <div className='flex justify-between'>
                    <span className='text-gray-600'>Box Size:</span>
                    <Badge variant='outline'>
                      {activeSubscription.box_size}
                    </Badge>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-gray-600'>Frequency:</span>
                    <span className='font-medium'>
                      {activeSubscription.frequency}
                    </span>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-gray-600'>Pickup Location:</span>
                    <span className='font-medium'>
                      {activeSubscription.pickup_location
                        .replace('_', ' ')
                        .toUpperCase()}
                    </span>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-gray-600'>Deliveries Remaining:</span>
                    <span className='font-medium'>
                      {activeSubscription.deliveries_remaining}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <Clock className='h-5 w-5 text-green-600' />
                  Next Delivery
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className='text-center'>
                  <p className='text-2xl font-bold text-green-600 mb-2'>
                    {activeSubscription.next_delivery_date
                      ? new Date(
                          activeSubscription.next_delivery_date
                        ).toLocaleDateString('en-US', {
                          weekday: 'long',
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                        })
                      : 'To be scheduled'}
                  </p>
                  <p className='text-gray-600 mb-4'>
                    Your fresh produce box will be ready for pickup
                  </p>
                  <Button asChild variant='outline' className='w-full'>
                    <Link href='/dashboard'>View Details</Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className='text-center'>
            <h3 className='text-xl font-semibold text-gray-900 mb-4'>
              Recommended for You
            </h3>
            <div className='grid md:grid-cols-3 gap-4'>
              <Card className='text-center'>
                <CardContent className='p-4'>
                  <Star className='h-8 w-8 text-yellow-500 mx-auto mb-2' />
                  <h4 className='font-medium mb-2'>Upgrade Your Box</h4>
                  <p className='text-sm text-gray-600 mb-3'>
                    Get more variety with our Large box option
                  </p>
                  <Button size='sm' variant='outline'>
                    Learn More
                  </Button>
                </CardContent>
              </Card>

              <Card className='text-center'>
                <CardContent className='p-4'>
                  <Leaf className='h-8 w-8 text-green-500 mx-auto mb-2' />
                  <h4 className='font-medium mb-2'>Add-On Items</h4>
                  <p className='text-sm text-gray-600 mb-3'>
                    Fresh herbs and specialty items available
                  </p>
                  <Button size='sm' variant='outline'>
                    Browse Add-Ons
                  </Button>
                </CardContent>
              </Card>

              <Card className='text-center'>
                <CardContent className='p-4'>
                  <MapPin className='h-8 w-8 text-blue-500 mx-auto mb-2' />
                  <h4 className='font-medium mb-2'>Refer Friends</h4>
                  <p className='text-sm text-gray-600 mb-3'>
                    Get $10 off when friends join AsedaFoods
                  </p>
                  <Button size='sm' variant='outline'>
                    Refer Now
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return <NewUserRecommendations user={user} />;
}

function NewUserRecommendations({ user }: { user: any }) {
  return (
    <section className='py-12 bg-green-50'>
      <div className='max-w-7xl mx-auto px-4'>
        <div className='text-center mb-8'>
          <h2 className='text-3xl font-bold text-gray-900 mb-4'>
            Welcome to AsedaFoods, {user.name}! 🎉
          </h2>
          <p className='text-lg text-gray-600'>
            You&apos;re all set up! Now let&apos;s get you started with fresh,
            local produce.
          </p>
        </div>

        <div className='grid md:grid-cols-3 gap-6 mb-8'>
          <Card className='text-center border-2 border-green-200'>
            <CardHeader>
              <Badge className='mx-auto mb-2 bg-green-100 text-green-800'>
                Recommended
              </Badge>
              <CardTitle>Small Box</CardTitle>
              <p className='text-2xl font-bold text-green-600'>$25/week</p>
            </CardHeader>
            <CardContent>
              <ul className='text-sm text-gray-600 space-y-1 mb-4'>
                <li>Perfect for 1-2 people</li>
                <li>5-7 seasonal items</li>
                <li>Weekly or bi-weekly delivery</li>
              </ul>
              <Button className='w-full'>Choose Small Box</Button>
            </CardContent>
          </Card>

          <Card className='text-center'>
            <CardHeader>
              <CardTitle>Medium Box</CardTitle>
              <p className='text-2xl font-bold text-green-600'>$40/week</p>
            </CardHeader>
            <CardContent>
              <ul className='text-sm text-gray-600 space-y-1 mb-4'>
                <li>Great for 3-4 people</li>
                <li>8-10 seasonal items</li>
                <li>Weekly or bi-weekly delivery</li>
              </ul>
              <Button variant='outline' className='w-full'>
                Choose Medium Box
              </Button>
            </CardContent>
          </Card>

          <Card className='text-center'>
            <CardHeader>
              <CardTitle>Large Box</CardTitle>
              <p className='text-2xl font-bold text-green-600'>$55/week</p>
            </CardHeader>
            <CardContent>
              <ul className='text-sm text-gray-600 space-y-1 mb-4'>
                <li>Perfect for 5+ people</li>
                <li>12-15 seasonal items</li>
                <li>Weekly or bi-weekly delivery</li>
              </ul>
              <Button variant='outline' className='w-full'>
                Choose Large Box
              </Button>
            </CardContent>
          </Card>
        </div>

        <div className='text-center'>
          <Button asChild size='lg'>
            <Link href='/get-a-box'>Start Your Subscription</Link>
          </Button>
        </div>
      </div>
    </section>
  );
}
