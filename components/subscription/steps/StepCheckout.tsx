'use client';

import StripePaymentForm from '@/components/payment/StripePaymentForm';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import {
  BOX_SIZES,
  FREQUENCIES,
  PAYMENT_PLANS,
  PICKUP_LOCATIONS,
  SubscriptionData,
  calculatePrice,
  formatPrice,
  getNextDeliveryDate,
} from '@/lib/constants/subscription';
import { CreditCard } from 'lucide-react';
import { useState } from 'react';

interface StepCheckoutProps {
  subscriptionData: SubscriptionData;
  subscriberId: string;
  onComplete: (paymentData: any) => void;
  onUpdateData: (updates: Partial<SubscriptionData>) => void;
}

export default function StepCheckout({
  subscriptionData,
  subscriberId,
  onComplete,
  onUpdateData,
}: StepCheckoutProps) {
  const [specialInstructions, setSpecialInstructions] = useState(
    subscriptionData.specialInstructions || ''
  );
  const [showPayment, setShowPayment] = useState(false);
  const [paymentError, setPaymentError] = useState<string | null>(null);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [paymentCompleted, setPaymentCompleted] = useState(false);

  const shareSize =
    subscriptionData.shareSize || subscriptionData.boxSize || 'standard';

  const pricingResult = (() => {
    try {
      const pricing = calculatePrice(
        shareSize,
        subscriptionData.frequency,
        subscriptionData.paymentPlan
      );
      return { success: true as const, data: pricing };
    } catch (error) {
      console.error('Error calculating price in StepCheckout:', error);
      return { success: false as const, error };
    }
  })();

  if (!pricingResult.success) {
    return (
      <div className='text-center py-8'>
        <p className='text-red-500 mb-4'>
          Error loading subscription details. Please go back and complete the
          wizard again.
        </p>
      </div>
    );
  }

  const pricing = pricingResult.data;

  const nextDeliveryDate = (() => {
    try {
      return getNextDeliveryDate(subscriptionData.frequency);
    } catch (error) {
      console.error(
        'Error calculating next delivery date in StepCheckout:',
        error
      );
      return new Date(); // Fallback to current date
    }
  })();

  const handleProceedToPayment = () => {
    // Prevent multiple clicks
    if (showPayment || isProcessingPayment || paymentCompleted) {
      return;
    }

    // Update subscription data with special instructions
    onUpdateData({
      specialInstructions: specialInstructions.trim(),
    });
    setShowPayment(true);
    setPaymentError(null);
  };

  const handlePaymentSuccess = (paymentData: any) => {
    setIsProcessingPayment(false);
    setPaymentCompleted(true);
    setPaymentError(null);
    // Immediately call onComplete to trigger wizard completion UI
    onComplete(paymentData);
  };

  const handlePaymentError = (error: string) => {
    setIsProcessingPayment(false);
    setPaymentError(error);
    // Don't reset showPayment so user can try again
  };

  const handlePaymentStart = () => {
    setIsProcessingPayment(true);
    setPaymentError(null);
  };

  return (
    <div
      className={`space-y-6 ${isProcessingPayment ? 'pointer-events-none opacity-75' : ''}`}
    >
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
        {/* Pickup Information */}
        <div className='space-y-6'>
          <Card>
            <CardHeader>
              <CardTitle>Pickup Information</CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div>
                <Label>Pickup Location</Label>
                <div className='mt-2 p-3 bg-gray-50 rounded-lg'>
                  <div className='font-medium'>
                    {PICKUP_LOCATIONS[subscriptionData.pickupLocation].name}
                  </div>
                  <div className='text-sm text-gray-600 mt-1'>
                    {PICKUP_LOCATIONS[subscriptionData.pickupLocation].address}
                  </div>
                </div>
              </div>

              <div>
                <Label htmlFor='specialInstructions'>
                  Special Instructions (Optional)
                </Label>
                <Textarea
                  id='specialInstructions'
                  placeholder='Any special pickup instructions, dietary restrictions, or preferences...'
                  value={specialInstructions}
                  onChange={(e) => setSpecialInstructions(e.target.value)}
                  rows={3}
                  className='mt-1'
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Order Summary */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Order Summary</CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              {/* Subscription Details */}
              <div className='space-y-3'>
                <div className='flex justify-between'>
                  <span className='text-sm text-gray-600'>Box Size:</span>
                  <span className='font-medium'>
                    {BOX_SIZES[shareSize].name}
                  </span>
                </div>

                <div className='flex justify-between'>
                  <span className='text-sm text-gray-600'>Frequency:</span>
                  <span className='font-medium'>
                    {FREQUENCIES[subscriptionData.frequency].name}
                  </span>
                </div>

                <div className='flex justify-between'>
                  <span className='text-sm text-gray-600'>
                    Pickup Location:
                  </span>
                  <span className='font-medium'>
                    {PICKUP_LOCATIONS[subscriptionData.pickupLocation].name}
                  </span>
                </div>

                <div className='flex justify-between'>
                  <span className='text-sm text-gray-600'>Payment Plan:</span>
                  <span className='font-medium'>
                    {PAYMENT_PLANS[subscriptionData.paymentPlan].name}
                  </span>
                </div>

                <div className='flex justify-between'>
                  <span className='text-sm text-gray-600'>Next Pickup:</span>
                  <span className='font-medium'>
                    {nextDeliveryDate.toLocaleDateString()}
                  </span>
                </div>
              </div>

              <Separator />

              {/* Pricing Breakdown */}
              <div className='space-y-2'>
                <div className='flex justify-between'>
                  <span className='text-sm text-gray-600'>
                    Base price per box:
                  </span>
                  <span>{formatPrice(pricing.basePrice)}</span>
                </div>

                {pricing.totalDiscount > 0 && (
                  <div className='flex justify-between text-green-600'>
                    <span className='text-sm'>
                      Discount ({pricing.totalDiscount}%):
                    </span>
                    <span>-{formatPrice(pricing.discountAmount)}</span>
                  </div>
                )}

                <div className='flex justify-between font-medium'>
                  <span className='text-sm'>Price per box:</span>
                  <span>{formatPrice(pricing.pricePerBox)}</span>
                </div>

                <div className='flex justify-between'>
                  <span className='text-sm text-gray-600'>
                    Number of pickups:
                  </span>
                  <span>
                    {PAYMENT_PLANS[subscriptionData.paymentPlan].deliveries}
                  </span>
                </div>
              </div>

              <Separator />

              {/* Total */}
              <div className='space-y-2'>
                <div className='flex justify-between text-lg font-semibold'>
                  <span>Total:</span>
                  <span>{formatPrice(pricing.totalPrice)}</span>
                </div>

                {pricing.savings > 0 && (
                  <div className='flex justify-between text-green-600 font-medium'>
                    <span>You save:</span>
                    <span>{formatPrice(pricing.savings)}</span>
                  </div>
                )}
              </div>

              {/* Discount Badge */}
              {pricing.totalDiscount > 0 && (
                <div className='pt-2'>
                  <Badge className='bg-green-100 text-green-800'>
                    {pricing.totalDiscount}% Total Discount Applied
                  </Badge>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Payment Section */}
      {!showPayment ? (
        <Card>
          <CardContent className='pt-6'>
            <div className='space-y-4'>
              <div className='text-sm text-gray-600 space-y-2'>
                <p>
                  <strong>Auto-renewal:</strong> Your subscription will
                  automatically renew after{' '}
                  {PAYMENT_PLANS[subscriptionData.paymentPlan].deliveries}{' '}
                  pickups unless cancelled.
                </p>
                <p>
                  <strong>Cancellation:</strong> You can pause or cancel your
                  subscription at any time from your dashboard.
                </p>
                <p>
                  <strong>Payment:</strong> You will be charged the full amount
                  upfront for your selected payment plan.
                </p>
              </div>

              <Button
                onClick={handleProceedToPayment}
                disabled={
                  showPayment || isProcessingPayment || paymentCompleted
                }
                className='w-full bg-green-600 hover:bg-green-700 text-white py-3 disabled:opacity-50 disabled:cursor-not-allowed'
                size='lg'
              >
                {showPayment || isProcessingPayment ? (
                  <>
                    <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2'></div>
                    {isProcessingPayment
                      ? 'Processing Payment...'
                      : 'Loading Payment Form...'}
                  </>
                ) : paymentCompleted ? (
                  <>
                    <CreditCard className='mr-2 h-4 w-4' />
                    Payment Completed
                  </>
                ) : (
                  <>
                    <CreditCard className='mr-2 h-4 w-4' />
                    Proceed to Payment - {formatPrice(pricing.totalPrice)}
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <>
          {paymentError && (
            <Alert variant='destructive'>
              <AlertDescription>{paymentError}</AlertDescription>
            </Alert>
          )}

          <StripePaymentForm
            subscriptionData={subscriptionData}
            subscriberId={subscriberId}
            onSuccess={handlePaymentSuccess}
            onError={handlePaymentError}
            onPaymentStart={handlePaymentStart}
            isProcessing={isProcessingPayment}
          />
        </>
      )}

      {/* Processing Overlay */}
      {isProcessingPayment && (
        <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
          <div className='bg-white rounded-lg p-6 max-w-sm mx-4 text-center'>
            <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4'></div>
            <h3 className='text-lg font-semibold text-gray-900 mb-2'>
              Processing Payment
            </h3>
            <p className='text-gray-600 text-sm'>
              Please don&apos;t close this window or navigate away. Your payment
              is being processed securely.
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
