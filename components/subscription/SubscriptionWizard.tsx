'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useSubscriptionStore } from '@/lib/store/subscription';
import { AuthUser } from '@/lib/utils/auth';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import StepAuthentication from './steps/StepAuthentication';
import StepBoxSelection from './steps/StepBoxSelection';
import StepPaymentPlan from './steps/StepPaymentPlan';

interface SubscriptionWizardProps {
  onCancel?: () => void;
  subscriberId: string | null;
  user: AuthUser | null | undefined;
}

const STEPS = [
  {
    id: 1,
    title: 'Choose Your Box',
    description: 'Select delivery frequency and pickup location',
  },
  {
    id: 2,
    title: 'Payment Plan',
    description: 'Choose your payment plan and save money',
  },
  {
    id: 3,
    title: 'Sign In',
    description: 'Create account or sign in to continue',
  },
];

export default function SubscriptionWizard({
  onCancel,
  subscriberId,
  user,
}: SubscriptionWizardProps) {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);

  const {
    subscriptionData,
    updateSubscriptionData,
    setSubscriberId,
    canProceedToCheckout,
  } = useSubscriptionStore();

  // Set subscriber ID when component mounts and user is available
  useEffect(() => {
    if (subscriberId) {
      setSubscriberId(subscriberId);
    }
  }, [subscriberId, setSubscriberId]);

  const canProceedToNext = () => {
    switch (currentStep) {
      case 1:
        return (
          (subscriptionData.shareSize || subscriptionData.boxSize) &&
          subscriptionData.frequency &&
          subscriptionData.pickupLocation
        );
      case 2:
        return subscriptionData.paymentPlan;
      case 3:
        return user && subscriberId; // User must be authenticated and have subscriber ID
      default:
        return false;
    }
  };

  // Skip authentication step if user is already logged in
  const getEffectiveSteps = () => {
    if (user && subscriberId) {
      return STEPS.filter((step) => step.id !== 3);
    }
    return STEPS;
  };

  const effectiveSteps = getEffectiveSteps();

  const handleNext = () => {
    if (user && subscriberId && currentStep === 2) {
      // Skip authentication step if user is already logged in
      router.push('/get-a-box/checkout');
      return;
    }

    if (currentStep < effectiveSteps.length && canProceedToNext()) {
      const nextStepId =
        effectiveSteps[
          effectiveSteps.findIndex((s) => s.id === currentStep) + 1
        ]?.id;
      if (nextStepId) {
        setCurrentStep(nextStepId);
      }
    }
  };

  const handleProceedToCheckout = () => {
    if (canProceedToCheckout()) {
      router.push('/get-a-box/checkout');
    }
  };

  const handlePrevious = () => {
    const currentIndex = effectiveSteps.findIndex((s) => s.id === currentStep);
    if (currentIndex > 0) {
      setCurrentStep(effectiveSteps[currentIndex - 1].id);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <StepBoxSelection
            selectedShareSize={subscriptionData.shareSize}
            selectedFrequency={subscriptionData.frequency}
            selectedPickupLocation={subscriptionData.pickupLocation}
            onShareSizeChange={(shareSize) =>
              updateSubscriptionData({ shareSize })
            }
            onFrequencyChange={(frequency) =>
              updateSubscriptionData({ frequency })
            }
            onPickupLocationChange={(pickupLocation) =>
              updateSubscriptionData({ pickupLocation })
            }
          />
        );
      case 2:
        return (
          <StepPaymentPlan
            selectedShareSize={subscriptionData.shareSize}
            selectedFrequency={subscriptionData.frequency}
            selectedPaymentPlan={subscriptionData.paymentPlan}
            onPaymentPlanChange={(paymentPlan) =>
              updateSubscriptionData({ paymentPlan })
            }
          />
        );
      case 3:
        return (
          <StepAuthentication user={user} subscriptionData={subscriptionData} />
        );
      default:
        return null;
    }
  };

  return (
    <div className='max-w-4xl mx-auto p-6'>
      {/* Progress Steps */}
      <div className='mb-8'>
        <div className='flex items-center justify-between'>
          {effectiveSteps.map((step, index) => (
            <div key={step.id} className='flex items-center'>
              <div className='flex flex-col items-center'>
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium ${
                    currentStep >= step.id
                      ? 'bg-green-600 text-white'
                      : 'bg-gray-200 text-gray-600'
                  }`}
                >
                  {index + 1}
                </div>
                <div className='mt-2 text-center'>
                  <div className='text-sm font-medium text-gray-900'>
                    {step.title}
                  </div>
                  <div className='text-xs text-gray-500'>
                    {step.description}
                  </div>
                </div>
              </div>
              {index < effectiveSteps.length - 1 && (
                <div
                  className={`flex-1 h-0.5 mx-4 ${
                    effectiveSteps.findIndex((s) => s.id === currentStep) >
                    index
                      ? 'bg-green-600'
                      : 'bg-gray-200'
                  }`}
                />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Step Content */}
      <Card>
        <CardHeader>
          <CardTitle>
            {effectiveSteps.find((s) => s.id === currentStep)?.title}
          </CardTitle>
        </CardHeader>
        <CardContent>{renderStep()}</CardContent>
      </Card>

      {/* Navigation */}
      <div className='flex justify-between mt-6'>
        <div>
          {effectiveSteps.findIndex((s) => s.id === currentStep) > 0 && (
            <Button variant='outline' onClick={handlePrevious}>
              <ChevronLeft className='w-4 h-4 mr-2' />
              Previous
            </Button>
          )}
          {onCancel &&
            effectiveSteps.findIndex((s) => s.id === currentStep) === 0 && (
              <Button variant='outline' onClick={onCancel}>
                Cancel
              </Button>
            )}
        </div>

        <div>
          {currentStep < effectiveSteps[effectiveSteps.length - 1].id ? (
            <Button onClick={handleNext} disabled={!canProceedToNext()}>
              {currentStep === 2 && user && subscriberId
                ? 'Proceed to Checkout'
                : 'Next'}
              <ChevronRight className='w-4 h-4 ml-2' />
            </Button>
          ) : (
            <Button
              onClick={handleProceedToCheckout}
              disabled={!canProceedToCheckout()}
              className='bg-green-600 hover:bg-green-700'
            >
              Proceed to Checkout
              <ChevronRight className='w-4 h-4 ml-2' />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
