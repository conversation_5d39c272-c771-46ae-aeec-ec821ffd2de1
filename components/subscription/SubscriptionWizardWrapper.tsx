'use client';

import SubscriptionWizard from '@/components/subscription/SubscriptionWizard';
import { AuthUser } from '@/lib/utils/auth';

interface Subscriber {
  id: string;
  // Add other subscriber fields as needed
}

export function GuestSubscriptionWizard() {
  return (
    <SubscriptionWizard
      onCancel={() => (window.location.href = '/')}
      subscriberId={null}
      user={null}
    />
  );
}

export function AuthenticatedSubscriptionWizard({
  subscriber,
  user,
}: {
  subscriber: Subscriber | null | undefined;
  user: AuthUser | null | undefined;
}) {
  return (
    <SubscriptionWizard
      onCancel={() => (window.location.href = '/')}
      subscriberId={subscriber?.id || null}
      user={user}
    />
  );
}
