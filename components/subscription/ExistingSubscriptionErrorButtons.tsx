'use client';

import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';

export function ExistingSubscriptionErrorButtons() {
  const router = useRouter();

  return (
    <div className='gap-3 flex flex-wrap justify-center'>
      <Button
        onClick={() => router.push('/dashboard')}
        className='w-full sm:w-auto'
      >
        Go to Dashboard
      </Button>
      <Button
        variant='outline'
        onClick={() => window.location.reload()}
        className='w-full sm:w-auto'
      >
        Check Again
      </Button>
    </div>
  );
}
