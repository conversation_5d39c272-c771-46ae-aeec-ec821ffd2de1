'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  BOX_SIZES,
  FREQUENCIES,
  PAYMENT_PLANS,
  PICKUP_LOCATIONS,
  SubscriptionData,
  calculatePrice,
  formatPrice,
} from '@/lib/constants/subscription';
import {
  ArrowRight,
  Calendar,
  CheckCircle,
  CreditCard,
  MapPin,
  Package,
} from 'lucide-react';
import Link from 'next/link';

interface SubscriptionSuccessProps {
  subscriptionData: SubscriptionData;
  paymentData: any;
}

export default function SubscriptionSuccess({
  subscriptionData,
}: SubscriptionSuccessProps) {
  const shareSize =
    subscriptionData.shareSize || subscriptionData.boxSize || 'standard';

  const pricingResult = (() => {
    try {
      const pricing = calculatePrice(
        shareSize,
        subscriptionData.frequency,
        subscriptionData.paymentPlan
      );
      return { success: true as const, data: pricing };
    } catch (error) {
      console.error('Error calculating price in SubscriptionSuccess:', error);
      return { success: false as const, error };
    }
  })();

  if (!pricingResult.success) {
    return (
      <div className='text-center py-8'>
        <p className='text-red-500'>
          Error loading subscription details. Please contact support.
        </p>
      </div>
    );
  }

  const pricing = pricingResult.data;

  const shareSizeInfo = BOX_SIZES[shareSize];
  const frequencyInfo = FREQUENCIES[subscriptionData.frequency];
  const pickupLocationInfo = PICKUP_LOCATIONS[subscriptionData.pickupLocation];
  const paymentPlanInfo = PAYMENT_PLANS[subscriptionData.paymentPlan];

  return (
    <div className='max-w-4xl mx-auto p-6'>
      <Card>
        <CardContent className='pt-6'>
          <div className='text-center py-8'>
            <CheckCircle className='h-20 w-20 text-green-600 mx-auto mb-6' />
            <h2 className='text-3xl font-bold text-gray-900 mb-4'>
              Payment Successful!
            </h2>
            <p className='text-lg text-gray-600 mb-8'>
              Your subscription has been created successfully. You&apos;ll
              receive fresh, seasonal produce according to your preferences.
            </p>

            {/* Subscription Summary */}
            <div className='bg-green-50 border border-green-200 rounded-lg p-6 max-w-2xl mx-auto mb-8'>
              <h3 className='text-xl font-semibold text-green-800 mb-6'>
                Subscription Summary
              </h3>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-6 text-left'>
                {/* Share Details */}
                <div className='space-y-4'>
                  <div className='flex items-start gap-3'>
                    <Package className='h-5 w-5 text-green-600 mt-0.5' />
                    <div>
                      <div className='font-medium text-green-800'>
                        Share Type
                      </div>
                      <div className='text-green-700'>{shareSizeInfo.name}</div>
                      <div className='text-sm text-green-600'>
                        {shareSizeInfo.description}
                      </div>
                    </div>
                  </div>

                  <div className='flex items-start gap-3'>
                    <Calendar className='h-5 w-5 text-green-600 mt-0.5' />
                    <div>
                      <div className='font-medium text-green-800'>
                        Delivery Frequency
                      </div>
                      <div className='text-green-700'>{frequencyInfo.name}</div>
                      <div className='text-sm text-green-600'>
                        {frequencyInfo.description}
                      </div>
                    </div>
                  </div>

                  <div className='flex items-start gap-3'>
                    <MapPin className='h-5 w-5 text-green-600 mt-0.5' />
                    <div>
                      <div className='font-medium text-green-800'>
                        Pickup Location
                      </div>
                      <div className='text-green-700'>
                        {pickupLocationInfo.name}
                      </div>
                      <div className='text-sm text-green-600'>
                        {pickupLocationInfo.address}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Payment Details */}
                <div className='space-y-4'>
                  <div className='flex items-start gap-3'>
                    <CreditCard className='h-5 w-5 text-green-600 mt-0.5' />
                    <div>
                      <div className='font-medium text-green-800'>
                        Payment Plan
                      </div>
                      <div className='text-green-700'>
                        {paymentPlanInfo.name}
                      </div>
                      <div className='text-sm text-green-600'>
                        {paymentPlanInfo.description}
                      </div>
                    </div>
                  </div>

                  <div className='bg-white rounded-lg p-4 border border-green-200'>
                    <div className='space-y-2'>
                      <div className='flex justify-between'>
                        <span className='text-sm text-gray-600'>
                          Total Paid:
                        </span>
                        <span className='font-semibold text-green-800'>
                          {formatPrice(pricing.totalPrice)}
                        </span>
                      </div>
                      {pricing.savings > 0 && (
                        <div className='flex justify-between'>
                          <span className='text-sm text-gray-600'>
                            You Saved:
                          </span>
                          <span className='font-semibold text-green-600'>
                            {formatPrice(pricing.savings)}
                          </span>
                        </div>
                      )}
                      <div className='flex justify-between'>
                        <span className='text-sm text-gray-600'>
                          Number of Pickups:
                        </span>
                        <span className='text-green-700'>
                          {paymentPlanInfo.deliveries}
                        </span>
                      </div>
                    </div>
                  </div>

                  {pricing.totalDiscount > 0 && (
                    <Badge className='bg-green-100 text-green-800 border-green-200'>
                      {pricing.totalDiscount}% Total Discount Applied
                    </Badge>
                  )}
                </div>
              </div>

              {subscriptionData.specialInstructions && (
                <div className='mt-6 pt-4 border-t border-green-200'>
                  <div className='text-left'>
                    <div className='font-medium text-green-800 mb-2'>
                      Special Instructions
                    </div>
                    <div className='text-green-700 text-sm bg-white rounded p-3 border border-green-200'>
                      {subscriptionData.specialInstructions}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className='flex flex-col sm:flex-row gap-4 justify-center'>
              <Link href='/dashboard'>
                <Button size='lg' className='bg-green-600 hover:bg-green-700'>
                  View Dashboard
                  <ArrowRight className='ml-2 h-4 w-4' />
                </Button>
              </Link>
              <Link href='/dashboard'>
                <Button variant='outline' size='lg'>
                  View Subscription History
                </Button>
              </Link>
            </div>

            {/* Next Steps */}
            <div className='mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg max-w-2xl mx-auto'>
              <h4 className='font-semibold text-blue-800 mb-2'>
                What&apos;s Next?
              </h4>
              <div className='text-sm text-blue-700 space-y-1'>
                <p>• You&apos;ll receive an email confirmation shortly</p>
                <p>
                  • Your first pickup will be available according to your
                  selected frequency
                </p>
                <p>
                  • You can manage your subscription, pause, or cancel anytime
                  from your dashboard
                </p>
                <p>
                  • Set up your produce preferences to avoid items you
                  don&apos;t like
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
