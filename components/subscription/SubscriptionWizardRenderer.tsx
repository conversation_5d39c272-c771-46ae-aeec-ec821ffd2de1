'use client';

import {
  GuestSubscriptionWizard,
  AuthenticatedSubscriptionWizard,
} from './SubscriptionWizardWrapper';
import { AuthUser } from '@/lib/utils/auth';

interface Subscriber {
  // Define subscriber properties based on your data model
  id: string;
  // Add other subscriber fields
}

interface SubscriptionWizardRendererProps {
  userType: 'guest' | 'authenticated';
  subscriber?: Subscriber | null;
  user?: AuthUser | null;
}

export function SubscriptionWizardRenderer({
  userType,
  subscriber,
  user,
}: SubscriptionWizardRendererProps) {
  if (userType === 'guest') {
    return <GuestSubscriptionWizard />;
  }

  return (
    <AuthenticatedSubscriptionWizard subscriber={subscriber} user={user} />
  );
}
