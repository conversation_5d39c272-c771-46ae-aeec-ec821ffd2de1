'use client';

import { useState } from 'react';
import { format } from 'date-fns';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ContactSubmissionWithStatus } from '@/lib/services/admin/contact-forms';
import { Mail, Calendar, MessageSquare } from 'lucide-react';

interface ContactFormViewDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  contactForm: ContactSubmissionWithStatus | null;
  onMarkAsRead?: (contact: ContactSubmissionWithStatus) => Promise<void> | void;
  onDelete?: (contact: ContactSubmissionWithStatus) => Promise<void> | void;
  isLoading?: boolean;
}

export function ContactFormViewDialog({
  open,
  onOpenChange,
  contactForm,
  onMarkAsRead,
  onDelete,
  isLoading = false,
}: ContactFormViewDialogProps) {
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  if (!contactForm) return null;

  const handleMarkAsRead = async () => {
    if (!onMarkAsRead) return;

    setIsUpdating(true);
    try {
      const result = onMarkAsRead(contactForm);
      if (result instanceof Promise) {
        await result;
      }
    } catch (error) {
      console.error('Error marking as read:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDelete = async () => {
    if (!onDelete) return;

    setIsDeleting(true);
    try {
      const result = onDelete(contactForm);
      if (result instanceof Promise) {
        await result;
      }
      onOpenChange(false);
    } catch (error) {
      console.error('Error deleting contact form:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new':
        return 'bg-blue-100 text-blue-800';
      case 'read':
        return 'bg-green-100 text-green-800';
      case 'responded':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-[600px] max-h-[80vh] overflow-y-auto'>
        <DialogHeader>
          <div className='flex items-center justify-between'>
            <DialogTitle>Contact Form Details</DialogTitle>
            <Badge className={getStatusColor(contactForm.status || 'new')}>
              {contactForm.status || 'new'}
            </Badge>
          </div>
          <DialogDescription>
            Submitted on{' '}
            {contactForm.created_at
              ? format(new Date(contactForm.created_at), 'PPP')
              : 'Unknown date'}
          </DialogDescription>
        </DialogHeader>

        <div className='space-y-6'>
          {/* Contact Information */}
          <div className='space-y-4'>
            <h3 className='text-lg font-semibold'>Contact Information</h3>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div className='flex items-center gap-3'>
                <div className='flex h-8 w-8 items-center justify-center rounded-full bg-blue-100'>
                  <Mail className='h-4 w-4 text-blue-600' />
                </div>
                <div>
                  <p className='text-sm font-medium text-gray-900'>
                    {contactForm.name}
                  </p>
                  <p className='text-sm text-gray-500'>{contactForm.email}</p>
                </div>
              </div>

              <div className='flex items-center gap-3'>
                <div className='flex h-8 w-8 items-center justify-center rounded-full bg-purple-100'>
                  <Calendar className='h-4 w-4 text-purple-600' />
                </div>
                <div>
                  <p className='text-sm font-medium text-gray-900'>Submitted</p>
                  <p className='text-sm text-gray-500'>
                    {contactForm.created_at
                      ? format(new Date(contactForm.created_at), 'PPp')
                      : 'Unknown date'}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Message */}
          <div className='space-y-3'>
            <div className='flex items-center gap-2'>
              <MessageSquare className='h-5 w-5 text-gray-600' />
              <h3 className='text-lg font-semibold'>Message</h3>
            </div>
            <div className='rounded-lg bg-gray-50 p-4'>
              <p className='text-sm text-gray-900 whitespace-pre-wrap'>
                {contactForm.message}
              </p>
            </div>
          </div>
        </div>

        <DialogFooter className='gap-2'>
          <Button
            variant='outline'
            onClick={() => onOpenChange(false)}
            disabled={isUpdating || isDeleting || isLoading}
          >
            Close
          </Button>

          {onMarkAsRead && (contactForm.status || 'new') === 'new' && (
            <Button
              variant='secondary'
              onClick={handleMarkAsRead}
              disabled={isUpdating || isDeleting || isLoading}
            >
              {isUpdating ? 'Marking...' : 'Mark as Read'}
            </Button>
          )}

          {onDelete && (
            <Button
              variant='destructive'
              onClick={handleDelete}
              disabled={isUpdating || isDeleting || isLoading}
            >
              {isDeleting ? 'Deleting...' : 'Delete'}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
