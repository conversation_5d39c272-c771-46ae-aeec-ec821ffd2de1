'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
// Note: Calendar and Popover components would be imported here if available
// import { Calendar } from '@/components/ui/calendar';
// import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { Search, X, Filter, RotateCcw } from 'lucide-react';
import {
  ORDER_STATUSES,
  PAYMENT_STATUSES,
  PICKUP_STATUSES,
  getOrderStatusDisplay,
  getPaymentStatusDisplay,
  getPickupStatusDisplay,
} from '@/lib/field-day-2025-config';

interface FilterState {
  search: string;
  orderStatus: string;
  paymentStatus: string;
  pickupStatus: string;
  startDate: Date | undefined;
  endDate: Date | undefined;
}

export function FieldDay2025OrdersFilters() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [filters, setFilters] = useState<FilterState>({
    search: searchParams.get('search') || '',
    orderStatus: searchParams.get('orderStatus') || '',
    paymentStatus: searchParams.get('paymentStatus') || '',
    pickupStatus: searchParams.get('pickupStatus') || '',
    startDate: searchParams.get('startDate')
      ? new Date(searchParams.get('startDate')!)
      : undefined,
    endDate: searchParams.get('endDate')
      ? new Date(searchParams.get('endDate')!)
      : undefined,
  });

  const [activeFiltersCount, setActiveFiltersCount] = useState(0);

  // Update active filters count
  useEffect(() => {
    let count = 0;
    if (filters.search) count++;
    if (filters.orderStatus) count++;
    if (filters.paymentStatus) count++;
    if (filters.pickupStatus) count++;
    if (filters.startDate) count++;
    if (filters.endDate) count++;
    setActiveFiltersCount(count);
  }, [filters]);

  // Apply filters to URL
  const applyFilters = () => {
    const params = new URLSearchParams();

    if (filters.search) params.set('search', filters.search);
    if (filters.orderStatus) params.set('orderStatus', filters.orderStatus);
    if (filters.paymentStatus)
      params.set('paymentStatus', filters.paymentStatus);
    if (filters.pickupStatus) params.set('pickupStatus', filters.pickupStatus);
    if (filters.startDate)
      params.set('startDate', filters.startDate.toISOString().split('T')[0]);
    if (filters.endDate)
      params.set('endDate', filters.endDate.toISOString().split('T')[0]);

    router.push(`/admin/field-day-2025/orders?${params.toString()}`);
  };

  // Clear all filters
  const clearFilters = () => {
    setFilters({
      search: '',
      orderStatus: '',
      paymentStatus: '',
      pickupStatus: '',
      startDate: undefined,
      endDate: undefined,
    });
    router.push('/admin/field-day-2025/orders');
  };

  // Update individual filter
  const updateFilter = (key: keyof FilterState, value: any) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
  };

  return (
    <div className='space-y-4'>
      {/* Search and Primary Filters */}
      <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
        {/* Search */}
        <div className='space-y-2'>
          <Label htmlFor='search'>Search Orders</Label>
          <div className='relative'>
            <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400' />
            <Input
              id='search'
              placeholder='Order number, name, email...'
              value={filters.search}
              onChange={(e) => updateFilter('search', e.target.value)}
              className='pl-10'
            />
          </div>
        </div>

        {/* Order Status */}
        <div className='space-y-2'>
          <Label>Order Status</Label>
          <Select
            value={filters.orderStatus}
            onValueChange={(value) => updateFilter('orderStatus', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder='All statuses' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value=''>All statuses</SelectItem>
              {Object.entries(ORDER_STATUSES).map(([key, value]) => (
                <SelectItem key={key} value={value}>
                  {getOrderStatusDisplay(value).label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Payment Status */}
        <div className='space-y-2'>
          <Label>Payment Status</Label>
          <Select
            value={filters.paymentStatus}
            onValueChange={(value) => updateFilter('paymentStatus', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder='All payments' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value=''>All payments</SelectItem>
              {Object.entries(PAYMENT_STATUSES).map(([key, value]) => (
                <SelectItem key={key} value={value}>
                  {getPaymentStatusDisplay(value).label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Pickup Status */}
        <div className='space-y-2'>
          <Label>Pickup Status</Label>
          <Select
            value={filters.pickupStatus}
            onValueChange={(value) => updateFilter('pickupStatus', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder='All pickup' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value=''>All pickup</SelectItem>
              {Object.entries(PICKUP_STATUSES).map(([key, value]) => (
                <SelectItem key={key} value={value}>
                  {getPickupStatusDisplay(value).label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Date Range Filters */}
      <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
        {/* Start Date */}
        <div className='space-y-2'>
          <Label>Start Date</Label>
          <Input
            type='date'
            value={
              filters.startDate
                ? filters.startDate.toISOString().split('T')[0]
                : ''
            }
            onChange={(e) =>
              updateFilter(
                'startDate',
                e.target.value ? new Date(e.target.value) : null
              )
            }
            className='w-full'
          />
        </div>

        {/* End Date */}
        <div className='space-y-2'>
          <Label>End Date</Label>
          <Input
            type='date'
            value={
              filters.endDate ? filters.endDate.toISOString().split('T')[0] : ''
            }
            onChange={(e) =>
              updateFilter(
                'endDate',
                e.target.value ? new Date(e.target.value) : null
              )
            }
            className='w-full'
          />
        </div>
      </div>

      {/* Active Filters Display */}
      {activeFiltersCount > 0 && (
        <div className='flex flex-wrap items-center gap-2'>
          <span className='text-sm font-medium text-gray-700'>
            Active filters:
          </span>

          {filters.search && (
            <Badge variant='secondary' className='gap-1'>
              Search: {filters.search}
              <X
                className='h-3 w-3 cursor-pointer'
                onClick={() => updateFilter('search', '')}
              />
            </Badge>
          )}

          {filters.orderStatus && (
            <Badge variant='secondary' className='gap-1'>
              Order: {getOrderStatusDisplay(filters.orderStatus as any).label}
              <X
                className='h-3 w-3 cursor-pointer'
                onClick={() => updateFilter('orderStatus', '')}
              />
            </Badge>
          )}

          {filters.paymentStatus && (
            <Badge variant='secondary' className='gap-1'>
              Payment:{' '}
              {getPaymentStatusDisplay(filters.paymentStatus as any).label}
              <X
                className='h-3 w-3 cursor-pointer'
                onClick={() => updateFilter('paymentStatus', '')}
              />
            </Badge>
          )}

          {filters.pickupStatus && (
            <Badge variant='secondary' className='gap-1'>
              Pickup:{' '}
              {getPickupStatusDisplay(filters.pickupStatus as any).label}
              <X
                className='h-3 w-3 cursor-pointer'
                onClick={() => updateFilter('pickupStatus', '')}
              />
            </Badge>
          )}

          {filters.startDate && (
            <Badge variant='secondary' className='gap-1'>
              From: {filters.startDate.toLocaleDateString()}
              <X
                className='h-3 w-3 cursor-pointer'
                onClick={() => updateFilter('startDate', undefined)}
              />
            </Badge>
          )}

          {filters.endDate && (
            <Badge variant='secondary' className='gap-1'>
              To: {filters.endDate.toLocaleDateString()}
              <X
                className='h-3 w-3 cursor-pointer'
                onClick={() => updateFilter('endDate', undefined)}
              />
            </Badge>
          )}
        </div>
      )}

      {/* Action Buttons */}
      <div className='flex justify-between items-center pt-2'>
        <div className='text-sm text-gray-600'>
          {activeFiltersCount > 0 && (
            <span>
              {activeFiltersCount} filter{activeFiltersCount !== 1 ? 's' : ''}{' '}
              active
            </span>
          )}
        </div>

        <div className='flex gap-2'>
          <Button
            variant='outline'
            size='sm'
            onClick={clearFilters}
            disabled={activeFiltersCount === 0}
          >
            <RotateCcw className='mr-2 h-4 w-4' />
            Clear All
          </Button>

          <Button size='sm' onClick={applyFilters}>
            <Filter className='mr-2 h-4 w-4' />
            Apply Filters
          </Button>
        </div>
      </div>
    </div>
  );
}
