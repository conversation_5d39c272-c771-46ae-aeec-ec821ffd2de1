import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

export function FieldDay2025OrdersSkeleton() {
  return (
    <div className='space-y-6'>
      {/* Statistics Cards Skeleton */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
        {[1, 2, 3, 4].map((i) => (
          <Card key={i}>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <Skeleton className='h-4 w-24' />
              <Skeleton className='h-4 w-4 rounded' />
            </CardHeader>
            <CardContent>
              <Skeleton className='h-8 w-16 mb-2' />
              <Skeleton className='h-3 w-20' />
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Status Breakdown Cards Skeleton */}
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
        {[1, 2].map((i) => (
          <Card key={i}>
            <CardHeader>
              <Skeleton className='h-6 w-48' />
            </CardHeader>
            <CardContent className='space-y-3'>
              {[1, 2, 3, 4, 5].map((j) => (
                <div key={j} className='flex items-center justify-between'>
                  <div className='flex items-center gap-2'>
                    <Skeleton className='h-4 w-4 rounded-full' />
                    <Skeleton className='h-4 w-24' />
                  </div>
                  <Skeleton className='h-5 w-8 rounded-full' />
                </div>
              ))}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Orders Table Skeleton */}
      <Card>
        <CardHeader>
          <div className='flex justify-between items-center'>
            <div>
              <Skeleton className='h-6 w-32 mb-2' />
              <Skeleton className='h-4 w-24' />
            </div>
            <Skeleton className='h-9 w-20' />
          </div>
        </CardHeader>
        <CardContent>
          {/* Table Header */}
          <div className='grid grid-cols-8 gap-4 pb-4 border-b'>
            <Skeleton className='h-4 w-20' />
            <Skeleton className='h-4 w-16' />
            <Skeleton className='h-4 w-12' />
            <Skeleton className='h-4 w-12' />
            <Skeleton className='h-4 w-16' />
            <Skeleton className='h-4 w-14' />
            <Skeleton className='h-4 w-12' />
            <Skeleton className='h-4 w-16' />
          </div>

          {/* Table Rows */}
          {[1, 2, 3, 4, 5].map((i) => (
            <div
              key={i}
              className='grid grid-cols-8 gap-4 py-4 border-b last:border-b-0'
            >
              <Skeleton className='h-4 w-24' />
              <div className='space-y-1'>
                <Skeleton className='h-4 w-20' />
                <Skeleton className='h-3 w-32' />
              </div>
              <Skeleton className='h-4 w-8' />
              <Skeleton className='h-4 w-12' />
              <Skeleton className='h-5 w-16 rounded-full' />
              <Skeleton className='h-5 w-14 rounded-full' />
              <Skeleton className='h-5 w-12 rounded-full' />
              <Skeleton className='h-4 w-16' />
            </div>
          ))}
        </CardContent>
      </Card>
    </div>
  );
}

// Compact skeleton for smaller spaces
export function FieldDay2025StatsCompactSkeleton() {
  return (
    <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
      {[1, 2, 3, 4].map((i) => (
        <Card key={i}>
          <CardContent className='p-4'>
            <div className='flex items-center justify-between mb-2'>
              <Skeleton className='h-3 w-16' />
              <Skeleton className='h-3 w-3 rounded' />
            </div>
            <Skeleton className='h-6 w-12 mb-1' />
            <Skeleton className='h-2 w-20' />
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

// Table-only skeleton
export function FieldDay2025TableSkeleton() {
  return (
    <Card>
      <CardHeader>
        <div className='flex justify-between items-center'>
          <div>
            <Skeleton className='h-6 w-32 mb-2' />
            <Skeleton className='h-4 w-24' />
          </div>
          <Skeleton className='h-9 w-20' />
        </div>
      </CardHeader>
      <CardContent>
        {/* Search and filters */}
        <div className='flex justify-between items-center mb-4'>
          <Skeleton className='h-9 w-64' />
          <div className='flex gap-2'>
            <Skeleton className='h-9 w-24' />
            <Skeleton className='h-9 w-20' />
          </div>
        </div>

        {/* Table */}
        <div className='space-y-3'>
          {/* Header */}
          <div className='grid grid-cols-8 gap-4 pb-2 border-b'>
            {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
              <Skeleton key={i} className='h-4 w-full' />
            ))}
          </div>

          {/* Rows */}
          {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
            <div
              key={i}
              className='grid grid-cols-8 gap-4 py-3 border-b last:border-b-0'
            >
              {[1, 2, 3, 4, 5, 6, 7, 8].map((j) => (
                <Skeleton key={j} className='h-4 w-full' />
              ))}
            </div>
          ))}
        </div>

        {/* Pagination */}
        <div className='flex justify-between items-center mt-4 pt-4 border-t'>
          <Skeleton className='h-4 w-32' />
          <div className='flex gap-2'>
            <Skeleton className='h-8 w-8' />
            <Skeleton className='h-8 w-8' />
            <Skeleton className='h-8 w-8' />
            <Skeleton className='h-8 w-8' />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
