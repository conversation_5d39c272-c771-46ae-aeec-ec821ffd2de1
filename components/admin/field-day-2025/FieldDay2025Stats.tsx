'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Package,
  DollarSign,
  Users,
  TrendingUp,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
} from 'lucide-react';
import { formatCurrency } from '@/lib/field-day-2025-config';

interface FieldDay2025StatsData {
  totalOrders: number;
  totalRevenue: number;
  totalQuantity: number;
  averageOrderValue: number;
  ordersByStatus: {
    pending: number;
    confirmed: number;
    ready: number;
    completed: number;
    cancelled: number;
  };
  paymentsByStatus: {
    pending: number;
    succeeded: number;
    failed: number;
    cancelled: number;
  };
  recentOrders: number; // Orders in last 24 hours
  conversionRate: number; // Percentage of completed orders
}

export function FieldDay2025Stats() {
  const [stats, setStats] = useState<FieldDay2025StatsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch stats from API
        const response = await fetch('/api/admin/field-day-2025/stats');

        if (!response.ok) {
          throw new Error('Failed to fetch statistics');
        }

        const data = await response.json();

        if (data.success) {
          setStats(data.data);
        } else {
          throw new Error(data.error || 'Failed to load statistics');
        }
      } catch (error) {
        console.error('Error fetching Field Day 2025 stats:', error);
        setError(
          error instanceof Error ? error.message : 'Failed to load statistics'
        );

        // Set mock data for development
        setStats({
          totalOrders: 0,
          totalRevenue: 0,
          totalQuantity: 0,
          averageOrderValue: 0,
          ordersByStatus: {
            pending: 0,
            confirmed: 0,
            ready: 0,
            completed: 0,
            cancelled: 0,
          },
          paymentsByStatus: {
            pending: 0,
            succeeded: 0,
            failed: 0,
            cancelled: 0,
          },
          recentOrders: 0,
          conversionRate: 0,
        });
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  if (loading) {
    return (
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
        {[1, 2, 3, 4].map((i) => (
          <Card key={i}>
            <CardHeader className='pb-2'>
              <div className='h-4 bg-gray-200 rounded animate-pulse'></div>
            </CardHeader>
            <CardContent>
              <div className='h-8 bg-gray-200 rounded animate-pulse mb-2'></div>
              <div className='h-3 bg-gray-100 rounded animate-pulse'></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error || !stats) {
    return (
      <Card className='border-red-200 bg-red-50'>
        <CardContent className='p-6'>
          <div className='flex items-center gap-2 text-red-600'>
            <AlertCircle className='h-5 w-5' />
            <span className='font-medium'>Failed to load statistics</span>
          </div>
          <p className='text-red-600 text-sm mt-1'>
            {error || 'Unable to fetch Field Day 2025 order statistics'}
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className='space-y-6'>
      {/* Main Statistics Cards */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Total Orders</CardTitle>
            <Package className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.totalOrders}</div>
            <p className='text-xs text-muted-foreground'>
              {stats.recentOrders} in last 24h
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Total Revenue</CardTitle>
            <DollarSign className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {formatCurrency(stats.totalRevenue)}
            </div>
            <p className='text-xs text-muted-foreground'>
              Avg: {formatCurrency(stats.averageOrderValue)} per order
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Total Boxes</CardTitle>
            <Users className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.totalQuantity}</div>
            <p className='text-xs text-muted-foreground'>
              Produce boxes ordered
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Completion Rate
            </CardTitle>
            <TrendingUp className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {stats.conversionRate.toFixed(1)}%
            </div>
            <p className='text-xs text-muted-foreground'>
              Orders completed successfully
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Order Status Breakdown */}
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
        <Card>
          <CardHeader>
            <CardTitle className='text-lg'>Order Status Breakdown</CardTitle>
          </CardHeader>
          <CardContent className='space-y-3'>
            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-2'>
                <Clock className='h-4 w-4 text-yellow-500' />
                <span className='text-sm'>Pending</span>
              </div>
              <div className='flex items-center gap-2'>
                <Badge variant='secondary'>
                  {stats.ordersByStatus.pending}
                </Badge>
              </div>
            </div>

            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-2'>
                <CheckCircle className='h-4 w-4 text-blue-500' />
                <span className='text-sm'>Confirmed</span>
              </div>
              <div className='flex items-center gap-2'>
                <Badge variant='secondary'>
                  {stats.ordersByStatus.confirmed}
                </Badge>
              </div>
            </div>

            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-2'>
                <Package className='h-4 w-4 text-green-500' />
                <span className='text-sm'>Ready for Pickup</span>
              </div>
              <div className='flex items-center gap-2'>
                <Badge variant='secondary'>{stats.ordersByStatus.ready}</Badge>
              </div>
            </div>

            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-2'>
                <CheckCircle className='h-4 w-4 text-green-600' />
                <span className='text-sm'>Completed</span>
              </div>
              <div className='flex items-center gap-2'>
                <Badge variant='secondary'>
                  {stats.ordersByStatus.completed}
                </Badge>
              </div>
            </div>

            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-2'>
                <XCircle className='h-4 w-4 text-red-500' />
                <span className='text-sm'>Cancelled</span>
              </div>
              <div className='flex items-center gap-2'>
                <Badge variant='secondary'>
                  {stats.ordersByStatus.cancelled}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className='text-lg'>Payment Status Breakdown</CardTitle>
          </CardHeader>
          <CardContent className='space-y-3'>
            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-2'>
                <Clock className='h-4 w-4 text-yellow-500' />
                <span className='text-sm'>Pending Payment</span>
              </div>
              <div className='flex items-center gap-2'>
                <Badge variant='secondary'>
                  {stats.paymentsByStatus.pending}
                </Badge>
              </div>
            </div>

            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-2'>
                <CheckCircle className='h-4 w-4 text-green-500' />
                <span className='text-sm'>Payment Successful</span>
              </div>
              <div className='flex items-center gap-2'>
                <Badge variant='secondary'>
                  {stats.paymentsByStatus.succeeded}
                </Badge>
              </div>
            </div>

            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-2'>
                <XCircle className='h-4 w-4 text-red-500' />
                <span className='text-sm'>Payment Failed</span>
              </div>
              <div className='flex items-center gap-2'>
                <Badge variant='secondary'>
                  {stats.paymentsByStatus.failed}
                </Badge>
              </div>
            </div>

            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-2'>
                <XCircle className='h-4 w-4 text-gray-500' />
                <span className='text-sm'>Payment Cancelled</span>
              </div>
              <div className='flex items-center gap-2'>
                <Badge variant='secondary'>
                  {stats.paymentsByStatus.cancelled}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
