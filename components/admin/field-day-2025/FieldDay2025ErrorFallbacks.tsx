'use client';

import { Button } from '@/components/ui/button';

interface ErrorFallbackProps {
  onRetry?: () => void;
}

export function FieldDay2025StatsErrorFallback({ onRetry }: ErrorFallbackProps) {
  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    } else {
      window.location.reload();
    }
  };

  return (
    <div className="text-center py-8">
      <h3 className="text-lg font-semibold text-gray-900 mb-2">
        Failed to load statistics
      </h3>
      <p className="text-gray-600 mb-4">
        There was an error loading the Field Day 2025 order statistics.
      </p>
      <Button onClick={handleRetry}>
        Try Again
      </Button>
    </div>
  );
}

export function FieldDay2025OrdersErrorFallback({ onRetry }: ErrorFallbackProps) {
  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    } else {
      window.location.reload();
    }
  };

  return (
    <div className="text-center py-12">
      <h3 className="text-lg font-semibold text-gray-900 mb-2">
        Failed to load orders
      </h3>
      <p className="text-gray-600 mb-4">
        There was an error loading the Field Day 2025 orders.
      </p>
      <Button onClick={handleRetry}>
        Reload Orders
      </Button>
    </div>
  );
}

export function FieldDay2025RefreshButton({ onRefresh }: { onRefresh?: () => void }) {
  const handleRefresh = () => {
    if (onRefresh) {
      onRefresh();
    } else {
      window.location.reload();
    }
  };

  return (
    <Button variant="outline" size="sm" onClick={handleRefresh}>
      <svg className="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
      </svg>
      Refresh
    </Button>
  );
}
