'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
//
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { User, Mail, Phone, Package, DollarSign, Save, X } from 'lucide-react';
import {
  adminOrderUpdateSchema,
  AdminOrderUpdateData,
  formatCurrency,
  ORDER_STATUSES,
  PAYMENT_STATUSES,
  PICKUP_STATUSES,
  getOrderStatusDisplay,
  getPaymentStatusDisplay,
  getPickupStatusDisplay,
} from '@/lib/field-day-2025-config';
import type { Tables } from '@/lib/supabase/types';

// Type alias for the Field Day 2025 order
type FieldDay2025Order = Tables<'field_day_2025_orders'>;
import { fieldDay2025OrderClientActions } from '@/lib/services/field-day-2025-orders-client';

interface FieldDay2025OrderDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order: FieldDay2025Order | null;
  mode: 'view' | 'edit';
  onUpdate?: () => void;
}

export function FieldDay2025OrderDialog({
  open,
  onOpenChange,
  order,
  mode,
  onUpdate,
}: FieldDay2025OrderDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<AdminOrderUpdateData>({
    resolver: zodResolver(adminOrderUpdateSchema),
    defaultValues: {
      orderStatus: undefined,
      paymentStatus: undefined,
      pickupStatus: undefined,
      adminNotes: '',
    },
  });

  // Update form when order changes
  useEffect(() => {
    if (order) {
      form.reset({
        orderStatus: order.order_status as any,
        paymentStatus: order.payment_status as any,
        pickupStatus: order.pickup_status as any,
        adminNotes: order.admin_notes || '',
      });
    }
  }, [order, form]);

  const onSubmit = async (data: AdminOrderUpdateData) => {
    if (!order) return;

    try {
      setIsSubmitting(true);

      await fieldDay2025OrderClientActions.updateOrder(order.id, data);

      onUpdate?.();
      onOpenChange(false);
    } catch (error) {
      console.error('Failed to update order:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!order) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-w-2xl max-h-[90vh] overflow-y-auto'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Package className='h-5 w-5' />
            {mode === 'edit' ? 'Edit Order' : 'Order Details'} -{' '}
            {order.order_number}
          </DialogTitle>
        </DialogHeader>

        <div className='space-y-6'>
          {/* Order Information */}
          <div className='grid grid-cols-2 gap-4'>
            <div>
              <Label className='text-sm font-medium text-gray-500'>
                Order Number
              </Label>
              <p className='font-mono text-sm'>{order.order_number}</p>
            </div>
            <div>
              <Label className='text-sm font-medium text-gray-500'>
                Order Date
              </Label>
              <p className='text-sm'>
                {order.created_at
                  ? new Date(order.created_at).toLocaleString()
                  : 'N/A'}
              </p>
            </div>
          </div>

          <Separator />

          {/* Customer Information */}
          <div className='space-y-4'>
            <h3 className='font-semibold flex items-center gap-2'>
              <User className='h-4 w-4' />
              Customer Information
            </h3>

            <div className='grid grid-cols-2 gap-4'>
              <div>
                <Label className='text-sm font-medium text-gray-500'>
                  Name
                </Label>
                <p className='text-sm'>
                  {order.first_name} {order.last_name}
                </p>
              </div>
              <div>
                <Label className='text-sm font-medium text-gray-500'>
                  Email
                </Label>
                <div className='flex items-center gap-2'>
                  <Mail className='h-3 w-3 text-gray-400' />
                  <p className='text-sm'>{order.email}</p>
                </div>
              </div>
            </div>

            <div>
              <Label className='text-sm font-medium text-gray-500'>Phone</Label>
              <div className='flex items-center gap-2'>
                <Phone className='h-3 w-3 text-gray-400' />
                <p className='text-sm'>{order.phone_number}</p>
              </div>
            </div>
          </div>

          <Separator />

          {/* Order Details */}
          <div className='space-y-4'>
            <h3 className='font-semibold flex items-center gap-2'>
              <Package className='h-4 w-4' />
              Order Details
            </h3>

            <div className='grid grid-cols-3 gap-4'>
              <div>
                <Label className='text-sm font-medium text-gray-500'>
                  Product
                </Label>
                <p className='text-sm'>{order.product_name}</p>
              </div>
              <div>
                <Label className='text-sm font-medium text-gray-500'>
                  Quantity
                </Label>
                <p className='text-sm'>{order.quantity} boxes</p>
              </div>
              <div>
                <Label className='text-sm font-medium text-gray-500'>
                  Unit Price
                </Label>
                <p className='text-sm'>
                  {formatCurrency(order.unit_price_cents)}
                </p>
              </div>
            </div>

            <div className='grid grid-cols-2 gap-4'>
              <div>
                <Label className='text-sm font-medium text-gray-500'>
                  Tax Amount
                </Label>
                <p className='text-sm'>
                  {formatCurrency(order.tax_amount_cents || 0)}
                </p>
              </div>
              <div>
                <Label className='text-sm font-medium text-gray-500'>
                  Total Amount
                </Label>
                <p className='text-sm font-semibold'>
                  {formatCurrency(order.total_amount_cents)}
                </p>
              </div>
            </div>
          </div>

          <Separator />

          {/* Status Information */}
          {mode === 'view' ? (
            <div className='space-y-4'>
              <h3 className='font-semibold'>Status Information</h3>

              <div className='grid grid-cols-3 gap-4'>
                <div>
                  <Label className='text-sm font-medium text-gray-500'>
                    Order Status
                  </Label>
                  <div className='mt-1'>
                    <Badge
                      variant={
                        getOrderStatusDisplay(order.order_status as any)
                          .color as any
                      }
                    >
                      {getOrderStatusDisplay(order.order_status as any).label}
                    </Badge>
                  </div>
                </div>
                <div>
                  <Label className='text-sm font-medium text-gray-500'>
                    Payment Status
                  </Label>
                  <div className='mt-1'>
                    <Badge
                      variant={
                        getPaymentStatusDisplay(order.payment_status as any)
                          .color as any
                      }
                    >
                      {
                        getPaymentStatusDisplay(order.payment_status as any)
                          .label
                      }
                    </Badge>
                  </div>
                </div>
                <div>
                  <Label className='text-sm font-medium text-gray-500'>
                    Pickup Status
                  </Label>
                  <div className='mt-1'>
                    <Badge
                      variant={
                        getPickupStatusDisplay(order.pickup_status as any)
                          .color as any
                      }
                    >
                      {getPickupStatusDisplay(order.pickup_status as any).label}
                    </Badge>
                  </div>
                </div>
              </div>

              {order.admin_notes && (
                <div>
                  <Label className='text-sm font-medium text-gray-500'>
                    Admin Notes
                  </Label>
                  <p className='text-sm mt-1 p-2 bg-gray-50 rounded'>
                    {order.admin_notes}
                  </p>
                </div>
              )}
            </div>
          ) : (
            <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
              <h3 className='font-semibold'>Update Status</h3>

              <div className='grid grid-cols-3 gap-4'>
                <div className='space-y-2'>
                  <Label htmlFor='orderStatus'>Order Status</Label>
                  <Select
                    value={form.watch('orderStatus')}
                    onValueChange={(value) =>
                      form.setValue('orderStatus', value as any)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select status' />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(ORDER_STATUSES).map(([key, value]) => (
                        <SelectItem key={key} value={value}>
                          {getOrderStatusDisplay(value).label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className='space-y-2'>
                  <Label htmlFor='paymentStatus'>Payment Status</Label>
                  <Select
                    value={form.watch('paymentStatus')}
                    onValueChange={(value) =>
                      form.setValue('paymentStatus', value as any)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select status' />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(PAYMENT_STATUSES).map(([key, value]) => (
                        <SelectItem key={key} value={value}>
                          {getPaymentStatusDisplay(value).label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className='space-y-2'>
                  <Label htmlFor='pickupStatus'>Pickup Status</Label>
                  <Select
                    value={form.watch('pickupStatus')}
                    onValueChange={(value) =>
                      form.setValue('pickupStatus', value as any)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select status' />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(PICKUP_STATUSES).map(([key, value]) => (
                        <SelectItem key={key} value={value}>
                          {getPickupStatusDisplay(value).label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='adminNotes'>Admin Notes</Label>
                <Textarea
                  id='adminNotes'
                  placeholder='Add notes about this order...'
                  {...form.register('adminNotes')}
                />
              </div>

              <div className='flex justify-end gap-2 pt-4'>
                <Button
                  type='button'
                  variant='outline'
                  onClick={() => onOpenChange(false)}
                >
                  <X className='mr-2 h-4 w-4' />
                  Cancel
                </Button>
                <Button type='submit' disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2'></div>
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className='mr-2 h-4 w-4' />
                      Save Changes
                    </>
                  )}
                </Button>
              </div>
            </form>
          )}

          {/* Payment Information */}
          {order.payment_timestamp && (
            <>
              <Separator />
              <div className='space-y-2'>
                <h3 className='font-semibold flex items-center gap-2'>
                  <DollarSign className='h-4 w-4' />
                  Payment Information
                </h3>
                <div className='grid grid-cols-2 gap-4'>
                  <div>
                    <Label className='text-sm font-medium text-gray-500'>
                      Payment Date
                    </Label>
                    <p className='text-sm'>
                      {new Date(order.payment_timestamp).toLocaleString()}
                    </p>
                  </div>
                  {order.payment_method && (
                    <div>
                      <Label className='text-sm font-medium text-gray-500'>
                        Payment Method
                      </Label>
                      <p className='text-sm capitalize'>
                        {order.payment_method}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </>
          )}

          {/* Timestamps */}
          <Separator />
          <div className='grid grid-cols-2 gap-4 text-xs text-gray-500'>
            <div>
              <Label className='text-xs font-medium text-gray-400'>
                Created
              </Label>
              <p>
                {order.created_at
                  ? new Date(order.created_at).toLocaleString()
                  : 'N/A'}
              </p>
            </div>
            <div>
              <Label className='text-xs font-medium text-gray-400'>
                Last Updated
              </Label>
              <p>
                {order.updated_at
                  ? new Date(order.updated_at).toLocaleString()
                  : 'N/A'}
              </p>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
