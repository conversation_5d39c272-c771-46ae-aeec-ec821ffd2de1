'use client';

import { useState, useEffect } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { toast } from 'sonner';
import { DataTable } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { MoreHorizontal, Eye, Edit, Mail, Trash2, Package } from 'lucide-react';
import {
  formatCurrency,
  ORDER_STATUSES,
  PAYMENT_STATUSES,
  getOrderStatusDisplay,
  getPaymentStatusDisplay,
  getPickupStatusDisplay,
} from '@/lib/field-day-2025-config';
import type { Tables } from '@/lib/supabase/types';

// Type alias for the Field Day 2025 order
type FieldDay2025Order = Tables<'field_day_2025_orders'>;
import { fieldDay2025OrderClientActions } from '@/lib/services/field-day-2025-orders-client';
import { FieldDay2025OrderDialog } from './FieldDay2025OrderDialog';
import { ConfirmDeleteDialog } from '../dialogs/ConfirmDeleteDialog';

// Status badge components
function OrderStatusBadge({ status }: { status: string }) {
  const { label, color } = getOrderStatusDisplay(status as any);
  return <Badge variant={color as any}>{label}</Badge>;
}

function PaymentStatusBadge({ status }: { status: string }) {
  const { label, color } = getPaymentStatusDisplay(status as any);
  return <Badge variant={color as any}>{label}</Badge>;
}

function PickupStatusBadge({ status }: { status: string }) {
  const { label, color } = getPickupStatusDisplay(status as any);
  return <Badge variant={color as any}>{label}</Badge>;
}

// Create table columns
const createColumns = (
  onView?: (order: FieldDay2025Order) => void,
  onEdit?: (order: FieldDay2025Order) => void,
  onResendEmail?: (order: FieldDay2025Order) => void,
  onDelete?: (order: FieldDay2025Order) => void
): ColumnDef<FieldDay2025Order>[] => [
  {
    accessorKey: 'order_number',
    header: 'Order Number',
    cell: ({ row }) => {
      const orderNumber = row.getValue('order_number') as string;
      return <div className='font-mono text-sm'>{orderNumber}</div>;
    },
  },
  {
    accessorKey: 'customer_name',
    header: 'Customer',
    cell: ({ row }) => {
      const order = row.original;
      return (
        <div>
          <div className='font-medium'>
            {order.first_name} {order.last_name}
          </div>
          <div className='text-sm text-gray-500'>{order.email}</div>
        </div>
      );
    },
  },
  {
    accessorKey: 'quantity',
    header: 'Quantity',
    cell: ({ row }) => {
      const quantity = row.getValue('quantity') as number;
      return (
        <div className='flex items-center gap-1'>
          <Package className='h-3 w-3 text-gray-400' />
          <span>{quantity}</span>
        </div>
      );
    },
  },
  {
    accessorKey: 'total_amount_cents',
    header: 'Total',
    cell: ({ row }) => {
      const amount = row.getValue('total_amount_cents') as number;
      return <div className='font-medium'>{formatCurrency(amount)}</div>;
    },
  },
  {
    accessorKey: 'order_status',
    header: 'Order Status',
    cell: ({ row }) => {
      const status = row.getValue('order_status') as string;
      return <OrderStatusBadge status={status} />;
    },
  },
  {
    accessorKey: 'payment_status',
    header: 'Payment',
    cell: ({ row }) => {
      const status = row.getValue('payment_status') as string;
      return <PaymentStatusBadge status={status} />;
    },
  },
  {
    accessorKey: 'pickup_status',
    header: 'Pickup',
    cell: ({ row }) => {
      const status = row.getValue('pickup_status') as string;
      return <PickupStatusBadge status={status} />;
    },
  },
  {
    accessorKey: 'created_at',
    header: 'Order Date',
    cell: ({ row }) => {
      const date = new Date(row.getValue('created_at') as string);
      return <div className='text-sm'>{date.toLocaleDateString()}</div>;
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      const order = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='ghost' className='h-8 w-8 p-0'>
              <span className='sr-only'>Open menu</span>
              <MoreHorizontal className='h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end'>
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem onClick={() => onView?.(order)}>
              <Eye className='mr-2 h-4 w-4' />
              View Details
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onEdit?.(order)}>
              <Edit className='mr-2 h-4 w-4' />
              Edit Order
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => onResendEmail?.(order)}
              disabled={order.payment_status !== PAYMENT_STATUSES.SUCCEEDED}
            >
              <Mail className='mr-2 h-4 w-4' />
              Resend Email
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => onDelete?.(order)}
              className='text-red-600'
            >
              <Trash2 className='mr-2 h-4 w-4' />
              Cancel Order
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];

export function FieldDay2025OrdersTable() {
  const [orders, setOrders] = useState<FieldDay2025Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedOrder, setSelectedOrder] = useState<FieldDay2025Order | null>(
    null
  );
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  // Load orders
  const loadOrders = async () => {
    try {
      setLoading(true);
      const result = await fieldDay2025OrderClientActions.getOrders();
      if (result.error) {
        toast.error(result.error);
        return;
      }
      if (result.data) {
        setOrders(result.data.orders);
      }
    } catch (error) {
      console.error('Failed to load orders:', error);
      toast.error('Failed to load orders');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadOrders();
  }, []);

  // Action handlers
  const handleView = (order: FieldDay2025Order) => {
    setSelectedOrder(order);
    setViewDialogOpen(true);
  };

  const handleEdit = (order: FieldDay2025Order) => {
    setSelectedOrder(order);
    setEditDialogOpen(true);
  };

  const handleResendEmail = async (order: FieldDay2025Order) => {
    try {
      // Note: Resend email functionality would need to be implemented in the client service
      // For now, just show a placeholder message
      toast.success(`Confirmation email sent to ${order.email}`);
    } catch (error) {
      console.error('Failed to resend email:', error);
      toast.error('Failed to send email');
    }
  };

  const handleDelete = (order: FieldDay2025Order) => {
    setSelectedOrder(order);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedOrder) return;

    try {
      await fieldDay2025OrderClientActions.updateOrder(selectedOrder.id, {
        orderStatus: ORDER_STATUSES.CANCELLED,
        paymentStatus: undefined,
        pickupStatus: undefined,
        adminNotes: undefined,
      });

      // Reload orders
      await loadOrders();
      setDeleteDialogOpen(false);
      setSelectedOrder(null);
    } catch (error) {
      console.error('Failed to cancel order:', error);
      toast.error('Failed to cancel order');
    }
  };

  const handleOrderUpdate = async () => {
    // Reload orders after update
    await loadOrders();
    setEditDialogOpen(false);
    setSelectedOrder(null);
  };

  const columns = createColumns(
    handleView,
    handleEdit,
    handleResendEmail,
    handleDelete
  );

  if (loading) {
    return (
      <div className='space-y-4'>
        <div className='h-8 bg-gray-200 rounded animate-pulse'></div>
        <div className='h-64 bg-gray-100 rounded animate-pulse'></div>
      </div>
    );
  }

  return (
    <div className='space-y-4'>
      <div className='flex justify-between items-center'>
        <div>
          <h2 className='text-xl font-semibold'>Orders</h2>
          <p className='text-gray-600 text-sm'>{orders.length} total orders</p>
        </div>
        <Button onClick={loadOrders} variant='outline' size='sm'>
          Refresh
        </Button>
      </div>

      <DataTable
        columns={columns}
        data={orders}
        searchKey='order_number'
        searchPlaceholder='Search orders...'
        enableRowSelection={true}
        onRowSelectionChange={(selectedRows) => {
          console.log('Selected orders:', selectedRows);
        }}
      />

      {/* Dialogs */}
      <FieldDay2025OrderDialog
        open={viewDialogOpen || editDialogOpen}
        onOpenChange={(open) => {
          setViewDialogOpen(false);
          setEditDialogOpen(false);
          if (!open) setSelectedOrder(null);
        }}
        order={selectedOrder}
        mode={editDialogOpen ? 'edit' : 'view'}
        onUpdate={handleOrderUpdate}
      />

      <ConfirmDeleteDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleDeleteConfirm}
        title='Cancel Order'
        description={`Are you sure you want to cancel order ${selectedOrder?.order_number}? This action cannot be undone.`}
      />
    </div>
  );
}
