'use client';

import { Button } from '@/components/ui/button';

interface AdminErrorFallbackProps {
  title?: string;
  message?: string;
  onRetry?: () => void;
}

export function AdminErrorFallback({ 
  title = 'Failed to load data',
  message = 'There was an error loading the requested information.',
  onRetry 
}: AdminErrorFallbackProps) {
  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    } else {
      window.location.reload();
    }
  };

  return (
    <div className='text-center py-12'>
      <h3 className='text-lg font-semibold text-gray-900 mb-2'>
        {title}
      </h3>
      <p className='text-gray-600 mb-4'>
        {message}
      </p>
      <Button onClick={handleRetry}>
        Try Again
      </Button>
    </div>
  );
}
