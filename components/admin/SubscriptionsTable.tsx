'use client';

import { useState, useEffect } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';
import { MoreHorizontal, Eye, Edit, Pause, Play, X, Truck } from 'lucide-react';

import { DataTable } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { SubscriptionWithSubscriber } from '@/lib/types/admin';
import { getSubscriptionsAction } from '@/lib/actions/admin/subscriptions';

const getStatusBadge = (status: string) => {
  switch (status) {
    case 'active':
      return <Badge className='bg-green-100 text-green-800'>Active</Badge>;
    case 'paused':
      return <Badge className='bg-yellow-100 text-yellow-800'>Paused</Badge>;
    case 'cancelled':
      return <Badge className='bg-red-100 text-red-800'>Cancelled</Badge>;
    case 'completed':
      return <Badge className='bg-gray-100 text-gray-800'>Completed</Badge>;
    default:
      return <Badge variant='secondary'>{status}</Badge>;
  }
};

const getPickupLocationName = (location: string) => {
  switch (location) {
    case 'elite_bodies':
      return 'Elite Bodies';
    case 'shabach_ministries':
      return 'Shabach Ministries';
    default:
      return location;
  }
};

const columns: ColumnDef<SubscriptionWithSubscriber>[] = [
  {
    accessorFn: (row) => row.subscribers?.name || '',
    id: 'subscriber_name',
    header: 'Subscriber Name',
    cell: ({ row }) => {
      const subscription = row.original;
      return (
        <div>
          <div className='font-medium'>
            {subscription.subscribers?.name || 'N/A'}
          </div>
          <div className='text-sm text-gray-500'>
            {subscription.subscribers?.email || 'N/A'}
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: 'box_size',
    header: 'Box Size',
    cell: ({ row }) => {
      const boxSize = row.getValue('box_size') as string;
      return (
        <Badge variant='outline'>
          {boxSize ? boxSize.charAt(0).toUpperCase() + boxSize.slice(1) : 'N/A'}
        </Badge>
      );
    },
  },
  {
    accessorKey: 'frequency',
    header: 'Frequency',
    cell: ({ row }) => {
      const frequency = row.getValue('frequency') as string;
      return frequency.charAt(0).toUpperCase() + frequency.slice(1);
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.getValue('status') as string;
      return getStatusBadge(status);
    },
  },
  {
    accessorKey: 'pickup_location',
    header: 'Pickup Location',
    cell: ({ row }) => {
      const location = row.getValue('pickup_location') as string;
      return getPickupLocationName(location);
    },
  },
  {
    accessorKey: 'deliveries_remaining',
    header: 'Deliveries Remaining',
    cell: ({ row }) => {
      const remaining = row.getValue('deliveries_remaining') as number;
      return (
        <div className='text-right'>
          <span className='font-medium'>{remaining || 0}</span>
        </div>
      );
    },
  },
  {
    accessorKey: 'next_delivery_date',
    header: 'Next Delivery',
    cell: ({ row }) => {
      const date = row.getValue('next_delivery_date') as string;
      return date ? format(new Date(date), 'MMM dd, yyyy') : '-';
    },
  },
  {
    accessorKey: 'base_price_cents',
    header: 'Monthly Price',
    cell: ({ row }) => {
      const priceCents = row.getValue('base_price_cents') as number;
      const discount = row.original.discount_percentage || 0;
      const finalPrice = priceCents * (1 - discount / 100);
      return (
        <div className='text-right'>
          <div className='font-medium'>${(finalPrice / 100).toFixed(2)}</div>
          {discount > 0 && (
            <div className='text-xs text-green-600'>-{discount}%</div>
          )}
        </div>
      );
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      const subscription = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='ghost' className='h-8 w-8 p-0'>
              <span className='sr-only'>Open menu</span>
              <MoreHorizontal className='h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end'>
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem>
              <Eye className='mr-2 h-4 w-4' />
              View details
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Edit className='mr-2 h-4 w-4' />
              Edit subscription
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              <Truck className='mr-2 h-4 w-4' />
              Process delivery
            </DropdownMenuItem>
            {subscription.status === 'active' ? (
              <DropdownMenuItem>
                <Pause className='mr-2 h-4 w-4' />
                Pause subscription
              </DropdownMenuItem>
            ) : subscription.status === 'paused' ? (
              <DropdownMenuItem>
                <Play className='mr-2 h-4 w-4' />
                Resume subscription
              </DropdownMenuItem>
            ) : null}
            <DropdownMenuSeparator />
            <DropdownMenuItem className='text-red-600'>
              <X className='mr-2 h-4 w-4' />
              Cancel subscription
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];

export function SubscriptionsTable() {
  const [data, setData] = useState<SubscriptionWithSubscriber[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        const result = await getSubscriptionsAction({
          page: 1,
          pageSize: 100,
          sortBy: 'created_at',
          sortOrder: 'desc',
        });

        if (!result.success || result.error) {
          setError(result.error || 'Failed to fetch subscriptions');
        } else if (result.data) {
          setData(result.data.data);
        }
      } catch (err) {
        setError('Failed to fetch subscriptions');
        console.error('Error fetching subscriptions:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <div className='flex items-center justify-center h-64'>
        <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-green-600'></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className='flex items-center justify-center h-64'>
        <div className='text-center'>
          <p className='text-red-600 mb-2'>Error loading subscriptions</p>
          <p className='text-sm text-gray-500'>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className='space-y-4'>
      <div>
        <h2 className='text-2xl font-bold'>Subscriptions</h2>
        <p className='text-gray-600'>
          Manage active subscription plans and settings
        </p>
      </div>

      <DataTable
        columns={columns}
        data={data}
        searchKey='subscriber_name'
        searchPlaceholder='Search subscriptions...'
        enableRowSelection={true}
        onRowSelectionChange={(selectedRows) => {
          console.log('Selected subscriptions:', selectedRows);
        }}
      />
    </div>
  );
}
