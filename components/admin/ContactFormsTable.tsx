'use client';

import { useState, useEffect } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';
import {
  MoreHorizontal,
  Eye,
  Mail,
  Check,
  AlertCircle,
  Trash2,
} from 'lucide-react';
import { toast } from 'sonner';

import { DataTable } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ContactSubmissionWithStatus } from '@/lib/services/admin/contact-forms';
import {
  getContactFormsAction,
  updateContactFormAction,
  deleteContactFormAction,
} from '@/lib/actions/admin/contact-forms';
import { ConfirmDeleteDialog } from './dialogs/ConfirmDeleteDialog';
import { ContactFormViewDialog } from './dialogs/ContactFormViewDialog';

const truncateText = (text: string, maxLength: number = 60) => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

const getStatusBadge = (status: string) => {
  switch (status) {
    case 'new':
      return <Badge className='bg-blue-100 text-blue-800'>New</Badge>;
    case 'read':
      return <Badge className='bg-yellow-100 text-yellow-800'>Read</Badge>;
    case 'responded':
      return <Badge className='bg-green-100 text-green-800'>Responded</Badge>;
    default:
      return <Badge variant='secondary'>{status}</Badge>;
  }
};

const createColumns = (
  onView?: (contact: ContactSubmissionWithStatus) => void,
  onMarkAsRead?: (contact: ContactSubmissionWithStatus) => void,
  onDelete?: (contact: ContactSubmissionWithStatus) => void
): ColumnDef<ContactSubmissionWithStatus>[] => [
  {
    accessorKey: 'name',
    header: 'Contact Name',
    cell: ({ row }) => {
      const contact = row.original;
      return (
        <div>
          <div className='font-medium'>{contact.name}</div>
          <div className='text-sm text-gray-500'>{contact.email}</div>
        </div>
      );
    },
  },
  {
    accessorKey: 'message',
    header: 'Message Preview',
    cell: ({ row }) => {
      const message = row.getValue('message') as string;
      return (
        <div className='max-w-md'>
          <p className='text-sm' title={message}>
            {truncateText(message)}
          </p>
        </div>
      );
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = (row.getValue('status') as string) || 'new';
      return getStatusBadge(status);
    },
  },
  {
    accessorKey: 'created_at',
    header: 'Date Submitted',
    cell: ({ row }) => {
      const date = row.getValue('created_at') as string;
      return (
        <div className='font-medium'>
          {format(new Date(date), 'MMM dd, yyyy')}
        </div>
      );
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      const contact = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='ghost' className='h-8 w-8 p-0'>
              <span className='sr-only'>Open menu</span>
              <MoreHorizontal className='h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end'>
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(contact.email)}
            >
              Copy email
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => onView?.(contact)}>
              <Eye className='mr-2 h-4 w-4' />
              View full message
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => window.open(`mailto:${contact.email}`, '_blank')}
            >
              <Mail className='mr-2 h-4 w-4' />
              Reply via email
            </DropdownMenuItem>
            {contact.status === 'new' && (
              <DropdownMenuItem onClick={() => onMarkAsRead?.(contact)}>
                <Check className='mr-2 h-4 w-4' />
                Mark as read
              </DropdownMenuItem>
            )}
            <DropdownMenuSeparator />
            <DropdownMenuItem
              className='text-red-600'
              onClick={() => onDelete?.(contact)}
            >
              <Trash2 className='mr-2 h-4 w-4' />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];

export function ContactFormsTable() {
  const [data, setData] = useState<ContactSubmissionWithStatus[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Dialog states
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedContact, setSelectedContact] =
    useState<ContactSubmissionWithStatus | null>(null);

  const fetchData = async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await getContactFormsAction(
        { page: 1, pageSize: 100 }, // Get first 100 items for now
        {} // No filters for now
      );

      if (!result.success || result.error) {
        setError(result.error || 'Failed to fetch contact forms');
      } else if (result.data) {
        setData(result.data.data);
      }
    } catch {
      setError('Failed to fetch contact forms');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  // CRUD Handlers
  const handleView = (contact: ContactSubmissionWithStatus) => {
    setSelectedContact(contact);
    setViewDialogOpen(true);
  };

  const handleMarkAsRead = async (contact: ContactSubmissionWithStatus) => {
    try {
      const result = await updateContactFormAction(contact.id, {
        status: 'read',
      });
      if (result.success) {
        toast.success('Contact form marked as read');
        fetchData();
      } else {
        toast.error(result.error || 'Failed to update contact form');
      }
    } catch {
      toast.error('An unexpected error occurred');
    }
  };

  const handleDelete = (contact: ContactSubmissionWithStatus) => {
    setSelectedContact(contact);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedContact) return;

    try {
      const result = await deleteContactFormAction(selectedContact.id);
      if (result.success) {
        toast.success('Contact form deleted successfully');
        fetchData();
      } else {
        toast.error(result.error || 'Failed to delete contact form');
      }
    } catch {
      toast.error('An unexpected error occurred');
    }
  };

  const columns = createColumns(handleView, handleMarkAsRead, handleDelete);

  if (loading) {
    return (
      <div className='flex items-center justify-center h-64'>
        <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-green-600'></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className='flex items-center justify-center h-64'>
        <div className='text-center'>
          <AlertCircle className='h-8 w-8 text-red-500 mx-auto mb-2' />
          <p className='text-red-600 font-medium'>
            Error loading contact forms
          </p>
          <p className='text-gray-500 text-sm'>{error}</p>
          <Button onClick={fetchData} className='mt-2'>
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className='space-y-4'>
      <div>
        <h2 className='text-2xl font-bold'>Contact Forms</h2>
        <p className='text-gray-600'>Manage customer inquiries and messages</p>
      </div>

      <DataTable
        columns={columns}
        data={data}
        searchKey='name'
        searchPlaceholder='Search contact forms...'
        enableRowSelection={true}
        onRowSelectionChange={(selectedRows) => {
          console.log('Selected contact forms:', selectedRows);
        }}
      />

      {/* Dialogs */}
      <ContactFormViewDialog
        open={viewDialogOpen}
        onOpenChange={setViewDialogOpen}
        contactForm={selectedContact}
        onMarkAsRead={handleMarkAsRead}
        onDelete={handleDelete}
      />

      <ConfirmDeleteDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title='Delete Contact Form'
        description='Are you sure you want to delete this contact form? This action cannot be undone.'
        itemName={selectedContact?.name}
        onConfirm={handleDeleteConfirm}
      />
    </div>
  );
}
