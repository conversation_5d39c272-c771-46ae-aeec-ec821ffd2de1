'use client';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { SubscriptionData } from '@/lib/constants/subscription';
import { stripeConfig } from '@/lib/stripe/client-config';
import {
  Elements,
  PaymentElement,
  useElements,
  useStripe,
} from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import { CreditCard, Loader2 } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';

// Initialize Stripe
const stripePromise = loadStripe(stripeConfig.publicKey);

interface StripePaymentFormProps {
  subscriptionData: SubscriptionData;
  subscriberId: string;
  onSuccess: (data: any) => void;
  onError: (error: string) => void;
  onPaymentStart?: () => void;
  isProcessing?: boolean;
}

interface PaymentFormProps {
  subscriptionData: SubscriptionData;
  subscriberId: string;
  onSuccess: (data: any) => void;
  onError: (error: string) => void;
  onPaymentStart?: () => void;
  isProcessing?: boolean;
}

// Payment form component that uses Stripe Elements
function PaymentForm({
  subscriptionData,
  subscriberId,
  onSuccess,
  onError,
  onPaymentStart,
  isProcessing: externalIsProcessing = false,
}: PaymentFormProps) {
  const stripe = useStripe();
  const elements = useElements();
  const [internalIsProcessing, setInternalIsProcessing] = useState(false);
  const [paymentError, setPaymentError] = useState<string | null>(null);

  // Use external processing state if provided, otherwise use internal state
  const isProcessing = externalIsProcessing || internalIsProcessing;

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements || isProcessing) {
      return;
    }

    // Call external payment start handler if provided
    if (onPaymentStart) {
      onPaymentStart();
    } else {
      setInternalIsProcessing(true);
    }

    setPaymentError(null);

    try {
      // Confirm payment with Stripe
      const { error: stripeError, paymentIntent } = await stripe.confirmPayment(
        {
          elements,
          redirect: 'if_required',
        }
      );

      if (stripeError) {
        const errorMessage = stripeError.message || 'Payment failed';
        setPaymentError(errorMessage);
        onError(errorMessage);
        // Only reset internal processing state if not using external state
        if (!onPaymentStart) {
          setInternalIsProcessing(false);
        }
        return;
      }

      if (paymentIntent && paymentIntent.status === 'succeeded') {
        // Confirm payment with our backend
        const response = await fetch('/api/payments/confirm', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            paymentIntentId: paymentIntent.id,
            subscriptionData,
            subscriberId,
          }),
        });

        if (!response.ok) {
          throw new Error(`Backend confirmation failed: ${response.status}`);
        }

        const result = await response.json();

        if (result.success) {
          onSuccess(result.data);
        } else {
          const errorMessage = result.error || 'Failed to create subscription';
          setPaymentError(errorMessage);
          onError(errorMessage);
        }
      } else {
        const errorMessage = 'Payment was not completed successfully';
        setPaymentError(errorMessage);
        onError(errorMessage);
      }
    } catch {
      const errorMessage = 'An unexpected error occurred during payment';
      setPaymentError(errorMessage);
      onError(errorMessage);
    } finally {
      // Only reset internal processing state if not using external state
      if (!onPaymentStart) {
        setInternalIsProcessing(false);
      }
    }
  };

  return (
    <form onSubmit={handleSubmit} className='space-y-6'>
      <div className='space-y-4'>
        <PaymentElement
          options={{
            layout: 'tabs',
            paymentMethodOrder: ['card'],
          }}
        />
      </div>

      {paymentError && (
        <Alert variant='destructive'>
          <AlertDescription>{paymentError}</AlertDescription>
        </Alert>
      )}

      <Button
        type='submit'
        disabled={!stripe || !elements || isProcessing}
        className='w-full bg-green-600 hover:bg-green-700 text-white py-3'
        size='lg'
      >
        {isProcessing ? (
          <>
            <Loader2 className='mr-2 h-4 w-4 animate-spin' />
            <span className='font-medium'>Processing Payment...</span>
          </>
        ) : (
          <>
            <CreditCard className='mr-2 h-4 w-4' />
            <span className='font-medium'>Complete Payment</span>
          </>
        )}
      </Button>
    </form>
  );
}

// Main component that handles payment intent creation
export default function StripePaymentForm({
  subscriptionData,
  subscriberId,
  onSuccess,
  onError,
  onPaymentStart,
  isProcessing,
}: StripePaymentFormProps) {
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const createPaymentIntent = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/payments/create-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subscriptionData,
          subscriberId,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const result = await response.json();

      if (result.success && result.data?.clientSecret) {
        setClientSecret(result.data.clientSecret);
      } else {
        const errorMessage =
          result.error ||
          'Failed to initialize payment - no client secret received';
        setError(errorMessage);
        onError(errorMessage);
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to initialize payment';
      setError(errorMessage);
      onError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [subscriptionData, subscriberId, onError]);

  useEffect(() => {
    createPaymentIntent();
  }, [createPaymentIntent]);

  if (isLoading) {
    return (
      <Card>
        <CardContent className='pt-6'>
          <div className='flex flex-col items-center justify-center py-8 space-y-4'>
            <Loader2 className='h-8 w-8 animate-spin text-green-600' />
            <div className='text-center'>
              <p className='text-gray-600 font-medium'>Preparing Payment</p>
              <p className='text-sm text-gray-500'>
                Setting up secure payment processing...
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className='pt-6'>
          <Alert variant='destructive'>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <Button
            onClick={createPaymentIntent}
            className='w-full mt-4'
            variant='outline'
          >
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (!clientSecret) {
    return (
      <Card>
        <CardContent className='pt-6'>
          <Alert>
            <AlertDescription>Payment initialization failed</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  const stripeOptions = {
    clientSecret,
    appearance: {
      theme: 'stripe' as const,
      variables: {
        colorPrimary: '#16a34a',
        colorBackground: '#ffffff',
        colorText: '#1f2937',
        colorDanger: '#dc2626',
        fontFamily: 'system-ui, sans-serif',
        spacingUnit: '4px',
        borderRadius: '6px',
      },
    },
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center'>
          <CreditCard className='mr-2 h-5 w-5' />
          Payment Information
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Elements stripe={stripePromise} options={stripeOptions}>
          <PaymentForm
            subscriptionData={subscriptionData}
            subscriberId={subscriberId}
            onSuccess={onSuccess}
            onError={onError}
            onPaymentStart={onPaymentStart}
            isProcessing={isProcessing}
          />
        </Elements>
      </CardContent>
    </Card>
  );
}
