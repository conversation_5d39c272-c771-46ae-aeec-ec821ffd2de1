# AsedaFoods Email Notification System Implementation

## Overview

This document outlines the implementation plan for the AsedaFoods email notification system. The system will handle all transactional and marketing emails for the CSA subscription platform.

## Implementation Status: ⏳ PENDING

## Directory Structure

```
lib/
├── emails/
│   ├── templates/
│   │   ├── auth/
│   │   │   └── welcome.tsx
│   │   ├── subscription/
│   │   │   ├── confirmation.tsx
│   │   │   ├── payment.tsx
│   │   │   ├── delivery-reminder.tsx
│   │   │   ├── delivery-processed.tsx
│   │   │   ├── renewal-reminder.tsx
│   │   │   ├── cancellation.tsx
│   │   │   └── pause.tsx
│   │   └── admin/
│   │       ├── new-subscription.tsx
│   │       ├── contact-form.tsx
│   │       └── newsletter-signup.tsx
│   ├── components/
│   │   ├── EmailLayout.tsx
│   │   ├── EmailButton.tsx
│   │   ├── EmailFooter.tsx
│   │   └── EmailHeader.tsx
│   └── index.ts
├── services/
│   └── email.ts
└── utils/
    └── email-utils.ts
```

## Technical Implementation

### 1. Email Service Integration

We'll use Resend as our email service provider, which integrates well with React Email templates.

```typescript
// lib/services/email.ts
import { Resend } from 'resend';
import { EmailTemplate } from '@/lib/emails/types';
import { logger } from '@/lib/utils/logger';

// Initialize Resend with API key from environment variables
const resend = new Resend(process.env.RESEND_API_KEY);

export class EmailService {
  static async sendEmail({
    to,
    subject,
    react,
    from = 'AsedaFoods <<EMAIL>>',
  }: {
    to: string | string[];
    subject: string;
    react: React.ReactNode;
    from?: string;
  }) {
    try {
      const { data, error } = await resend.emails.send({
        from,
        to,
        subject,
        react,
      });

      if (error) {
        logger.error('Failed to send email', { error, to, subject });
        return { success: false, error };
      }

      logger.info('Email sent successfully', { id: data?.id, to, subject });
      return { success: true, data };
    } catch (error) {
      logger.error('Exception when sending email', { error, to, subject });
      return { success: false, error };
    }
  }

  // Helper methods for specific email types
  static async sendWelcomeEmail(to: string, name: string) {
    const { WelcomeEmail } = await import(
      '@/lib/emails/templates/auth/welcome'
    );
    return this.sendEmail({
      to,
      subject: 'Welcome to AsedaFoods CSA! Your Account is Ready',
      react: WelcomeEmail({ name }),
    });
  }

  static async sendSubscriptionConfirmation(to: string, subscriptionData: any) {
    const { SubscriptionConfirmation } = await import(
      '@/lib/emails/templates/subscription/confirmation'
    );
    return this.sendEmail({
      to,
      subject: 'Your AsedaFoods CSA Subscription is Confirmed!',
      react: SubscriptionConfirmation({ subscriptionData }),
    });
  }

  // Additional helper methods for each email type...
}
```

### 2. Email Template Components

We'll create reusable components for consistent email styling:

```typescript
// lib/emails/components/EmailLayout.tsx
import {
  Html,
  Body,
  Container,
  Section,
  Head,
  Preview
} from '@react-email/components';
import { EmailHeader } from './EmailHeader';
import { EmailFooter } from './EmailFooter';

export interface EmailLayoutProps {
  children: React.ReactNode;
  preview: string;
}

export function EmailLayout({ children, preview }: EmailLayoutProps) {
  return (
    <Html>
      <Head />
      <Preview>{preview}</Preview>
      <Body style={{
        backgroundColor: '#f6f9fc',
        fontFamily: 'Arial, sans-serif',
        margin: '0',
      }}>
        <Container style={{
          maxWidth: '600px',
          margin: '0 auto',
          backgroundColor: '#ffffff',
          borderRadius: '8px',
          overflow: 'hidden',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        }}>
          <EmailHeader />
          <Section style={{ padding: '20px 30px' }}>
            {children}
          </Section>
          <EmailFooter />
        </Container>
      </Body>
    </Html>
  );
}
```

### 3. Sample Email Template Implementation

Here's an example of the subscription confirmation email:

```typescript
// lib/emails/templates/subscription/confirmation.tsx
import {
  Text,
  Section,
  Row,
  Column,
} from '@react-email/components';
import { EmailLayout } from '../../components/EmailLayout';
import { EmailButton } from '../../components/EmailButton';
import { formatPrice, formatDate } from '@/lib/utils/format';

interface SubscriptionConfirmationProps {
  subscriptionData: {
    boxSize: string;
    frequency: string;
    paymentPlan: string;
    pickupLocation: string;
    nextDeliveryDate: Date;
    totalPrice: number;
  };
  name: string;
}

export function SubscriptionConfirmation({
  subscriptionData,
  name
}: SubscriptionConfirmationProps) {
  const {
    boxSize,
    frequency,
    paymentPlan,
    pickupLocation,
    nextDeliveryDate,
    totalPrice,
  } = subscriptionData;

  return (
    <EmailLayout preview="Your AsedaFoods CSA subscription has been confirmed!">
      <Text style={{ fontSize: '20px', fontWeight: 'bold', color: '#333' }}>
        Thank you for your subscription, {name}!
      </Text>

      <Text style={{ fontSize: '16px', color: '#666', lineHeight: '24px' }}>
        We're excited to have you join our community of local food supporters. Your first box will be ready for pickup soon.
      </Text>

      <Section style={{
        backgroundColor: '#f7f9fa',
        padding: '20px',
        borderRadius: '8px',
        margin: '20px 0'
      }}>
        <Text style={{ fontSize: '18px', fontWeight: 'bold', color: '#333', marginBottom: '15px' }}>
          Subscription Details
        </Text>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', color: '#666', margin: '0' }}>Box Size:</Text>
          </Column>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', fontWeight: 'bold', color: '#333', margin: '0' }}>{boxSize}</Text>
          </Column>
        </Row>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', color: '#666', margin: '0' }}>Frequency:</Text>
          </Column>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', fontWeight: 'bold', color: '#333', margin: '0' }}>{frequency}</Text>
          </Column>
        </Row>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', color: '#666', margin: '0' }}>Payment Plan:</Text>
          </Column>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', fontWeight: 'bold', color: '#333', margin: '0' }}>{paymentPlan}</Text>
          </Column>
        </Row>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', color: '#666', margin: '0' }}>Pickup Location:</Text>
          </Column>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', fontWeight: 'bold', color: '#333', margin: '0' }}>{pickupLocation}</Text>
          </Column>
        </Row>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', color: '#666', margin: '0' }}>Next Delivery:</Text>
          </Column>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', fontWeight: 'bold', color: '#333', margin: '0' }}>{formatDate(nextDeliveryDate)}</Text>
          </Column>
        </Row>

        <Row>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', color: '#666', margin: '0' }}>Total Paid:</Text>
          </Column>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', fontWeight: 'bold', color: '#333', margin: '0' }}>{formatPrice(totalPrice)}</Text>
          </Column>
        </Row>
      </Section>

      <Text style={{ fontSize: '16px', color: '#666', lineHeight: '24px' }}>
        You can view and manage your subscription anytime from your dashboard.
      </Text>

      <EmailButton href="https://asedafoods.org/dashboard" text="View Your Dashboard" />

      <Text style={{ fontSize: '14px', color: '#666', marginTop: '20px' }}>
        If you have any questions about your subscription, please don't hesitate to contact us.
      </Text>
    </EmailLayout>
  );
}
```

### 4. Integration with Existing Workflows

#### Subscription Confirmation Email

```typescript
// app/(web)/get-a-box/checkout/page.tsx (modified)
import { EmailService } from '@/lib/services/email';

// Inside the handlePaymentSuccess function
const handlePaymentSuccess = async (data: any) => {
  setPaymentError(null);
  setPaymentSuccess(true, data);

  // Send confirmation email
  await EmailService.sendSubscriptionConfirmation(user.email, {
    ...subscriptionData,
    nextDeliveryDate: getNextDeliveryDate(subscriptionData.frequency!),
    totalPrice: pricing.totalPrice,
  });
};
```

#### Admin Notification for New Subscriptions

```typescript
// lib/services/subscription.ts (modified)
import { EmailService } from '@/lib/services/email';

// Inside the createSubscription method
async createSubscription(subscriberId: string, data: SubscriptionData) {
  // Existing code to create subscription...

  // Send confirmation email to subscriber
  const { data: subscriber } = await this.supabase
    .from('subscribers')
    .select('email, first_name')
    .eq('id', subscriberId)
    .single();

  if (subscriber) {
    await EmailService.sendSubscriptionConfirmation(
      subscriber.email,
      {
        ...data,
        nextDeliveryDate: getNextDeliveryDate(data.frequency),
        totalPrice: calculatePrice(data.boxSize, data.frequency, data.paymentPlan).totalPrice,
      }
    );

    // Send notification to admin
    await EmailService.sendNewSubscriptionAlert(
      process.env.ADMIN_EMAIL!,
      {
        subscriberId,
        subscriberName: subscriber.first_name,
        subscriptionData: data,
      }
    );
  }

  return { data: result.data, error: result.error };
}
```

## 5. Email Sending Triggers

| Email Type                | Trigger Point                          | Implementation Location                                   |
| ------------------------- | -------------------------------------- | --------------------------------------------------------- |
| Welcome                   | After email verification               | `lib/hooks/useAuth.ts`                                    |
| Subscription Confirmation | After successful payment               | `app/(web)/get-a-box/checkout/page.tsx`                   |
| Payment Confirmation      | After successful payment               | `app/(web)/get-a-box/checkout/page.tsx`                   |
| Delivery Reminder         | Scheduled job (3 days before delivery) | `lib/jobs/delivery-reminders.ts`                          |
| Delivery Processed        | Admin marks delivery as processed      | `app/(admin)/admin/deliveries/[id]/page.tsx`              |
| Renewal Reminder          | Scheduled job (7 days before renewal)  | `lib/jobs/renewal-reminders.ts`                           |
| Cancellation Confirmation | User cancels subscription              | `app/(user)/dashboard/subscriptions/[id]/cancel/page.tsx` |
| Pause Confirmation        | User pauses subscription               | `app/(user)/dashboard/subscriptions/[id]/pause/page.tsx`  |
| Admin Notifications       | Various user actions                   | Respective service methods                                |

## 6. Deployment Considerations

1. Set up proper environment variables for production
2. Configure email sending domain and DNS records
3. Implement email sending rate limiting
4. Set up email analytics and tracking
5. Create monitoring for failed email sends

## Newsletter Subscription Integration

### Automatic Audience Management

When users subscribe to the newsletter through the homepage form, they should be automatically added to the "general audience" list in Resend for future email campaigns. This ensures seamless integration between the website subscription form and email marketing campaigns.

**Implementation Requirements:**

- Newsletter subscriptions captured through the homepage form are stored in the `newsletter_subscriptions` table
- Each new subscription should trigger an API call to Resend to add the email to the "general audience" list
- Handle duplicate email addresses gracefully (both in database and Resend)
- Provide unsubscribe functionality that removes emails from both the database and Resend audience

## Next Steps

1. Install required dependencies (Resend, React Email)
2. Implement email service and base components
3. Create email templates in priority order
4. Integrate with existing workflows
5. Set up scheduled jobs for time-based emails
6. Implement Resend audience management for newsletter subscriptions
7. Test and deploy
