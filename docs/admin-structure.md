# Admin Route Structure

## New Centralized Structure

### Components Location: `components/admin/`
- `AdminCharts.tsx` - Dashboard analytics charts
- `SubscribersTable.tsx` - Subscribers data table
- `SubscriptionsTable.tsx` - Subscriptions data table  
- `BoxContentsTable.tsx` - Box contents data table
- `BoxContentForm.tsx` - Box content creation/editing form
- `ContactFormsTable.tsx` - Contact forms data table
- `index.ts` - Centralized exports

### Actions Location: `lib/actions/admin/`
- `charts.ts` - Chart data server actions
- `subscribers.ts` - Subscriber management actions
- `subscriptions.ts` - Subscription management actions
- `box-contents.ts` - Box content management actions
- `contact-forms.ts` - Contact form management actions
- `index.ts` - Centralized exports

### Admin Pages: `app/(admin)/admin/`
- `page.tsx` - Main admin dashboard
- `subscribers/page.tsx` - Subscribers management
- `subscriptions/page.tsx` - Subscriptions management
- `box-contents/page.tsx` - Box contents management
- `box-contents/create/page.tsx` - Create box content
- `contact-forms/page.tsx` - Contact forms management

## Import Patterns

### Components
```typescript
import { AdminCharts, SubscribersTable } from '@/components/admin';
```

### Actions
```typescript
import { getChartDataAction } from '@/lib/actions/admin/charts';
import { getSubscribersAction } from '@/lib/actions/admin/subscribers';
```

## Benefits
- Centralized admin components for better maintainability
- Consistent import patterns across the application
- Easier to locate and modify admin functionality
- Cleaner project structure with logical grouping
- Improved code reusability and organization
